# 前端开发重要提醒事项

## 🚨 缓存问题 - 每次修改前端代码后必须执行

### 正确的访问地址
- ✅ **正确**: `http://127.0.0.1:8080/`
- ✅ **带版本参数**: `http://127.0.0.1:8080/?v=100` (推荐)
- ❌ **错误**: `http://127.0.0.1:8080/web/` (会访问错误路径)

### 强制刷新缓存的方法
1. **使用版本参数** (推荐)
   ```
   http://127.0.0.1:8080/?v=101
   http://127.0.0.1:8080/?v=102
   ```
   每次修改代码后递增版本号

2. **键盘快捷键**
   - `Ctrl + F5` - 强制刷新
   - `Ctrl + Shift + R` - 硬性重新加载

3. **开发者工具方法**
   - 按 `F12` 打开开发者工具
   - 右键点击刷新按钮
   - 选择"清空缓存并硬性重新加载"

### 为什么会有缓存问题？
- 浏览器会缓存 HTML、CSS、JavaScript 文件
- 修改代码后，浏览器可能仍然使用旧的缓存文件
- 导致新功能不生效，看起来像"代码倒退"

## 🔧 开发流程检查清单

### 每次修改前端代码后：
- [ ] 保存文件
- [ ] 使用版本参数访问: `http://127.0.0.1:8080/?v=新版本号`
- [ ] 或者按 `Ctrl + F5` 强制刷新
- [ ] 验证修改是否生效
- [ ] 测试相关功能

### 常见问题排查：
1. **功能不生效** → 检查是否刷新了缓存
2. **显示旧版本** → 使用版本参数或强制刷新
3. **样式错乱** → 清除缓存重新加载
4. **JavaScript错误** → 确认加载的是最新版本

## 📝 API服务器配置说明

当前API服务器配置：
```python
app.mount("/", StaticFiles(directory=str(web_dir), html=True), name="web")
```

这意味着：
- 根路径 `/` 直接映射到 `web/` 目录
- 访问 `http://127.0.0.1:8080/` 实际加载 `web/index.html`
- 访问 `http://127.0.0.1:8080/web/` 是错误的路径

## 🎯 最佳实践

1. **开发时始终使用版本参数**
2. **每次修改后立即验证**
3. **遇到问题首先检查缓存**
4. **使用浏览器开发者工具监控网络请求**

---
**重要**: 这个文件的存在就是为了提醒开发者注意缓存问题！
