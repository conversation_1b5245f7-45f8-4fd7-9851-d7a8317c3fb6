#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite 连接复用器（轻量连接池）
- 每个 db_path 维护一个固定上限的连接池
- 返回的连接是包装对象，调用 close() 不会真正关闭，而是归还到池中
- 统一设置 PRAGMA: WAL、busy_timeout、synchronous、foreign_keys
- 线程安全，可在多线程中使用（check_same_thread=False）

用法：
from db.connection_manager import get_connection_manager
conn = get_connection_manager('db/shipment_records.db').acquire()
try:
    cur = conn.cursor()
    ...
finally:
    conn.close()  # 实际归还
"""

import sqlite3
import threading
from queue import Queue
from typing import Dict

_DEFAULT_MAXSIZE = 4

class _ConnectionWrapper:
    """包装 sqlite3.Connection，使 close() 归还到池中"""
    def __init__(self, conn: sqlite3.Connection, pool: "ConnectionManager"):
        self._conn = conn
        self._pool = pool

    # 常用属性透传
    @property
    def row_factory(self):
        return self._conn.row_factory

    @row_factory.setter
    def row_factory(self, val):
        self._conn.row_factory = val

    def cursor(self):
        return self._conn.cursor()

    def commit(self):
        return self._conn.commit()

    def rollback(self):
        return self._conn.rollback()

    def execute(self, *args, **kwargs):
        return self._conn.execute(*args, **kwargs)

    def close(self):
        # 归还到池中，而不是关闭
        self._pool.release(self._conn)

    # 兜底：其它访问统一透传
    def __getattr__(self, item):
        return getattr(self._conn, item)

class ConnectionManager:
    def __init__(self, db_path: str, maxsize: int = _DEFAULT_MAXSIZE):
        self.db_path = db_path
        self.maxsize = maxsize
        self._pool: Queue[sqlite3.Connection] = Queue(maxsize)
        self._lock = threading.Lock()
        self._created = 0

    def _create_connection(self) -> sqlite3.Connection:
        conn = sqlite3.connect(self.db_path, timeout=30.0, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        # 统一 PRAGMA，容错执行
        for pragma in (
            "PRAGMA journal_mode=WAL;",
            "PRAGMA busy_timeout = 10000;",
            "PRAGMA synchronous = NORMAL;",
            "PRAGMA foreign_keys = ON;",
        ):
            try:
                conn.execute(pragma)
            except Exception:
                pass
        return conn

    def stats(self) -> dict:
        """返回池状态统计信息"""
        available = self._pool.qsize()
        return {
            "db_path": self.db_path,
            "maxsize": self.maxsize,
            "created": self._created,
            "available": available,
            "in_use": max(self._created - available, 0),
        }

    def acquire(self) -> _ConnectionWrapper:
        try:
            conn = self._pool.get_nowait()
        except Exception:
            with self._lock:
                if self._created < self.maxsize:
                    conn = self._create_connection()
                    self._created += 1
                else:
                    # 等待可用连接
                    conn = self._pool.get()
        return _ConnectionWrapper(conn, self)

    def release(self, conn: sqlite3.Connection):
        try:
            # 尽量回滚未提交事务，保证连接干净
            conn.rollback()
        except Exception:
            pass
        try:
            self._pool.put_nowait(conn)
        except Exception:
            # 池已满（极少数场景），安全关闭
            try:
                conn.close()
            except Exception:
                pass

# 全局单例管理
_managers: Dict[str, ConnectionManager] = {}
_managers_lock = threading.Lock()

def get_connection_manager(db_path: str, maxsize: int = _DEFAULT_MAXSIZE) -> ConnectionManager:
    with _managers_lock:
        m = _managers.get(db_path)
        if not m:
            m = ConnectionManager(db_path, maxsize=maxsize)
            _managers[db_path] = m
        return m


def get_pool_stats(db_path: str) -> dict:
    """获取指定数据库的连接池状态"""
    m = get_connection_manager(db_path)
    return m.stats()

