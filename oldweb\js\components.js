// UI组件管理器
class ComponentManager {
    constructor() {
        this.notifications = [];
    }
    
    // 显示通知
    showNotification(type, title, message, duration = 5000) {
        const notification = {
            id: Date.now().toString(),
            type,
            title,
            message,
            timestamp: new Date().toLocaleTimeString('zh-CN')
        };
        
        this.notifications.unshift(notification);
        this.renderNotification(notification);
        
        // 自动移除通知
        setTimeout(() => {
            this.removeNotification(notification.id);
        }, duration);
    }
    
    // 渲染通知
    renderNotification(notification) {
        const container = this.getNotificationContainer();
        
        const notificationEl = document.createElement('div');
        notificationEl.className = `notification notification-${notification.type}`;
        notificationEl.id = `notification-${notification.id}`;
        
        const iconMap = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'alert-triangle',
            info: 'info'
        };
        
        notificationEl.innerHTML = `
            <div class="notification-content">
                <div class="notification-header">
                    <i data-feather="${iconMap[notification.type] || 'info'}"></i>
                    <strong>${notification.title}</strong>
                    <button class="notification-close" onclick="app.components.removeNotification('${notification.id}')">
                        <i data-feather="x"></i>
                    </button>
                </div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${notification.timestamp}</div>
            </div>
        `;
        
        container.appendChild(notificationEl);
        
        // 初始化图标
        if (window.feather) {
            feather.replace();
        }
        
        // 添加进入动画
        setTimeout(() => {
            notificationEl.classList.add('notification-show');
        }, 10);
    }
    
    // 移除通知
    removeNotification(id) {
        const notificationEl = document.getElementById(`notification-${id}`);
        if (notificationEl) {
            notificationEl.classList.add('notification-hide');
            setTimeout(() => {
                notificationEl.remove();
            }, 300);
        }
        
        this.notifications = this.notifications.filter(n => n.id !== id);
    }
    
    // 获取或创建通知容器
    getNotificationContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        return container;
    }
    
    // 显示确认对话框
    showConfirm(title, message, onConfirm, onCancel) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.innerHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>${title}</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="modal-cancel">
                        <i data-feather="x"></i>
                        取消
                    </button>
                    <button class="btn btn-primary" id="modal-confirm">
                        <i data-feather="check"></i>
                        确认
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // 绑定事件
        document.getElementById('modal-cancel').onclick = () => {
            overlay.remove();
            if (onCancel) onCancel();
        };
        
        document.getElementById('modal-confirm').onclick = () => {
            overlay.remove();
            if (onConfirm) onConfirm();
        };
        
        // 点击遮罩关闭
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                overlay.remove();
                if (onCancel) onCancel();
            }
        };
        
        // 初始化图标
        if (window.feather) {
            feather.replace();
        }
    }
    
    // 显示加载状态
    showLoading(message = '加载中...') {
        const existing = document.getElementById('loading-overlay');
        if (existing) return;
        
        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">${message}</div>
            </div>
        `;
        
        document.body.appendChild(overlay);
    }
    
    // 隐藏加载状态
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
    
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes);
    }
    
    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

// 导出组件管理器
window.ComponentManager = ComponentManager;