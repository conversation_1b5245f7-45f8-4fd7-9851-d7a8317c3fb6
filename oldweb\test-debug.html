<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Debug Test</title>
</head>
<body>
    <h1>Debug Test</h1>
    <div id="output"></div>
    
    <script>
        console.log('Starting debug test...');
        document.getElementById('output').innerHTML = 'Basic HTML and JS working';
    </script>
    
    <script>
        console.log('Loading router...');
    </script>
    <script src="js/router.js"></script>
    
    <script>
        console.log('Router loaded, testing...');
        try {
            const router = new Router();
            console.log('Router created successfully');
            document.getElementById('output').innerHTML += '<br>Router: OK';
        } catch (error) {
            console.error('Router error:', error);
            document.getElementById('output').innerHTML += '<br>Router: ERROR - ' + error.message;
        }
    </script>
    
    <script>
        console.log('Loading components...');
    </script>
    <script src="js/components.js"></script>
    
    <script>
        console.log('Components loaded, testing...');
        try {
            const components = new ComponentManager();
            console.log('ComponentManager created successfully');
            document.getElementById('output').innerHTML += '<br>Components: OK';
        } catch (error) {
            console.error('Components error:', error);
            document.getElementById('output').innerHTML += '<br>Components: ERROR - ' + error.message;
        }
    </script>
    
    <script>
        console.log('Loading data...');
    </script>
    <script src="js/data.js"></script>
    
    <script>
        console.log('Data loaded, testing...');
        try {
            const dataManager = new DataManager();
            console.log('DataManager created successfully');
            document.getElementById('output').innerHTML += '<br>Data: OK';
        } catch (error) {
            console.error('Data error:', error);
            document.getElementById('output').innerHTML += '<br>Data: ERROR - ' + error.message;
        }
    </script>
    
    <script>
        console.log('Loading app...');
    </script>
    <script src="js/app.js"></script>
    
    <script>
        console.log('App loaded, testing...');
        try {
            const app = new ShipmentApp();
            console.log('ShipmentApp created successfully');
            document.getElementById('output').innerHTML += '<br>App: OK';
        } catch (error) {
            console.error('App error:', error);
            document.getElementById('output').innerHTML += '<br>App: ERROR - ' + error.message;
        }
    </script>
</body>
</html>