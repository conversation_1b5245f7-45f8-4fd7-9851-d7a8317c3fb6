#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查任务队列数据库的实际状态
"""

import sqlite3
import os

def check_task_status():
    """检查任务状态"""
    if not os.path.exists('db/task_queue.db'):
        print("❌ 数据库不存在")
        return
    
    conn = sqlite3.connect('db/task_queue.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("🔍 检查最近的任务状态...")
    
    # 查看所有跟踪号及其任务状态
    cursor.execute("""
        SELECT tracking_number, task_stage, status, created_at, id
        FROM task_queue 
        ORDER BY tracking_number, created_at DESC
        LIMIT 20
    """)
    
    results = cursor.fetchall()
    current_tracking = None
    
    for row in results:
        tracking_number = row['tracking_number']
        if tracking_number != current_tracking:
            if current_tracking is not None:
                print("  ---")
            print(f"\n📋 跟踪号: {tracking_number}")
            current_tracking = tracking_number
        
        print(f"    {row['task_stage']:15} {row['status']:15} {row['created_at']} ({row['id'][:8]})")
    
    conn.close()

if __name__ == "__main__":
    check_task_status()