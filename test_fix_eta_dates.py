#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复：AI分析回调先处理数据再同步状态，确保ETA与时间节点写入数据库。
"""
from shipment_manager import ShipmentManager
from task_manager import TaskManager
from datetime import datetime

def run():
    sm = ShipmentManager()
    tm = TaskManager()

    # 1) 创建一条货运记录
    record_id = sm.create_shipment_record(
        bill_of_lading="TEST_BL_FIX_001",
        carrier_company="MSC",
        created_by="unit_test"
    )
    print(f"Created shipment record id={record_id}")

    # 2) 创建一条 AI 分析阶段的任务，并把货运记录ID放入备注（模拟真实环境）
    task_id = tm.create_task(
        tracking_number="TEST_BL_FIX_001",
        task_type="bill_of_lading",
        creator_id="unit_test",
        remarks=f"货运记录ID: {record_id}",
        task_stage='ai_analysis'
    )
    print(f"Created AI task id={task_id}")

    # 标记任务为已完成（handle_task_completion 会读取任务以确定阶段和备注）
    tm.update_task_status(task_id, "completed")

    # 3) 模拟 AI 返回结果
    result_data = {
        'estimated_arrival_time': '2025-08-27',
        'dates_data': [
            {
                'date': '2025-08-27',
                'location': 'Shanghai, CN',
                'event': 'POD ETA',
                'status': 'estimated',
                'event_type': 'POD_ETA',
                'vessel_info': ''
            }
        ],
        'result_files': {}
    }

    # 4) 触发回调处理
    ok = sm.handle_task_completion(task_id, result_data)
    print(f"handle_task_completion: {ok}")

    # 5) 读取并打印验证
    rec = sm.get_shipment_record(record_id)
    print("record:", rec)

    # 统计 dates 数量
    import sqlite3
    conn = sqlite3.connect('db/shipment_records.db')
    cur = conn.cursor()
    cur.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id=?", (record_id,))
    cnt = cur.fetchone()[0]
    conn.close()
    print("dates count:", cnt)

if __name__ == '__main__':
    run()

