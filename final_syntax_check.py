#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终语法和完整性检查
"""

def final_syntax_check():
    """最终语法检查"""
    print("🔍 进行最终语法和完整性检查...")
    
    try:
        # 语法检查
        import ast
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        print("✅ 语法检查通过")
        
        # 检查关键结构
        checks = [
            ("主函数存在", "def main():" in content),
            ("ContainerHelperApp类存在", "class ContainerHelperApp(" in content),
            ("所有必需导入", "from PySide6.QtWidgets import" in content),
            ("main调用存在", 'if __name__ == "__main__":' in content),
            ("TaskDetailDialog存在", "class TaskDetailDialog(" in content),
            ("两阶段功能", "网页抓取" in content and "AI分析" in content),
        ]
        
        all_passed = True
        for name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {name}: {'通过' if result else '失败'}")
            if not result:
                all_passed = False
        
        # 检查关键方法
        key_methods = [
            'open_carrier_management',
            'handle_task_completion',
            'get_task_stages_status', 
            'create_status_item',
            'handle_status_click',
            'refresh_shipments_list',
            'load_shipments',
            'on_search_changed',
            'show_shipment_details',
            'delete_selected_shipment',
            'toggle_task_processor',
            'show_about'
        ]
        
        missing_methods = []
        for method in key_methods:
            if f"def {method}(self" not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"\n❌ 缺失关键方法: {missing_methods}")
            all_passed = False
        else:
            print(f"\n✅ 所有 {len(key_methods)} 个关键方法都存在")
        
        if all_passed:
            print("\n🎉 所有检查通过！app.py 应该可以正常运行")
            return True
        else:
            print("\n❌ 仍有问题需要解决")
            return False
            
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    success = final_syntax_check()
    if success:
        print("\n🚀 现在可以运行: python app.py")
    else:
        print("\n🔧 请先修复上述问题再运行")