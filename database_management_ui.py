import sys
import os
import sqlite3
from datetime import datetime
from typing import List, Dict, Any, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QPushButton,
    QComboBox, QLineEdit, QMessageBox, QFrame, QGroupBox, QSplitter,
    QTextEdit, QProgressBar, QTabWidget, QTreeWidget, QTreeWidgetItem,
    QFileDialog, QCheckBox, QSpinBox
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QIcon


class DatabaseQueryThread(QThread):
    """数据库查询线程"""
    query_completed = Signal(list, list)  # 结果数据, 列名
    error_occurred = Signal(str)
    
    def __init__(self, db_path: str, query: str):
        super().__init__()
        self.db_path = db_path
        self.query = query
    
    def run(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(self.query)
            
            if self.query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                self.query_completed.emit(results, columns)
            else:
                conn.commit()
                affected_rows = cursor.rowcount
                self.query_completed.emit([[f"操作完成，影响 {affected_rows} 行"]], ["结果"])
            
            conn.close()
        except Exception as e:
            self.error_occurred.emit(str(e))


class DatabaseInfoThread(QThread):
    """数据库信息获取线程"""
    info_loaded = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, db_path: str):
        super().__init__()
        self.db_path = db_path
    
    def run(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            table_info = {}
            for table in tables:
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # 获取行数
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                table_info[table] = {
                    'columns': columns,
                    'row_count': row_count
                }
            
            # 获取数据库大小
            db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            
            info = {
                'tables': table_info,
                'db_size': db_size,
                'db_path': self.db_path
            }
            
            self.info_loaded.emit(info)
            conn.close()
        except Exception as e:
            self.error_occurred.emit(str(e))


class DatabaseManagementWindow(QMainWindow):
    """数据库管理窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_db_path = ""
        self.query_thread = None
        self.info_thread = None
        
        # 数据库文件路径
        self.db_files = {
            "货运记录": "db/shipment_records.db",
            "任务队列": "db/task_queue.db",
            "AI调用日志": "db/ai_call_logs.db",
            "AI配置": "db/ai_config.db",
            "船公司信息": "db/carriers.db"
        }
        
        self.setWindowTitle("数据库管理工具")
        self.setGeometry(100, 100, 1400, 900)
        
        self.setup_ui()
        self.setup_connections()
        self.load_first_database()
    
    def setup_ui(self):
        """设置UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 紧凑设计
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(3)
        
        # 数据库工具栏 - 紧凑设计
        db_toolbar = QHBoxLayout()
        db_toolbar.setSpacing(5)
        
        db_toolbar.addWidget(QLabel("数据库:"))
        self.db_combo = QComboBox()
        for name, path in self.db_files.items():
            self.db_combo.addItem(name, path)
        db_toolbar.addWidget(self.db_combo)
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.setMaximumWidth(60)
        db_toolbar.addWidget(self.browse_btn)
        
        self.refresh_db_btn = QPushButton("刷新")
        self.refresh_db_btn.setMaximumWidth(60)
        db_toolbar.addWidget(self.refresh_db_btn)
        
        db_toolbar.addStretch()
        main_layout.addLayout(db_toolbar)
        
        # 主要内容区域 - 紧凑设计
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：数据库结构
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(2, 2, 2, 2)
        left_layout.setSpacing(2)
        
        structure_label = QLabel("结构")
        structure_label.setMaximumHeight(18)
        structure_label.setStyleSheet("font-size: 11px; font-weight: bold; padding: 2px;")
        left_layout.addWidget(structure_label)
        
        self.structure_tree = QTreeWidget()
        self.structure_tree.setHeaderLabels(["表名", "列数", "行数"])
        self.structure_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #ddd;
                font-size: 10px;
            }
            QTreeWidget::item {
                padding: 2px;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: none;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        left_layout.addWidget(self.structure_tree)
        
        content_splitter.addWidget(left_widget)
        
        # 右侧：查询和结果 - 更紧凑设计
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(2, 2, 2, 2)
        right_layout.setSpacing(2)
        
        # 查询区域 - 紧凑版
        query_group = QGroupBox("查询")
        query_group.setStyleSheet("QGroupBox { font-size: 10px; font-weight: bold; }")
        query_layout = QVBoxLayout(query_group)
        query_layout.setContentsMargins(3, 3, 3, 3)
        query_layout.setSpacing(2)
        
        # 快捷查询按钮 - 紧凑版
        quick_layout = QHBoxLayout()
        quick_layout.setSpacing(2)
        
        self.select_all_btn = QPushButton("全部")
        self.select_all_btn.setMaximumWidth(35)
        self.select_all_btn.setMaximumHeight(22)
        self.select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 2px 4px;
                border-radius: 2px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        quick_layout.addWidget(self.select_all_btn)
        
        self.count_btn = QPushButton("统计")
        self.count_btn.setMaximumWidth(35)
        self.count_btn.setMaximumHeight(22)
        self.count_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 2px 4px;
                border-radius: 2px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        quick_layout.addWidget(self.count_btn)
        
        self.clear_query_btn = QPushButton("清空")
        self.clear_query_btn.setMaximumWidth(35)
        self.clear_query_btn.setMaximumHeight(22)
        self.clear_query_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 2px 4px;
                border-radius: 2px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        quick_layout.addWidget(self.clear_query_btn)
        
        quick_layout.addStretch()
        query_layout.addLayout(quick_layout)
        
        # SQL输入框 - 紧凑版
        self.query_edit = QTextEdit()
        self.query_edit.setMaximumHeight(60)
        self.query_edit.setPlaceholderText("SQL...")
        self.query_edit.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                padding: 3px;
                font-family: 'Consolas', monospace;
                font-size: 10px;
            }
        """)
        query_layout.addWidget(self.query_edit)
        
        # 执行按钮 - 紧凑版
        execute_layout = QHBoxLayout()
        execute_layout.setSpacing(2)
        
        self.execute_btn = QPushButton("执行")
        self.execute_btn.setMaximumWidth(40)
        self.execute_btn.setMaximumHeight(22)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 2px 4px;
                border-radius: 2px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        execute_layout.addWidget(self.execute_btn)
        
        # 限制结果数量
        execute_layout.addWidget(QLabel("限制结果:"))
        self.limit_spin = QSpinBox()
        self.limit_spin.setRange(1, 10000)
        self.limit_spin.setValue(100)
        execute_layout.addWidget(self.limit_spin)
        
        self.auto_limit_check = QCheckBox("自动添加LIMIT")
        self.auto_limit_check.setChecked(True)
        execute_layout.addWidget(self.auto_limit_check)
        
        execute_layout.addStretch()
        query_layout.addLayout(execute_layout)
        
        right_layout.addWidget(query_group)
        
        # 结果区域 - 紧凑版
        result_group = QGroupBox("结果")
        result_group.setStyleSheet("QGroupBox { font-size: 10px; font-weight: bold; }")
        result_layout = QVBoxLayout(result_group)
        result_layout.setContentsMargins(3, 3, 3, 3)
        result_layout.setSpacing(2)
        
        # 结果统计 - 紧凑版
        self.result_stats_label = QLabel("准备就绪")
        self.result_stats_label.setMaximumHeight(16)
        self.result_stats_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 10px;
                padding: 2px;
            }
        """)
        result_layout.addWidget(self.result_stats_label)
        
        # 结果表格 - 紧凑版
        self.result_table = QTableWidget()
        self.result_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 10px;
            }
            QTableWidget::item {
                padding: 2px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 4px;
                border: none;
                font-weight: bold;
                font-size: 10px;
            }
        """)
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.result_table.verticalHeader().setDefaultSectionSize(18)  # 紧凑行高
        result_layout.addWidget(self.result_table)
        
        # 导出按钮
        export_layout = QHBoxLayout()
        
        self.export_csv_btn = QPushButton("导出CSV")
        self.export_csv_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        export_layout.addWidget(self.export_csv_btn)
        
        export_layout.addStretch()
        result_layout.addLayout(export_layout)
        
        right_layout.addWidget(result_group)
        
        content_splitter.addWidget(right_widget)
        content_splitter.setSizes([300, 900])
        
        main_layout.addWidget(content_splitter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def setup_connections(self):
        """设置信号连接"""
        self.db_combo.currentTextChanged.connect(self.on_database_changed)
        self.browse_btn.clicked.connect(self.browse_database)
        self.refresh_db_btn.clicked.connect(self.refresh_database_info)
        self.execute_btn.clicked.connect(self.execute_query)
        self.select_all_btn.clicked.connect(self.quick_select_all)
        self.count_btn.clicked.connect(self.quick_count)
        self.clear_query_btn.clicked.connect(self.clear_query)
        self.export_csv_btn.clicked.connect(self.export_csv)
        self.structure_tree.itemDoubleClicked.connect(self.on_table_double_clicked)
    
    def load_first_database(self):
        """加载第一个数据库"""
        if self.db_combo.count() > 0:
            self.on_database_changed()
    
    def on_database_changed(self):
        """数据库选择改变"""
        db_path = self.db_combo.currentData()
        if db_path:
            self.current_db_path = db_path
            self.refresh_database_info()
    
    def browse_database(self):
        """浏览数据库文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库文件", "", "SQLite数据库 (*.db *.sqlite *.sqlite3)"
        )
        if file_path:
            self.current_db_path = file_path
            self.refresh_database_info()
    
    def refresh_database_info(self):
        """刷新数据库信息"""
        if not self.current_db_path or not os.path.exists(self.current_db_path):
            QMessageBox.warning(self, "警告", "数据库文件不存在")
            return
        
        if self.info_thread and self.info_thread.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        self.info_thread = DatabaseInfoThread(self.current_db_path)
        self.info_thread.info_loaded.connect(self.on_database_info_loaded)
        self.info_thread.error_occurred.connect(self.on_info_error)
        self.info_thread.start()
    
    def on_database_info_loaded(self, info: Dict):
        """数据库信息加载完成"""
        self.progress_bar.setVisible(False)
        self.populate_structure_tree(info)
        
        # 更新状态
        db_size_mb = info['db_size'] / (1024 * 1024)
        table_count = len(info['tables'])
        self.result_stats_label.setText(
            f"数据库: {os.path.basename(info['db_path'])} | "
            f"大小: {db_size_mb:.2f} MB | 表数量: {table_count}"
        )
    
    def on_info_error(self, error: str):
        """信息加载错误"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "错误", f"加载数据库信息失败: {error}")
    
    def populate_structure_tree(self, info: Dict):
        """填充结构树"""
        self.structure_tree.clear()
        
        for table_name, table_info in info['tables'].items():
            table_item = QTreeWidgetItem(self.structure_tree)
            table_item.setText(0, table_name)
            table_item.setText(1, "表")
            table_item.setText(2, f"{table_info['row_count']} 行")
            table_item.setData(0, Qt.ItemDataRole.UserRole, table_name)
            
            for column in table_info['columns']:
                col_item = QTreeWidgetItem(table_item)
                col_item.setText(0, column[1])  # 列名
                col_item.setText(1, column[2])  # 类型
                col_item.setText(2, "主键" if column[5] else "")
        
        self.structure_tree.expandAll()
    
    def execute_query(self):
        """执行查询"""
        query = self.query_edit.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "警告", "请输入SQL查询语句")
            return
        
        if not self.current_db_path:
            QMessageBox.warning(self, "警告", "请先选择数据库")
            return
        
        # 自动添加LIMIT
        if (self.auto_limit_check.isChecked() and 
            query.upper().startswith('SELECT') and 
            'LIMIT' not in query.upper()):
            query += f" LIMIT {self.limit_spin.value()}"
        
        if self.query_thread and self.query_thread.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        self.query_thread = DatabaseQueryThread(self.current_db_path, query)
        self.query_thread.query_completed.connect(self.on_query_completed)
        self.query_thread.error_occurred.connect(self.on_query_error)
        self.query_thread.start()
    
    def on_query_completed(self, results: List, columns: List):
        """查询完成"""
        self.progress_bar.setVisible(False)
        self.populate_result_table(results, columns)
        
        self.result_stats_label.setText(f"查询完成，返回 {len(results)} 行结果")
    
    def on_query_error(self, error: str):
        """查询错误"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "查询错误", f"SQL执行失败: {error}")
    
    def populate_result_table(self, results: List, columns: List):
        """填充结果表格"""
        self.result_table.setRowCount(len(results))
        self.result_table.setColumnCount(len(columns))
        self.result_table.setHorizontalHeaderLabels(columns)
        
        for row, result in enumerate(results):
            for col, value in enumerate(result):
                item = QTableWidgetItem(str(value) if value is not None else "NULL")
                self.result_table.setItem(row, col, item)
        
        # 自动调整列宽
        self.result_table.resizeColumnsToContents()
    
    def quick_select_all(self):
        """快速查看全部"""
        current_item = self.structure_tree.currentItem()
        if current_item:
            table_name = current_item.data(0, Qt.ItemDataRole.UserRole)
            if table_name:
                self.query_edit.setPlainText(f"SELECT * FROM {table_name}")
    
    def quick_count(self):
        """快速统计行数"""
        current_item = self.structure_tree.currentItem()
        if current_item:
            table_name = current_item.data(0, Qt.ItemDataRole.UserRole)
            if table_name:
                self.query_edit.setPlainText(f"SELECT COUNT(*) as total_rows FROM {table_name}")
    
    def clear_query(self):
        """清空查询"""
        self.query_edit.clear()
    
    def on_table_double_clicked(self, item: QTreeWidgetItem):
        """表格双击事件"""
        table_name = item.data(0, Qt.ItemDataRole.UserRole)
        if table_name:
            self.query_edit.setPlainText(f"SELECT * FROM {table_name}")
    
    def export_csv(self):
        """导出CSV"""
        if self.result_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出CSV", f"query_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV文件 (*.csv)"
        )
        
        if file_path:
            try:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # 写入表头
                    headers = []
                    for col in range(self.result_table.columnCount()):
                        headers.append(self.result_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)
                    
                    # 写入数据
                    for row in range(self.result_table.rowCount()):
                        row_data = []
                        for col in range(self.result_table.columnCount()):
                            item = self.result_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)
                
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("Database Management")
    app.setApplicationVersion("1.0.0")
    
    # 设置全局字体
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    
    # 创建并显示窗口
    window = DatabaseManagementWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())