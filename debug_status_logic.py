#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接查看数据库中的任务状态数据
"""

import sqlite3
import os

def check_actual_database_status():
    """检查数据库中的实际状态"""
    print("🔍 检查数据库中的实际任务状态...")
    
    if not os.path.exists('db/task_queue.db'):
        print("❌ 任务队列数据库不存在")
        return
    
    conn = sqlite3.connect('db/task_queue.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # 查看最新的任务记录，按跟踪号分组
    print("\n📊 按跟踪号查看最新任务状态:")
    cursor.execute("""
        WITH latest_tasks AS (
            SELECT tracking_number, task_stage, status, created_at,
                   ROW_NUMBER() OVER (PARTITION BY tracking_number, task_stage ORDER BY created_at DESC) as rn
            FROM task_queue
            WHERE tracking_number IS NOT NULL
        )
        SELECT tracking_number, task_stage, status, created_at
        FROM latest_tasks 
        WHERE rn = 1
        ORDER BY tracking_number, 
                 CASE WHEN task_stage = 'scraping' THEN 1 ELSE 2 END
    """)
    
    results = cursor.fetchall()
    current_tracking = None
    
    for row in results:
        tracking_number = row['tracking_number']
        if tracking_number != current_tracking:
            if current_tracking is not None:
                print("  ---")
            print(f"\n📋 跟踪号: {tracking_number}")
            current_tracking = tracking_number
        
        print(f"    {row['task_stage']:15} {row['status']:15} {row['created_at']}")
    
    # 查看是否有AI任务显示为completed但实际应该是其他状态
    print("\n🔍 检查可能有问题的AI分析任务:")
    cursor.execute("""
        SELECT tracking_number, task_stage, status, created_at, started_at, completed_at
        FROM task_queue 
        WHERE task_stage = 'ai_analysis'
        ORDER BY created_at DESC
        LIMIT 10
    """)
    
    ai_tasks = cursor.fetchall()
    if ai_tasks:
        for task in ai_tasks:
            print(f"  跟踪号: {task['tracking_number']}")
            print(f"    状态: {task['status']}")
            print(f"    创建: {task['created_at']}")
            print(f"    开始: {task['started_at'] or '未开始'}")
            print(f"    完成: {task['completed_at'] or '未完成'}")
            
            # 分析是否有异常
            if task['status'] == 'completed' and not task['completed_at']:
                print("    ⚠️  异常：状态为completed但无完成时间")
            elif task['status'] == 'pending' and task['started_at']:
                print("    ⚠️  异常：状态为pending但有开始时间")
            print("    ---")
    else:
        print("  暂无AI分析任务")
    
    conn.close()

def simulate_get_task_stages_status(tracking_number):
    """模拟get_task_stages_status方法的实际执行"""
    print(f"\n🔍 模拟获取跟踪号 {tracking_number} 的状态:")
    
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    
    # 使用与app.py完全相同的查询
    cursor.execute("""
        SELECT task_stage, status, id, error_message, created_at
        FROM task_queue 
        WHERE tracking_number = ? 
        ORDER BY created_at DESC
    """, (tracking_number,))
    
    tasks = cursor.fetchall()
    print(f"查询返回 {len(tasks)} 个任务:")
    
    for i, task in enumerate(tasks):
        print(f"  {i+1}. {task[0]:12} {task[1]:10} {task[4]} {task[2][:8]}...")
    
    # 模拟状态处理逻辑
    scraping_status = {"status": "未开始", "clickable": False, "task_id": None}
    ai_status = {"status": "未开始", "clickable": False, "task_id": None}
    
    latest_scraping_task = None
    latest_ai_task = None
    
    for task_stage, status, task_id, error_message, created_at in tasks:
        if task_stage == 'scraping' and latest_scraping_task is None:
            latest_scraping_task = (task_stage, status, task_id, error_message)
        elif task_stage == 'ai_analysis' and latest_ai_task is None:
            latest_ai_task = (task_stage, status, task_id, error_message)
    
    print("\n处理结果:")
    if latest_scraping_task:
        status_mapping = {
            'pending': '待处理',
            'processing': '进行中', 
            'completed': '已完成',
            'failed': '失败'
        }
        scraping_status["status"] = status_mapping.get(latest_scraping_task[1], latest_scraping_task[1])
        print(f"  网页抓取: {latest_scraping_task[1]} -> {scraping_status['status']}")
    
    if latest_ai_task:
        status_mapping = {
            'pending': '待处理',
            'processing': '进行中', 
            'completed': '已完成',
            'failed': '失败'
        }
        ai_status["status"] = status_mapping.get(latest_ai_task[1], latest_ai_task[1])
        print(f"  AI分析: {latest_ai_task[1]} -> {ai_status['status']}")
    else:
        if latest_scraping_task and latest_scraping_task[1] != 'completed':
            ai_status["status"] = "等待中"
            print(f"  AI分析: 等待网页抓取完成 -> {ai_status['status']}")
        else:
            print(f"  AI分析: 保持默认 -> {ai_status['status']}")
    
    conn.close()
    return scraping_status, ai_status

def main():
    """主函数"""
    print("🚀 数据库状态直接检查工具")
    print("=" * 60)
    
    check_actual_database_status()
    
    # 获取一个跟踪号进行详细分析
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT tracking_number FROM task_queue ORDER BY rowid DESC LIMIT 1")
    result = cursor.fetchone()
    
    if result:
        tracking_number = result[0]
        print(f"\n" + "=" * 60)
        scraping_status, ai_status = simulate_get_task_stages_status(tracking_number)
        
        print(f"\n最终状态模拟结果:")
        print(f"  网页抓取: {scraping_status['status']}")
        print(f"  AI分析: {ai_status['status']}")
        
        if ai_status['status'] == '已完成':
            print("  ⚠️  发现问题：AI分析状态为'已完成'")
            print("     可能原因：")
            print("     1. 数据库中AI任务状态确实是'completed'")
            print("     2. 状态映射逻辑有问题")
            print("     3. 查询逻辑返回了错误的任务")
    
    conn.close()

if __name__ == "__main__":
    main()