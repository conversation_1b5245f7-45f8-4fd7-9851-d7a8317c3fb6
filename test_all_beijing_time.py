#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的东八区时间验证测试脚本
验证所有数据库表的时间字段都正确使用东八区时间
"""

import sys
import os
from datetime import datetime, timezone, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from task_manager import TaskManager, create_bill_of_lading_task, get_beijing_time, get_beijing_time_str
from shipment_manager import ShipmentManager
from db_logger import AICallLogger, get_beijing_time as ai_get_beijing_time, get_beijing_time_str as ai_get_beijing_time_str

def test_all_beijing_time_functions():
    """测试所有东八区时间函数的一致性"""
    print("=" * 80)
    print("测试所有东八区时间函数的一致性")
    print("=" * 80)
    
    # 测试TaskManager的时间函数
    tm_beijing_time = get_beijing_time()
    tm_beijing_time_str = get_beijing_time_str()
    
    # 测试AICallLogger的时间函数
    ai_beijing_time = ai_get_beijing_time()
    ai_beijing_time_str = ai_get_beijing_time_str()
    
    # 测试ShipmentManager的时间函数（使用TaskManager的）
    from shipment_manager import get_beijing_time as sm_get_beijing_time
    from shipment_manager import get_beijing_time_str as sm_get_beijing_time_str
    sm_beijing_time = sm_get_beijing_time()
    sm_beijing_time_str = sm_get_beijing_time_str()
    
    print(f"[TIME] TaskManager时间函数:")
    print(f"  - 时间对象: {tm_beijing_time}")
    print(f"  - 时间字符串: {tm_beijing_time_str}")
    print(f"  - 时区偏移: {tm_beijing_time.utcoffset()}")
    
    print(f"\\n[TIME] AICallLogger时间函数:")
    print(f"  - 时间对象: {ai_beijing_time}")
    print(f"  - 时间字符串: {ai_beijing_time_str}")
    print(f"  - 时区偏移: {ai_beijing_time.utcoffset()}")
    
    print(f"\\n[TIME] ShipmentManager时间函数:")
    print(f"  - 时间对象: {sm_beijing_time}")
    print(f"  - 时间字符串: {sm_beijing_time_str}")
    print(f"  - 时区偏移: {sm_beijing_time.utcoffset()}")
    
    # 验证时区偏移是否都是8小时
    expected_offset = timedelta(hours=8)
    all_correct = True
    
    if tm_beijing_time.utcoffset() != expected_offset:
        print(f"[ERROR] TaskManager时区偏移不正确: {tm_beijing_time.utcoffset()}")
        all_correct = False
    
    if ai_beijing_time.utcoffset() != expected_offset:
        print(f"[ERROR] AICallLogger时区偏移不正确: {ai_beijing_time.utcoffset()}")
        all_correct = False
        
    if sm_beijing_time.utcoffset() != expected_offset:
        print(f"[ERROR] ShipmentManager时区偏移不正确: {sm_beijing_time.utcoffset()}")
        all_correct = False
    
    if all_correct:
        print(f"\\n[SUCCESS] 所有时间函数都正确使用东八区时间！")
    else:
        print(f"\\n[ERROR] 发现时区设置问题！")
    
    return all_correct

def test_task_manager_complete_workflow():
    """测试TaskManager完整工作流程的时间记录"""
    print("\\n" + "=" * 80)
    print("测试 TaskManager 完整工作流程时间记录")
    print("=" * 80)
    
    try:
        manager = TaskManager()
        
        print("\\n[TEST] 创建任务...")
        before_create = get_beijing_time_str()
        task_id = create_bill_of_lading_task(
            bl_number="COMPLETE_WORKFLOW_001",
            creator_id="workflow_test_user",
            creator_name="完整工作流程测试用户",
            carrier="MSC",
            priority=1,
            remarks="完整工作流程测试任务"
        )
        after_create = get_beijing_time_str()
        
        print(f"[TIME] 创建前: {before_create}")
        print(f"[TIME] 创建后: {after_create}")
        
        # 获取任务详情检查创建时间
        task = manager.get_task_by_id(task_id)
        print(f"[TIME] 数据库中的创建时间: {task.get('created_at', 'N/A')}")
        
        print("\\n[TEST] 更新为processing状态...")
        before_processing = get_beijing_time_str()
        manager.update_task_status(task_id, "processing")
        after_processing = get_beijing_time_str()
        
        task = manager.get_task_by_id(task_id)
        print(f"[TIME] 处理前: {before_processing}")
        print(f"[TIME] 处理后: {after_processing}")
        print(f"[TIME] 数据库中的开始时间: {task.get('started_at', 'N/A')}")
        
        print("\\n[TEST] 更新为completed状态...")
        before_completed = get_beijing_time_str()
        manager.update_task_status(task_id, "completed", result_summary="完整工作流程测试完成")
        after_completed = get_beijing_time_str()
        
        task = manager.get_task_by_id(task_id)
        print(f"[TIME] 完成前: {before_completed}")
        print(f"[TIME] 完成后: {after_completed}")
        print(f"[TIME] 数据库中的完成时间: {task.get('completed_at', 'N/A')}")
        
        print("\\n[SUCCESS] TaskManager 完整工作流程测试完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] TaskManager工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shipment_manager_complete_workflow():
    """测试ShipmentManager完整工作流程的时间记录"""
    print("\\n" + "=" * 80)
    print("测试 ShipmentManager 完整工作流程时间记录")
    print("=" * 80)
    
    try:
        manager = ShipmentManager()
        
        print("\\n[TEST] 创建货运记录...")
        before_create = get_beijing_time_str()
        record_id = manager.create_shipment_record(
            bill_of_lading="COMPLETE_SHIPMENT_001",
            container_number="COMPLETE_CONT_001",
            carrier_company="MSC",
            estimated_arrival_time=get_beijing_time(),
            remarks="完整工作流程测试货运记录",
            created_by="workflow_test_user"
        )
        after_create = get_beijing_time_str()
        
        print(f"[TIME] 创建前: {before_create}")
        print(f"[TIME] 创建后: {after_create}")
        print(f"[TEST] 货运记录创建成功，ID: {record_id}")
        
        print("\\n[TEST] 更新预计到港时间...")
        before_update_eta = get_beijing_time_str()
        new_eta = get_beijing_time_str()
        manager._update_estimated_arrival_time(record_id, new_eta)
        after_update_eta = get_beijing_time_str()
        
        print(f"[TIME] 更新ETA前: {before_update_eta}")
        print(f"[TIME] 更新ETA后: {after_update_eta}")
        
        print("\\n[TEST] 更新记录状态...")
        before_update_status = get_beijing_time_str()
        manager.update_shipment_status(record_id, "处理中", "workflow_test_user")
        after_update_status = get_beijing_time_str()
        
        print(f"[TIME] 更新状态前: {before_update_status}")
        print(f"[TIME] 更新状态后: {after_update_status}")
        
        print("\\n[TEST] 添加时间节点...")
        before_add_dates = get_beijing_time_str()
        test_dates = [
            {
                'date': '2025-08-07 08:00:00',
                'location': '深圳港',
                'event': '装船',
                'status': '已完成',
                'event_type': 'departure',
                'vessel_info': {'name': 'MSC WORKFLOW', 'voyage': 'WF001'}
            },
            {
                'date': '2025-08-20 16:00:00',
                'location': '洛杉矶港',
                'event': '到港',
                'status': '预计',
                'event_type': 'arrival',
                'vessel_info': {'name': 'MSC WORKFLOW', 'voyage': 'WF001'}
            }
        ]
        
        manager.add_shipment_dates(record_id, test_dates)
        after_add_dates = get_beijing_time_str()
        
        print(f"[TIME] 添加节点前: {before_add_dates}")
        print(f"[TIME] 添加节点后: {after_add_dates}")
        
        print("\\n[SUCCESS] ShipmentManager 完整工作流程测试完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] ShipmentManager工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_call_logger_workflow():
    """测试AICallLogger工作流程的时间记录"""
    print("\\n" + "=" * 80)
    print("测试 AICallLogger 工作流程时间记录")
    print("=" * 80)
    
    try:
        logger = AICallLogger()
        
        print("\\n[TEST] 记录AI调用日志...")
        before_log = get_beijing_time_str()
        
        request_time = get_beijing_time()
        response_time = get_beijing_time()
        
        record_id = logger.log_ai_call(
            model="gpt-4",
            content_type="text",
            caller_id="workflow_test_user",
            request_time=request_time,
            response_time=response_time,
            prompt_tokens=100,
            completion_tokens=200,
            total_tokens=300,
            business_id="COMPLETE_WORKFLOW_001",
            business_type="shipment_analysis",
            status="success",
            cost_amount=0.01,
            currency="USD",
            remarks="完整工作流程AI调用测试"
        )
        
        after_log = get_beijing_time_str()
        
        print(f"[TIME] 记录前: {before_log}")
        print(f"[TIME] 记录后: {after_log}")
        print(f"[TEST] AI调用日志记录成功，ID: {record_id}")
        
        print("\\n[TEST] 获取调用统计...")
        stats = logger.get_call_stats(caller_id="workflow_test_user", days=1)
        print(f"[INFO] 统计信息: {stats['basic']}")
        
        print("\\n[TEST] 获取最近调用记录...")
        recent_calls = logger.get_recent_calls(limit=3, caller_id="workflow_test_user")
        print(f"[INFO] 最近调用记录数量: {len(recent_calls)}")
        for call in recent_calls:
            print(f"  - ID: {call['id']}, 模型: {call['model']}, 请求时间: {call['request_time']}")
        
        print("\\n[SUCCESS] AICallLogger 工作流程测试完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] AICallLogger工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_all_database_times():
    """验证所有数据库表的时间记录"""
    print("\\n" + "=" * 80)
    print("验证所有数据库表的时间记录")
    print("=" * 80)
    
    try:
        import sqlite3
        
        # 验证任务队列表
        print("\\n[VERIFY] 检查任务队列表...")
        conn = sqlite3.connect('db/task_queue.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, task_name, created_at, started_at, completed_at 
            FROM task_queue 
            WHERE tracking_number LIKE '%WORKFLOW%' OR tracking_number LIKE '%COMPLETE%'
            ORDER BY created_at DESC LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        for task in tasks:
            print(f"[TIME] 任务 {task['task_name']}:")
            print(f"  - 创建时间: {task['created_at']}")
            print(f"  - 开始时间: {task['started_at']}")
            print(f"  - 完成时间: {task['completed_at']}")
            
            # 检查时间格式是否包含时区信息
            if task['created_at'] and '+08:00' not in str(task['created_at']):
                print(f"  [WARNING] 创建时间可能不是东八区格式: {task['created_at']}")
            if task['started_at'] and '+08:00' not in str(task['started_at']):
                print(f"  [WARNING] 开始时间可能不是东八区格式: {task['started_at']}")
            if task['completed_at'] and '+08:00' not in str(task['completed_at']):
                print(f"  [WARNING] 完成时间可能不是东八区格式: {task['completed_at']}")
        
        conn.close()
        
        # 验证货运记录表
        print("\\n[VERIFY] 检查货运记录表...")
        conn = sqlite3.connect('db/shipment_records.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, bill_of_lading, created_at, updated_at, estimated_arrival_time
            FROM shipment_records 
            WHERE bill_of_lading LIKE '%COMPLETE%' OR bill_of_lading LIKE '%WORKFLOW%'
            ORDER BY created_at DESC LIMIT 10
        """)
        
        records = cursor.fetchall()
        for record in records:
            print(f"[TIME] 货运记录 {record['bill_of_lading']}:")
            print(f"  - 创建时间: {record['created_at']}")
            print(f"  - 更新时间: {record['updated_at']}")
            print(f"  - 预计到港时间: {record['estimated_arrival_time']}")
            
            # 检查时间格式
            if record['created_at'] and '+08:00' not in str(record['created_at']):
                print(f"  [WARNING] 创建时间可能不是东八区格式: {record['created_at']}")
            if record['updated_at'] and '+08:00' not in str(record['updated_at']):
                print(f"  [WARNING] 更新时间可能不是东八区格式: {record['updated_at']}")
        
        # 验证时间节点表
        cursor.execute("""
            SELECT sd.date, sd.location, sd.description, sd.created_at
            FROM shipment_dates sd
            JOIN shipment_records sr ON sd.shipment_id = sr.id
            WHERE sr.bill_of_lading LIKE '%COMPLETE%' OR sr.bill_of_lading LIKE '%WORKFLOW%'
            ORDER BY sd.created_at DESC LIMIT 10
        """)
        
        dates = cursor.fetchall()
        print(f"\\n[VERIFY] 检查时间节点表...")
        for date_record in dates:
            print(f"[TIME] 时间节点 {date_record['location']} - {date_record['description']}:")
            print(f"  - 节点时间: {date_record['date']}")
            print(f"  - 创建时间: {date_record['created_at']}")
            
            if date_record['created_at'] and '+08:00' not in str(date_record['created_at']):
                print(f"  [WARNING] 创建时间可能不是东八区格式: {date_record['created_at']}")
        
        conn.close()
        
        # 验证AI调用日志表
        print("\\n[VERIFY] 检查AI调用日志表...")
        conn = sqlite3.connect('db/ai_call_logs.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, model, caller_id, request_time, response_time, created_at, updated_at
            FROM ai_call_logs 
            WHERE caller_id LIKE '%workflow%' OR business_id LIKE '%COMPLETE%'
            ORDER BY request_time DESC LIMIT 5
        """)
        
        logs = cursor.fetchall()
        for log in logs:
            print(f"[TIME] AI调用 {log['model']} by {log['caller_id']}:")
            print(f"  - 请求时间: {log['request_time']}")
            print(f"  - 响应时间: {log['response_time']}")
            print(f"  - 创建时间: {log['created_at']}")
            print(f"  - 更新时间: {log['updated_at']}")
            
            if log['created_at'] and '+08:00' not in str(log['created_at']):
                print(f"  [WARNING] 创建时间可能不是东八区格式: {log['created_at']}")
            if log['updated_at'] and '+08:00' not in str(log['updated_at']):
                print(f"  [WARNING] 更新时间可能不是东八区格式: {log['updated_at']}")
        
        conn.close()
        
        print("\\n[SUCCESS] 所有数据库表时间验证完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] 数据库时间验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("[TEST] 开始全面东八区时间验证测试...")
    print(f"[TEST] 测试开始时间: {get_beijing_time_str()}")
    
    results = []
    
    # 运行所有测试
    print("\\n" + "=" * 40)
    results.append(("时间函数一致性", test_all_beijing_time_functions()))
    results.append(("TaskManager工作流程", test_task_manager_complete_workflow()))
    results.append(("ShipmentManager工作流程", test_shipment_manager_complete_workflow()))
    results.append(("AICallLogger工作流程", test_ai_call_logger_workflow()))
    results.append(("数据库时间验证", verify_all_database_times()))
    
    # 汇总结果
    print("\\n" + "=" * 80)
    print("全面东八区时间验证测试结果汇总")
    print("=" * 80)
    
    success_count = 0
    for test_name, result in results:
        status = "[SUCCESS]" if result else "[FAILED]"
        print(f"{status} {test_name}")
        if result:
            success_count += 1
    
    print(f"\\n[SUMMARY] 测试完成: {success_count}/{len(results)} 项通过")
    
    if success_count == len(results):
        print("\\n[EXCELLENT] 所有测试都通过！东八区时间配置完全正确！")
        print("\\n[INFO] 现在所有数据库操作都会正确使用东八区时间，包括：")
        print("  [OK] task_queue 表的 created_at, started_at, completed_at, cancelled_at")
        print("  [OK] shipment_records 表的 created_at, updated_at")
        print("  [OK] shipment_dates 表的 created_at")
        print("  [OK] ai_call_logs 表的 created_at, updated_at")
        print("\\n[INFO] 所有时间记录都包含正确的时区信息 (+08:00)")
    else:
        print(f"\\n[WARNING] 有 {len(results) - success_count} 项测试未通过，请检查相关配置")
    
    print("\\n" + "=" * 80)

if __name__ == "__main__":
    main()