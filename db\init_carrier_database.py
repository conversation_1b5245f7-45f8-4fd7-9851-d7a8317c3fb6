#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
船公司数据库初始化脚本
创建carriers表用于管理船公司信息，替代硬编码的COMPANY_DATA字典
"""

import sqlite3
import os
import json
from datetime import datetime

def create_database():
    """创建船公司数据库和表"""
    db_path = 'db/carriers.db'
    
    # 如果数据库文件已存在，备份
    if os.path.exists(db_path):
        backup_path = f'carriers_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        os.rename(db_path, backup_path)
        print(f"已备份现有数据库到: {backup_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建船公司主表
    create_carriers_table = """
    CREATE TABLE carriers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_name VARCHAR(200) NOT NULL UNIQUE,
        company_code VARCHAR(20),
        international_site VARCHAR(500),
        chinese_site VARCHAR(500),
        tracking_site VARCHAR(500) NOT NULL,
        input_element_id VARCHAR(100),
        search_button_id VARCHAR(100),
        is_active BOOLEAN NOT NULL DEFAULT 1,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """
    
    # 创建SCAC前缀表
    create_scac_table = """
    CREATE TABLE carrier_scac_prefixes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        carrier_id INTEGER NOT NULL,
        scac_prefix VARCHAR(10) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (carrier_id) REFERENCES carriers(id) ON DELETE CASCADE,
        UNIQUE(carrier_id, scac_prefix)
    )
    """
    
    # 创建提单号规则表
    create_bl_patterns_table = """
    CREATE TABLE carrier_bl_patterns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        carrier_id INTEGER NOT NULL,
        pattern VARCHAR(200) NOT NULL,
        description VARCHAR(500),
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (carrier_id) REFERENCES carriers(id) ON DELETE CASCADE
    )
    """
    
    # 执行创建表语句
    cursor.execute(create_carriers_table)
    cursor.execute(create_scac_table)
    cursor.execute(create_bl_patterns_table)
    
    # 创建索引
    cursor.execute("CREATE INDEX idx_carriers_company_name ON carriers(company_name)")
    cursor.execute("CREATE INDEX idx_scac_prefix ON carrier_scac_prefixes(scac_prefix)")
    cursor.execute("CREATE INDEX idx_carrier_id_scac ON carrier_scac_prefixes(carrier_id)")
    cursor.execute("CREATE INDEX idx_carrier_id_patterns ON carrier_bl_patterns(carrier_id)")
    
    conn.commit()
    conn.close()
    
    print(f"船公司数据库创建成功: {db_path}")
    print("已创建表:")
    print("- carriers (船公司主表)")
    print("- carrier_scac_prefixes (SCAC前缀表)")
    print("- carrier_bl_patterns (提单号规则表)")

def insert_sample_data():
    """插入示例数据"""
    conn = sqlite3.connect('db/carriers.db')
    cursor = conn.cursor()
    
    # 示例船公司数据
    sample_carriers = [
        {
            "company_name": "MSC (Mediterranean Shipping Company / 地中海航运)",
            "company_code": "MSC",
            "international_site": "https://www.msc.com/",
            "chinese_site": "https://www.msccargo.cn/",
            "tracking_site": "https://www.msc.com/en/track-a-shipment",
            "input_element_id": "#trackingNumber",
            "search_button_id": None,  # MSC使用回车键触发搜索
            "scac_prefixes": ["MSCU", "MEDU"],
            "bl_patterns": [r"^(177|MEDU)\w*"]
        },
        {
            "company_name": "Maersk (马士基)",
            "company_code": "MAERSK",
            "international_site": "https://www.maersk.com/",
            "chinese_site": "https://www.maersk.com/local-information/asia-pacific/china",
            "tracking_site": "https://www.maersk.com/tracking/",
            "input_element_id": "#tracking-input",
            "search_button_id": "#tracking-search-btn",
            "scac_prefixes": ["MAEU"],
            "bl_patterns": [r"^\d{9}$", r"^SGHX\d{5}\w*"]
        },
        {
            "company_name": "CMA CGM (达飞海运)",
            "company_code": "CMACGM",
            "international_site": "https://www.cma-cgm.com/",
            "chinese_site": "https://www.cma-cgm.com/local/china",
            "tracking_site": "https://www.cma-cgm.com/ebusiness/tracking",
            "input_element_id": "#container-number",
            "search_button_id": "#search-button",
            "scac_prefixes": ["CMDU"],
            "bl_patterns": [r"^(CN|WM|APLU|DR|CB|AC)\w*"]
        }
    ]
    
    for carrier_data in sample_carriers:
        # 插入船公司主记录
        cursor.execute("""
            INSERT INTO carriers (company_name, company_code, international_site, chinese_site, tracking_site, input_element_id, search_button_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            carrier_data["company_name"],
            carrier_data["company_code"],
            carrier_data["international_site"],
            carrier_data["chinese_site"],
            carrier_data["tracking_site"],
            carrier_data["input_element_id"],
            carrier_data["search_button_id"]
        ))
        
        carrier_id = cursor.lastrowid
        
        # 插入SCAC前缀
        for scac in carrier_data["scac_prefixes"]:
            cursor.execute("""
                INSERT INTO carrier_scac_prefixes (carrier_id, scac_prefix)
                VALUES (?, ?)
            """, (carrier_id, scac))
        
        # 插入提单号规则
        for pattern in carrier_data["bl_patterns"]:
            cursor.execute("""
                INSERT INTO carrier_bl_patterns (carrier_id, pattern)
                VALUES (?, ?)
            """, (carrier_id, pattern))
    
    conn.commit()
    conn.close()
    
    print(f"已插入 {len(sample_carriers)} 个示例船公司数据")

def test_database():
    """测试数据库功能"""
    conn = sqlite3.connect('db/carriers.db')
    cursor = conn.cursor()
    
    # 查询所有船公司
    cursor.execute("""
        SELECT c.id, c.company_name, c.tracking_site,
               GROUP_CONCAT(DISTINCT s.scac_prefix) as scac_prefixes,
               GROUP_CONCAT(DISTINCT p.pattern) as bl_patterns
        FROM carriers c
        LEFT JOIN carrier_scac_prefixes s ON c.id = s.carrier_id
        LEFT JOIN carrier_bl_patterns p ON c.id = p.carrier_id
        WHERE c.is_active = 1
        GROUP BY c.id, c.company_name, c.tracking_site
        ORDER BY c.company_name
    """)
    
    results = cursor.fetchall()
    
    print("\n=== 数据库测试结果 ===")
    for row in results:
        print(f"ID: {row[0]}")
        print(f"公司: {row[1]}")
        print(f"追踪网站: {row[2]}")
        print(f"SCAC前缀: {row[3]}")
        print(f"提单规则: {row[4]}")
        print("-" * 50)
    
    conn.close()
    
    return len(results)

if __name__ == "__main__":
    print("正在创建船公司数据库...")
    create_database()
    insert_sample_data()
    count = test_database()
    print(f"\n数据库初始化完成！共有 {count} 个船公司记录。")
    print("\n建议下一步:")
    print("1. 运行数据迁移脚本，将 carrier_lookup.py 中的 COMPANY_DATA 迁移到数据库")
    print("2. 修改 carrier_lookup.py 使用数据库查询替代硬编码字典")
    print("3. 创建船公司数据管理界面，方便添加/编辑船公司信息")