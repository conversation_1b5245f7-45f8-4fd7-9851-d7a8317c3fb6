import os
import time
import threading
import atexit
import queue
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
from datetime import datetime
from utils.file_manager import get_file_manager

# 导入AI模块
from ai import analyze_shipment_dates
from ai.text_analyzer import simplify_html_for_logistics_ai

# 共享的 Playwright/Browser（用于浏览器复用，减少启动成本）
_playwright = None
_browser = None
_browser_lock = threading.Lock()
_runner_lock = threading.Lock()
_runner = None  # 单线程执行器实例（延迟创建）


def get_shared_browser(headless: bool = False):
    """获取共享Browser实例，若不存在则启动一次并复用。"""
    global _playwright, _browser
    with _browser_lock:
        if _browser is not None:
            return _browser
        pw = sync_playwright().start()
        _playwright = pw
        _browser = pw.chromium.launch(headless=headless)
        return _browser


def close_shared_browser():
    """关闭共享Browser与Playwright（进程退出时自动调用）。"""
    global _playwright, _browser
    with _browser_lock:
        try:
            if _browser:
                _browser.close()
        except Exception:
            pass
        try:
            if _playwright:
                _playwright.stop()
        except Exception:
            pass
        _browser = None
        _playwright = None


# 进程退出时自动清理共享浏览器
atexit.register(close_shared_browser)

class _SingleThreadRunner:
    """将任务串行化到一个固定后台线程，避免 Playwright 在线程间切换。"""
    def __init__(self, name: str = "PlaywrightWorker"):
        self._q: "queue.Queue[tuple]" = queue.Queue()
        self._thread = threading.Thread(target=self._loop, name=name, daemon=True)
        self._thread.start()

    def _loop(self):
        # 在此线程内初始化/复用 Playwright（通过 get_shared_browser 懒加载）
        while True:
            func, args, kwargs, out_box = self._q.get()
            try:
                res = func(*args, **kwargs)
                out_box['result'] = res
            except Exception as e:
                out_box['exception'] = e
            finally:
                out_box['event'].set()

    def submit(self, func, *args, **kwargs):
        done = threading.Event()
        out_box = {'event': done}
        self._q.put((func, args, kwargs, out_box))
        done.wait()
        if 'exception' in out_box:
            raise out_box['exception']
        return out_box.get('result')


def _get_runner() -> _SingleThreadRunner:
    global _runner
    with _runner_lock:
        if _runner is None:
            _runner = _SingleThreadRunner()
        return _runner


def run_visual_agent(bl_number: str, url: str = "https://www.msc.com/en/track-a-shipment", input_element_id: str = "#trackingNumber", search_button_id: str = None):
    """公共入口：将调用排队到单一工作线程，确保同一线程内使用 Playwright。"""
    print(f"[QUEUE] 已排队网页抓取任务: {bl_number}")
    return _get_runner().submit(_run_visual_agent_internal, bl_number, url, input_element_id, search_button_id)


def _run_visual_agent_internal(bl_number: str, url: str = "https://www.msc.com/en/track-a-shipment", input_element_id: str = "#trackingNumber", search_button_id: str = None):
    """
    运行视觉AI代理，自动化网页操作
    
    Args:
        bl_number: 提单号
        url: 要访问的网站URL
        input_element_id: 输入提单号的元素ID或选择器
        search_button_id: 查询按钮的元素ID或选择器，如果为None则使用回车键触发查询
    """
    # 获取文件管理器
    file_mgr = get_file_manager()
    current_time = datetime.now()
    
    # 使用共享浏览器以减少每次启动开销；每次任务新建上下文确保隔离
    browser = get_shared_browser(headless=False)
    context = browser.new_context()
    page = context.new_page()
    
    try:
        # 导航到指定网站（更快的加载等待策略）
        print(f"➡️ 导航到网站: {url}...")
        page.goto(url, wait_until="domcontentloaded", timeout=45000)
        try:
            page.wait_for_load_state("networkidle", timeout=10000)
        except PlaywrightTimeoutError:
            pass
        
        # 处理可能的Cookie弹窗（更稳健，尝试多种常见按钮）
        print("➡️ 处理Cookie弹窗...")
        for sel in [
            "#onetrust-accept-btn-handler",
            "button:has-text('Accept')",
            "button:has-text('I agree')",
            "button:has-text('同意')",
            "text=同意",
        ]:
            try:
                locator = page.locator(sel)
                if locator.count() > 0:
                    locator.first.click()
                    print(f"✅ 已点击Cookie按钮: {sel}")
                    break
            except Exception:
                pass
        
        # 直接定位输入框并输入提单号（显式等待可见）
        print("➡️ 定位输入框并输入提单号...")
        input_locator = page.locator(input_element_id)
        input_locator.wait_for(state="visible", timeout=15000)
        input_locator.click()
        input_locator.fill(bl_number)
        print(f"✅ 已输入提单号: {bl_number}")
        
        # 触发查询前记录页面内容长度，用于动态判断结果是否加载
        initial_html_len = len(page.content())
        
        # 触发查询
        if search_button_id:
            search_button = page.locator(search_button_id)
            search_button.wait_for(state="visible", timeout=10000)
            search_button.click()
            print(f"✅ 已点击查询按钮: {search_button_id}")
        else:
            page.keyboard.press("Enter")
            print("✅ 已按回车触发查询")
        
        # 动态等待查询结果（替代固定30秒等待）
        print("⏳ 等待查询结果（动态判断）...")
        try:
            page.wait_for_load_state("networkidle", timeout=10000)
        except PlaywrightTimeoutError:
            pass
        
        ready = False
        start_ts = time.time()
        max_wait_sec = 20  # 最多等待20秒
        while time.time() - start_ts < max_wait_sec:
            try:
                # 1) 页面内容长度显著变化（通常意味着新内容渲染完成）
                cur_len = len(page.content())
                if abs(cur_len - initial_html_len) > 1500:
                    ready = True
                    break
                # 2) 常见结构出现（例如表格/结果容器）
                if page.locator("table").count() > 0:
                    ready = True
                    break
                if page.locator(".result, #result, #results, .tracking, .cargo, .shipment").count() > 0:
                    ready = True
                    break
                # 3) 常见提示文本
                if page.locator("text=查询结果").count() > 0 or page.locator("text=No result").count() > 0 or page.locator("text=未找到").count() > 0:
                    ready = True
                    break
            except Exception:
                # 忽略轮询中的偶发错误
                pass
            time.sleep(0.5)
        
        if ready:
            print("✅ 检测到结果已加载（动态判断）")
        else:
            print("⚠️ 未能明确检测到结果，继续后续流程以尽快完成")
        
        # 保存最终截图到文件管理器（确保截图总是被保存）
        final_screenshot_path = None
        storage_folder_path = None
        try:
            temp_screenshot_path = "temp_final_result.png"
            page.screenshot(path=temp_screenshot_path, full_page=True)
            final_screenshot_path = file_mgr.save_file(bl_number, temp_screenshot_path, "final_result.png", current_time)
            # 获取存储文件夹路径
            storage_folder_path = file_mgr.get_storage_path(bl_number, current_time)
            os.remove(temp_screenshot_path)  # 删除临时文件
            print(f"✅ 查询结果长截图已保存为: {final_screenshot_path}")
        except Exception as screenshot_error:
            print(f"⚠️ 保存截图时出错: {screenshot_error}")
            # 即使截图失败，也继续执行后续流程
        
        # 获取页面HTML内容并用AI分析
        print("🤖 正在用AI分析页面内容...")
        page_html = page.content()
        
        # 保存原始HTML内容到文件管理器
        original_html_path = file_mgr.save_content(bl_number, page_html, f"page_content_original_{bl_number}.txt", current_time)
        print(f"📄 原始HTML内容已保存为: {original_html_path}")
        
        # 获取简化的HTML用于比较
        simplified_html_preview = simplify_html_for_logistics_ai(page_html)
        simplified_html_path = file_mgr.save_content(bl_number, simplified_html_preview, f"page_content_simplified_{bl_number}.txt", current_time)
        print(f"📄 简化HTML内容已保存为: {simplified_html_path}")
        print(f"📊 HTML压缩率: {len(simplified_html_preview)/len(page_html)*100:.1f}%")
        
        # 确保变量已初始化，避免AI失败时未定义
        ai_result_path = None
        ai_analysis = analyze_shipment_dates(page_html, bl_number)
        if ai_analysis:
            print(f"📋 AI分析结果:\n{ai_analysis}")
            
            # 保存AI分析结果到文件管理器
            ai_result_content = f"提单号: {bl_number}\n"
            ai_result_content += f"分析时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            ai_result_content += "=" * 50 + "\n"
            ai_result_content += ai_analysis
            
            ai_result_path = file_mgr.save_content(bl_number, ai_result_content, f"ai_analysis_result_{bl_number}.txt", current_time)
            print(f"📄 AI分析结果已保存为: {ai_result_path}")
        else:
            print("❌ AI分析失败")
        
        # 返回结果数据，包含截图路径和AI分析结果
        result_data = {
            'result_files': {
                'screenshot': final_screenshot_path,
                'ai_analysis': ai_result_path if ai_analysis else None
            },
            'ai_analysis': ai_analysis,
            'status': 'completed',
            'raw_data_path': storage_folder_path  # 添加实际的存储文件夹路径
        }
        
        print(f"📦 返回结果数据: {result_data}")
        return result_data

    except Exception as e:
        print(f"💥 自动化流程中发生严重错误: {e}")
        # 保存错误时的截图用于调试
        error_screenshot_path = None
        try:
            temp_error_screenshot = "temp_error_screenshot.png"
            page.screenshot(path=temp_error_screenshot, full_page=True)
            error_screenshot_path = file_mgr.save_file(bl_number, temp_error_screenshot, "error_screenshot.png", current_time)
            os.remove(temp_error_screenshot)  # 删除临时文件
            print(f"📸 错误长截图已保存为: {error_screenshot_path}")
        except:
            print("⚠️ 无法保存错误截图")
        
        # 返回错误结果数据
        result_data = {
            'result_files': {
                'screenshot': error_screenshot_path,
                'ai_analysis': None
            },
            'ai_analysis': None,
            'status': 'error',
            'error': str(e),
            'raw_data_path': file_mgr.get_storage_path(bl_number, current_time) if error_screenshot_path else None
        }
        
        print(f"📦 返回错误结果数据: {result_data}")
        return result_data
    finally:
        try:
            context.close()
            print("✅ 页面上下文已关闭")
        except:
            print("⚠️ 关闭页面上下文时出现问题")

if __name__ == "__main__":
    # 测试用例
    bill_of_lading_number = "MEDUJ0618622"
    # 使用MSC网站进行测试，使用回车键触发查询
    run_visual_agent(bill_of_lading_number, "https://www.msc.com/en/track-a-shipment", "#trackingNumber", None)