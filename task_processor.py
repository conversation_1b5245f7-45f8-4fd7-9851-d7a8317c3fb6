#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务处理器
支持传统单阶段和新的两阶段任务处理模式
"""

import os
import sys
import time
import threading
from datetime import datetime
from typing import List, Dict, Optional
import json
import traceback

from task_manager import TaskManager
from task_executor import run_visual_agent
# from two_stage_task_processor import TwoStageTaskProcessor  # 暂时禁用
from utils.carrier_lookup import get_company_info
from utils.file_manager import get_file_manager


class TaskProcessor:
    """
    任务处理器类 - 传统单阶段模式（向后兼容）
    负责从任务队列中获取任务并处理
    """

    def __init__(self, max_concurrent_tasks: int = 1, check_interval: int = 5, 
                 completion_callback=None, use_two_stage: bool = False):
        """
        初始化任务处理器

        Args:
            max_concurrent_tasks: 最大并发任务数
            check_interval: 检查任务队列的间隔（秒）
            completion_callback: 任务完成时的回调函数
            use_two_stage: 是否使用两阶段处理模式
        """
        self.task_manager = TaskManager()
        self.max_concurrent_tasks = max_concurrent_tasks
        self.check_interval = check_interval
        self.completion_callback = completion_callback
        self.use_two_stage = use_two_stage
        self.running = False
        self.active_tasks = {}  # 正在处理的任务
        self.worker_threads = []
        
        # 如果启用两阶段模式，创建两阶段处理器
        if self.use_two_stage:
            # self.two_stage_processor = TwoStageTaskProcessor(
            #     max_scraping_tasks=max(1, max_concurrent_tasks // 2),
            #     max_ai_tasks=max(1, max_concurrent_tasks // 2),
            #     check_interval=check_interval,
            #     completion_callback=completion_callback
            # )
            print("[WARNING] 两阶段模式暂时禁用，请使用定时任务处理器")
            self.two_stage_processor = None
            self.use_two_stage = False  # 强制禁用
        else:
            self.two_stage_processor = None
            print("[INFO] 使用传统单阶段任务处理模式")

    def process_single_task(self, task: Dict) -> bool:
        """
        处理单个任务（传统模式）

        Args:
            task: 任务信息字典

        Returns:
            bool: 处理是否成功
        """
        task_id = task['id']
        tracking_number = task['tracking_number']
        task_type = task['task_type']
        task_stage = task.get('task_stage', 'scraping')  # 兼容新字段

        print(f"[INFO] 开始处理任务: {task['task_name']} (ID: {task_id}, 阶段: {task_stage})")

        # 如果是AI分析阶段的任务但在传统模式下，跳过处理
        if task_stage == 'ai_analysis':
            print(f"[WARNING] 传统模式不支持AI分析阶段任务，跳过: {task_id}")
            return False

        try:
            # 更新任务状态为处理中
            self.task_manager.update_task_status(task_id, "processing")

            # 根据任务类型调用相应的处理方法
            if task_type == "bill_of_lading":
                result = self._process_bill_of_lading(task)
            elif task_type == "container":
                result = self._process_container(task)
            else:
                raise ValueError(f"不支持的任务类型: {task_type}")

            if result['success']:
                # 任务处理成功
                self.task_manager.update_task_status(
                    task_id,
                    "completed",
                    result_summary=result.get('summary', '处理完成')
                )
                print(f"[SUCCESS] 任务处理成功: {task['task_name']}")

                # 调用完成回调
                if self.completion_callback:
                    try:
                        self.completion_callback(task_id, result.get('data', {}))
                    except Exception as callback_error:
                        print(f"[WARNING] 回调函数执行失败: {callback_error}")

                return True
            else:
                # 任务处理失败
                self.task_manager.update_task_status(
                    task_id,
                    "completed",
                    error_message=result.get('error', '处理失败'),
                    result_summary="处理失败"
                )
                print(f"[ERROR] 任务处理失败: {task['task_name']} - {result.get('error', '未知错误')}")
                return False

        except Exception as e:
            error_msg = f"任务处理异常: {str(e)}"
            print(f"[ERROR] {error_msg}")
            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")

            # 更新任务状态为失败
            self.task_manager.update_task_status(
                task_id,
                "completed",
                error_message=error_msg,
                result_summary="处理异常"
            )
            return False
        finally:
            # 从活跃任务列表中移除
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]

    def _process_bill_of_lading(self, task: Dict) -> Dict:
        """
        处理提单号查询任务

        Args:
            task: 任务信息

        Returns:
            Dict: 处理结果
        """
        tracking_number = task['tracking_number']

        try:
            print(f"[DEBUG] 开始处理提单号: {tracking_number}")

            # 根据提单号获取承运人信息
            carrier_info = get_company_info(tracking_number)

            if carrier_info:
                # 使用检测到的承运人信息
                url = carrier_info.get('tracking_site', 'https://www.msc.com/en/track-a-shipment')
                input_element_id = carrier_info.get('input_element_id', '#trackingNumber')
                search_button_id = carrier_info.get('search_button_id', None)
                company_name = carrier_info.get('company', '未知')

                print(f"[INFO] 检测到承运人: {company_name}")
                print(f"[INFO] 使用追踪网站: {url}")
                print(f"[INFO] 输入框ID: {input_element_id}")
                print(f"[INFO] 搜索按钮ID: {search_button_id}")
            else:
                # 如果无法检测承运人，使用默认的MSC配置
                url = "https://www.msc.com/en/track-a-shipment"
                input_element_id = "#trackingNumber"
                search_button_id = None
                print(f"[WARNING] 无法检测承运人，使用默认MSC配置")

            # 调用视觉AI代理处理方法并接收返回值
            agent_result = run_visual_agent(tracking_number, url, input_element_id, search_button_id)

            # 如果run_visual_agent返回了结果，直接使用
            if agent_result and 'result_files' in agent_result and agent_result.get('ai_analysis'):
                result_files = agent_result['result_files']
                ai_result = agent_result['ai_analysis']
                parsed_data = self._parse_ai_result(ai_result)

                return {
                    'success': True,
                    'summary': f"提单号 {tracking_number} 查询完成，AI分析结果已保存",
                    'data': {
                        'estimated_arrival_time': parsed_data.get('estimated_arrival_time'),
                        'estimated_arrival_port': parsed_data.get('estimated_arrival_port'),
                        'dates_data': parsed_data.get('dates_data', []),
                        'ai_result': ai_result,
                        'result_files': result_files
                    }
                }
            else:
                # 备用方案：从文件管理器中查找（保持向后兼容）
                file_mgr = get_file_manager()
                tracking_files = file_mgr.get_files_for_tracking(tracking_number)

                if tracking_files:
                    # 获取最新的文件记录
                    latest_record = tracking_files[0]
                    ai_result_file = None
                    result_files = {}

                    # 查找各种文件
                    for file_info in latest_record['files']:
                        file_name = file_info['name']
                        file_path = file_info['path']

                        if file_name.startswith('ai_analysis_result_'):
                            ai_result_file = file_path
                            result_files['ai_analysis'] = file_path
                        elif file_name == 'final_result.png':
                            result_files['screenshot'] = file_path
                        elif file_name.startswith('page_content_original_'):
                            result_files['html_original'] = file_path
                        elif file_name.startswith('page_content_simplified_'):
                            result_files['html_simplified'] = file_path

                    # 检查是否有AI分析结果文件
                    if ai_result_file and os.path.exists(ai_result_file):
                        with open(ai_result_file, 'r', encoding='utf-8') as f:
                            ai_result = f.read()

                        # 解析AI分析结果，提取关键信息
                        parsed_data = self._parse_ai_result(ai_result)

                        return {
                            'success': True,
                            'summary': f"提单号 {tracking_number} 查询完成，AI分析结果已保存",
                            'data': {
                                'estimated_arrival_time': parsed_data.get('estimated_arrival_time'),
                                'estimated_arrival_port': parsed_data.get('estimated_arrival_port'),
                                'dates_data': parsed_data.get('dates_data', []),
                                'ai_result': ai_result,
                                'result_files': result_files,
                                'storage_info': {
                                    'folder_path': latest_record['folder_path'],
                                    'year_month': latest_record['year_month'],
                                    'created': latest_record['created'].strftime('%Y-%m-%d %H:%M:%S')
                                }
                            }
                        }
                    else:
                        # 找到了文件夹记录，但没有AI分析文件
                        return {
                            'success': False,
                            'error': f"未找到AI分析结果文件，但找到了存储文件夹: {latest_record['folder_path']}"
                        }
                else:
                    # 未找到任何文件记录
                    return {
                        'success': False,
                        'error': f"未找到提单号 {tracking_number} 的任何结果文件"
                    }

        except Exception as e:
            return {
                'success': False,
                'error': f"提单号查询处理失败: {str(e)}"
            }

    def _process_container(self, task: Dict) -> Dict:
        """
        处理箱号查询任务 - 与提单号处理逻辑相同
        """
        # 更新任务信息中的名称以反映这是箱号查询
        task_copy = task.copy()
        container_number = task_copy['tracking_number']
        
        # 调用相同的处理逻辑
        result = self._process_bill_of_lading(task_copy)
        
        # 更新返回结果中的描述
        if result.get('success') and result.get('summary'):
            result['summary'] = result['summary'].replace('提单号', '箱号')
        
        return result

    def _parse_ai_result(self, ai_result: str) -> Dict:
        """
        解析AI分析结果，提取关键信息（更健壮的JSON提取）
        """
        try:
            print(f"[INFO] 开始解析AI结果，内容长度: {len(ai_result)}")
            preview = ai_result[:500].replace('\n', ' ') if ai_result else ''
            print(f"[DEBUG] AI结果前500字符: {preview}...")

            data = None
            # 优先：不区分大小写查找 ```json ... ``` 代码块
            lower = ai_result.lower()
            start = lower.find('```json')
            if start != -1:
                end = lower.find('```', start + 7)
                if end != -1:
                    json_str = ai_result[start + 7:end].strip()
                    try:
                        data = json.loads(json_str)
                        print(f"[SUCCESS] JSON解析成功（fenced json），keys: {list(data.keys())}")
                    except json.JSONDecodeError as je:
                        print(f"[ERROR] JSON解析失败（fenced json）: {je}")
                        print(f"[DEBUG] 原始JSON字符串: {json_str[:300]}...")

            # 其次：查找任意 ``` ... ```，若内容以 { 开头则尝试解析
            if data is None:
                first_ticks = ai_result.find('```')
                if first_ticks != -1:
                    second_ticks = ai_result.find('```', first_ticks + 3)
                    if second_ticks != -1:
                        block = ai_result[first_ticks + 3:second_ticks].strip()
                        if block.lstrip().startswith('{'):
                            try:
                                data = json.loads(block)
                                print(f"[SUCCESS] JSON解析成功（generic fenced），keys: {list(data.keys())}")
                            except json.JSONDecodeError as je:
                                print(f"[ERROR] JSON解析失败（generic fenced）: {je}")

            # 再次：从全文中提取第一个大括号对象（简单栈匹配）
            if data is None:
                i = ai_result.find('{')
                if i != -1:
                    depth = 0
                    j = i
                    while j < len(ai_result):
                        ch = ai_result[j]
                        if ch == '{':
                            depth += 1
                        elif ch == '}':
                            depth -= 1
                            if depth == 0:
                                try:
                                    candidate = ai_result[i:j + 1]
                                    data = json.loads(candidate)
                                    print(f"[SUCCESS] JSON解析成功（braces scan），keys: {list(data.keys())}")
                                except Exception:
                                    pass
                                break
                        j += 1

            if data is None:
                print("[WARNING] 未能解析到有效JSON，返回空结果")
                return {}

            result = {
                'dates_data': [],
                'estimated_arrival_time': None,
                'estimated_arrival_port': None,
            }

            # 直接从AI结果中获取预计到港时间和港口（并做字段名兜底）
            eta = data.get('estimated_arrival_time')
            if not eta:
                # 兜底：寻找可能的同义字段
                for k in (
                    'eta', 'estimated_eta', 'estimated_arrival', 'arrival_eta',
                    'pod_eta', 'eta_date', 'estimatedArrivalTime', 'EstimatedArrivalTime'
                ):  # 大小写/驼峰兼容
                    if k in data and data[k]:
                        eta = data[k]
                        print(f"[DEBUG] 使用同义字段 '{k}' 作为 ETA")
                        break
            result['estimated_arrival_time'] = eta
            result['estimated_arrival_port'] = data.get('estimated_arrival_port') or data.get('pod') or data.get('port')
            print(f"[INFO] AI识别的预计到港时间: {result['estimated_arrival_time']}")
            print(f"[INFO] AI识别的预计到港港口: {result['estimated_arrival_port']}")

            # 提取时间节点数据
            dates_src = data.get('dates') or data.get('dates_data') or []
            if dates_src:
                print(f"[INFO] 找到时间节点数组，包含 {len(dates_src)} 个时间节点")
                for i, date_item in enumerate(dates_src):
                    print(f"  [INFO] 处理时间节点 {i+1}: {date_item.get('date')} - {date_item.get('type') or date_item.get('event_type')}")
                    # 转换为货运记录时间节点格式（容错源字段）
                    date_record = {
                        'date': date_item.get('date'),
                        'location': date_item.get('location', ''),
                        'event': date_item.get('description') or date_item.get('event', ''),
                        'status': date_item.get('status', ''),
                        'vessel_info': date_item.get('vessel_info', ''),
                        'event_type': date_item.get('type') or date_item.get('event_type', ''),
                    }
                    result['dates_data'].append(date_record)
            else:
                print(f"[WARNING] JSON数据中未找到dates/dates_data字段")

            # 若 ETA 缺失，尝试从日期节点推导
            if not result['estimated_arrival_time'] and result['dates_data']:
                derived = self._derive_eta_from_dates(result['dates_data'])
                if derived:
                    result['estimated_arrival_time'] = derived
                    print(f"[INFO] 从时间节点推导 ETA: {derived}")

            print(f"[SUCCESS] 解析AI结果成功，提取到 {len(result['dates_data'])} 个时间节点")
            if result['estimated_arrival_time']:
                print(f"📅 预计到港时间: {result['estimated_arrival_time']}")

            return result

        except Exception as e:
            print(f"[ERROR] 解析AI结果失败: {e}")
            import traceback
            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")
            return {}

    def _derive_eta_from_dates(self, dates: List[Dict]) -> Optional[str]:
        """从时间节点中推导 ETA（与 ShipmentManager 规则一致）"""
        try:
            if not dates:
                return None
            def norm(s: Optional[str]) -> str:
                return (s or '').strip().lower()
            # 1) arrival + 预计
            for d in dates:
                et = norm(d.get('event_type') or d.get('type'))
                st = norm(d.get('status'))
                if ('arrival' in et or 'eta' in et) and (st in ('estimated','estimate','预计','預計','eta')):
                    if d.get('date'):
                        return d['date']
            # 2) 描述包含关键词 + 预计
            keywords = ('eta','estimated time of arrival','到港','抵达','抵達')
            for d in dates:
                desc = norm(d.get('event') or d.get('description'))
                st = norm(d.get('status'))
                if any(k in desc for k in keywords) and (st in ('estimated','estimate','预计','預計','eta')):
                    if d.get('date'):
                        return d['date']
            # 3) 任意 arrival/到港/抵达
            for d in dates:
                et = norm(d.get('event_type') or d.get('type'))
                desc = norm(d.get('event') or d.get('description'))
                if ('arrival' in et) or any(k in desc for k in ('到港','抵达','抵達')):
                    if d.get('date'):
                        return d['date']
            return None
        except Exception:
            return None

    def worker_thread(self):
        """
        工作线程，持续处理任务
        """
        while self.running:
            try:
                # 检查是否有空闲的处理槽位
                if len(self.active_tasks) >= self.max_concurrent_tasks:
                    time.sleep(1)
                    continue

                # 获取待处理任务（只获取抓取阶段的任务，兼容传统模式）
                if self.use_two_stage:
                    # 两阶段模式下由两阶段处理器处理
                    time.sleep(self.check_interval)
                    continue
                else:
                    # 传统模式：只处理抓取阶段的任务
                    pending_tasks = self.task_manager.get_pending_tasks_by_stage('scraping', limit=1)

                if not pending_tasks:
                    time.sleep(self.check_interval)
                    continue

                task = pending_tasks[0]
                task_id = task['id']

                # 添加到活跃任务列表
                self.active_tasks[task_id] = {
                    'task': task,
                    'start_time': datetime.now()
                }

                # 直接在当前线程中处理任务
                self.process_single_task(task)

            except Exception as e:
                print(f"[ERROR] 工作线程异常: {e}")
                time.sleep(5)  # 异常后等待5秒再继续

    def start(self):
        """
        启动任务处理器
        """
        if self.running:
            print("[WARNING] 任务处理器已在运行中")
            return

        self.running = True

        if self.use_two_stage:
            # 使用两阶段处理器
            print(f"[INFO] 启动两阶段任务处理器")
            self.two_stage_processor.start()
        else:
            # 使用传统单阶段处理器
            print(f"[INFO] 启动传统任务处理器 (最大并发: {self.max_concurrent_tasks}, 检查间隔: {self.check_interval}秒)")
            
            # 启动工作线程
            for i in range(self.max_concurrent_tasks):
                worker = threading.Thread(
                    target=self.worker_thread,
                    name=f"TaskWorker-{i+1}"
                )
                worker.daemon = True
                worker.start()
                self.worker_threads.append(worker)

            print(f"[SUCCESS] 传统任务处理器启动成功，{len(self.worker_threads)} 个工作线程已启动")

    def stop(self):
        """
        停止任务处理器
        """
        if not self.running:
            print("[WARNING] 任务处理器未在运行")
            return

        print("[STOP] 正在停止任务处理器...")
        self.running = False

        if self.use_two_stage:
            # 停止两阶段处理器
            self.two_stage_processor.stop()
        else:
            # 停止传统处理器
            # 等待所有工作线程结束
            for worker in self.worker_threads:
                worker.join(timeout=10)

            self.worker_threads.clear()

        print("[SUCCESS] 任务处理器已停止")

    def get_status(self) -> Dict:
        """
        获取处理器状态

        Returns:
            Dict: 状态信息
        """
        if self.use_two_stage:
            # 返回两阶段处理器状态
            return self.two_stage_processor.get_status()
        else:
            # 返回传统处理器状态
            return {
                'running': self.running,
                'mode': 'traditional',
                'active_tasks_count': len(self.active_tasks),
                'max_concurrent_tasks': self.max_concurrent_tasks,
                'worker_threads_count': len(self.worker_threads),
                'active_tasks': {
                    task_id: {
                        'task_name': info['task']['task_name'],
                        'start_time': info['start_time'].isoformat(),
                        'duration_seconds': (datetime.now() - info['start_time']).total_seconds()
                    }
                    for task_id, info in self.active_tasks.items()
                }
            }


def run_task_processor_daemon(use_two_stage: bool = True):
    """
    以守护进程模式运行任务处理器
    
    Args:
        use_two_stage: 是否使用两阶段处理模式
    """
    if use_two_stage:
        processor = TaskProcessor(max_concurrent_tasks=2, check_interval=3, use_two_stage=True)
        print("[INFO] 使用两阶段任务处理模式")
    else:
        processor = TaskProcessor(max_concurrent_tasks=1, check_interval=3, use_two_stage=False)
        print("[INFO] 使用传统单阶段任务处理模式")

    try:
        processor.start()

        if use_two_stage:
            print("[INFO] 两阶段任务处理器状态监控 (按 Ctrl+C 停止)")
        else:
            print("[INFO] 传统任务处理器状态监控 (按 Ctrl+C 停止)")
        print("=" * 60)

        while True:
            time.sleep(10)

            # 显示状态信息
            status = processor.get_status()
            
            if use_two_stage:
                stage_stats = processor.task_manager.get_stage_statistics()
                print(f"\n[TIME] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"[网页抓取] 活跃任务: {status['scraping']['active_tasks_count']}/{status['scraping']['max_concurrent_tasks']}")
                print(f"[AI分析] 活跃任务: {status['ai_analysis']['active_tasks_count']}/{status['ai_analysis']['max_concurrent_tasks']}")
                
                if stage_stats:
                    print(f"[统计] 抓取阶段: {stage_stats.get('scraping', {})}")
                    print(f"[统计] AI分析阶段: {stage_stats.get('ai_analysis', {})}")
            else:
                stats = processor.task_manager.get_task_statistics()
                print(f"\n[TIME] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"[INFO] 活跃任务: {status['active_tasks_count']}/{status['max_concurrent_tasks']}")
                print(f"[INFO] 任务统计: {stats.get('status_distribution', {})}")

                if status['active_tasks']:
                    print("[DEBUG] 正在处理的任务:")
                    for task_id, task_info in status['active_tasks'].items():
                        print(f"  - {task_info['task_name']} (运行 {task_info['duration_seconds']:.0f}秒)")

    except KeyboardInterrupt:
        print("\n[STOP] 收到停止信号，正在关闭任务处理器...")
    finally:
        processor.stop()
        print("👋 任务处理器已关闭")


if __name__ == "__main__":
    import sys
    
    # 从命令行参数判断是否使用两阶段模式
    use_two_stage = True
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['traditional', 'single', 'old']:
            use_two_stage = False
            print("强制使用传统单阶段模式")
    
    # 运行任务处理器守护进程
    run_task_processor_daemon(use_two_stage=use_two_stage)