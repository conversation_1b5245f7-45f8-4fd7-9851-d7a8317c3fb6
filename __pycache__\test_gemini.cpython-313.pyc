�

    |S�h#a  �            
       �Z  � S SK r S SKrS SKrS SKJr  S SKJrJr  S SKJ	r	J
r
  S SKrS SKJ
r
Jr  S SKrS SKJr  S SKJr  Sr\S	:X  d  \(       d  \" S
5        \" 5         \" S\S9rS
\S\S\S\4S jrS\S\4S jrS\S\S\4S jrS\S\S\S\S\4
S jrS\4S jr \!S:X  a  Sr"\ " \"5        gg)�    N)�OpenAI)�sync_playwright�expect)�Image�	ImageDraw)�
BeautifulSoup�Comment)�datetime)�log_ai_call_simplez$f1510fc9-df05-4cc7-b21f-85f7249800b6u$   在此处填入您的真实API密钥u4   错误：请在代码中设置 ARK_API_KEY 变量。�(https://ark.cn-beijing.volces.com/api/v3��base_url�api_key�image_base64�task_prompt�history�returnc                 ��  � [        S5        U(       a%  SR                  U Vs/ s H  nSU 3PM
     sn5      OSn [        R                  " 5       n[        R
                  R                  R                  SSSS.S	S
SSU  30S
.SSU SU S3S./S./SS9n[        R                  " 5       n [        SUU[        US5      (       a  UR                  R                  OS[        US5      (       a  UR                  R                  OS[        US5      (       a  UR                  R                  OSSSSSS9
  UR                  S   R                   R"                  n	[        SU	 35        U	R%                  S5      (       a  U	S S! R'                  5       n	[(        R*                  " U	5      n
U
$ s  snf ! [         a  n[        SU 35         SnAN�SnAff = f! [         a�  n[        S"U 35         [        R                  " 5       n[        SS#[-        5       ;   a  WOUUSSSSSSS$[/        U5      S%9  O%! [         a  n[        SU 35         SnAOSnAff = fS&S'S(.s SnA$ SnAff = f))u2  
调用豆包视觉大模型API，分析截图并返回下一步操作指令。

:param image_base64: 当前页面的Base64编码截图。
:param task_prompt: 当前需要完成的核心任务。
:param history: 之前的操作历史，为AI提供上下文。
:return: 一个包含操作指令的字典。
u&   🤖 AI 正在分析截图并决策...�
�- u   无zdoubao-1.5-vision-pro-250328�systemu  
                    你是一个专业的网页自动化AI助手。你的任务是分析用户提供的网页截图和指令，
                    然后决定下一步应该执行什么操作。
                    你的回答必须且只能是一个JSON对象，格式如下：
                    {"action": "type" | "click" | "read" | "finish", "x": 数字, "y": 数字, "text_to_type": "要输入的文本（仅当action为type时）"}
                    - action: 'type'表示输入, 'click'表示点击, 'read'表示读取信息, 'finish'表示任务结束。
                    - x: 要操作的屏幕x坐标位置（必须是纯数字，如498）。
                    - y: 要操作的屏幕y坐标位置（必须是纯数字，如400）。
                    - text_to_type: 仅在输入时提供要输入的具体文本。
                    重要：x和y必须是独立的数字字段，不能包含空格或其他字符。
                    注意：请仔细观察截图，准确给出坐标位置。
                    ��role�content�user�	image_url�urlzdata:image/png;base64,)�typer   �textup   
                            这是当前网页的截图。
                            我的主要任务是: "u]   "
                            我已经执行过的历史操作:
                            u�   
                            请根据截图和我的任务，告诉我下一步应该做什么。请务必以JSON格式返回。
                            )r   r   g�������?)�model�messages�temperature�usager   �visual_automation�visual_agent�image�success�
r    �request_time�
response_time�
prompt_tokens�completion_tokens�total_tokens�business_id�	caller_id�content_type�status�   ⚠️ 数据库记录失败: Nu   🧠 AI 返回的原始决策: z```json�   �����u$   ❌ 调用AI或解析JSON时出错: r)   �error�r    r)   r*   r+   r,   r-   r.   r/   r0   r1   �
error_message�finishu   AI调用失败)�action�element_description)�print�joinr
   �now�client�chat�completions�creater   �hasattrr#   r+   r,   r-   �	Exception�choices�messager   �
startswith�strip�json�loads�locals�str)r   r   r   �h�history_strr)   �responser*   �db_error�ai_response_text�command�es               �"d:\container-helper\test_gemini.py�
get_ai_actionrT      s�  � � 
�
2�3� =D�$�)�)�w�7�w�!�r�!��X�w�7�8��K�fM��|�|�~���;�;�*�*�1�1�0� %� ��  #� %0� !&�)?��~�'N�*�� %+�)5�5@�M� B�(�M� *�	% �	� ��!&�P �U 2� +
��\ !����
�	?��4�)�+�>E�h�PW�>X�>X�h�n�n�:�:�^_�FM�h�X_�F`�F`�(�.�.�"B�"B�fg�<C�H�g�<V�<V�X�^�^�8�8�\]�/�(�$� �
� $�+�+�A�.�6�6�>�>��
�/�0@�/A�B�C� �&�&�y�1�1�/��"�5�;�;�=���*�*�-�.�����a 8��H � 	?��2�8�*�=�>�>��	?�� � M�
�4�Q�C�8�9�	?�$�L�L�N�M��4�-;�v�x�-G�\�]�+��"#��/�(�$��!�!�f�
�� � 	?��2�8�*�=�>�>��	?�� #�;K�L�L��-M�sz   �F$�A/G �+BF) �2A1G �)
G�3G�G �G�G �
I�I�'AH)�(I�)
I�3I�I�I�I�I�I�html_contentc                 ��  ^� U (       d  g[        U S5      n/ SQnU H+  nUR                  U5       H  nUR                  5         M     M-     UR                  S S9 H  nUR                  5         M     [	        5       nUR                  / SQ5       Hs  nUR                  U5        UR                  nU(       d  M)  UR                  S;   d  M;  UR                  U5        UR                  nU(       d  Ma  UR                  S;   a  M8  Mu     SS	/n	U	 H�  n
UR                  [        R                  " U
5      S9 HY  nUR                  n[        S
5       H;  n
U(       a0  UR                  (       a  UR                  U5        UR                  nM:    MW     M[     M�     UR                  S5       H�  nUR                  (       d  M  UR                   Hf  m[        U4S jS
 5       5      (       d  M  UR                  U5        UR                  nU(       a"  UR                  (       a  UR                  U5          M�     M�     Sn Sn[        UR                  S5      5       HL  nXF;   a  M
  UR                  S;   a  M  UR                  SS9(       a  M2  UR                  5         US-
  nUS-
  nMN     US:X  a  OMu  UR                  S5       H�  nUR                  (       d  M  / SQ/ SQ/ SQSS/S/S/SS/S/SS/S.	nUR                  U;   aY  [        UR                  5      n[!        UR#                  5       5       H%  mTUUR                     ;  d  M  UR                  T	 M'     M�  [        UR                  5      n[!        UR#                  5       5       H,  m[        U4S jS 5       5      (       a  M  UR                  T	 M.     M�     UR                  S5       HG  nUR%                  S5      nUR'                  U5        UR%                  S 5      nUR)                  U5        MI     S!R+                  S" UR-                  5       R/                  S!5       5       5      n[1        S#U S$35        [1        S%[3        U5       S&35        U$ )'u�   
专门为物流追踪页面优化的智能HTML简化函数
保留表格结构和上下文信息，确保日期数据不失去含义
� zhtml.parser)�script�style�head�meta�link�title�header�footer�nav�aside�form�svg�button�iframe�noscript�canvas�templatec                 �"   � [        U [        5      $ �N)�
isinstancer	   )r   s    rS   �<lambda>�0simplify_html_for_logistics_ai.<locals>.<lambda>�   s   � �Z��g�5N�    )�string)�table�thead�tbody�tr�th�td)�div�section�articlez\d{1,2}[/\-]\d{1,2}[/\-]\d{4}z\d{4}[/\-]\d{1,2}[/\-]\d{1,2}�   Tc              3   �H   >#   � U  H  oTR                  5       ;   v �  M     g 7frj   ��lower��.0�keyword�attrs     �rS   �	<genexpr>�1simplify_html_for_logistics_ai.<locals>.<genexpr>�   s   �� � �s�>r�7�$�*�*�,�.�>r��   �")�x-text�
data-value�	data-date�data-statusr   )	�img�br�inputrp   rq   rr   rs   rt   ru   �rG   �   )r�   r�   r�   �class)r�   r�   r�   r�   )r�   r�   r�   r�   r�   r�   �href�src�alt)	�spanrv   ru   rt   rs   rp   �p�ar�   c              3   �H   >#   � U  H  oTR                  5       ;   v �  M     g 7frj   r{   r}   s     �rS   r�   r�   �   s   �� � �n�Fm�7�$�*�*�,�6�Fm�r�   )�datar   �value�datezx-rp   z 
<!-- LOGISTICS_TABLE_START -->
z
<!-- LOGISTICS_TABLE_END -->
r   c              3   �R   #   � U  H  oR                  5       (       d  M  Uv �  M     g 7frj   r�   )r~   �lines     rS   r�   r�     s   � � �]�1L��PZ�PZ�P\���1L�s   �'�	'u   🗑️ 移除了 u
    个空标签u   🛡️ 保护了 u    个重要结构标签)r   �find_all�	decompose�extract�set�add�parent�name�re�compile�range�attrs�any�reversed�get_text�dict�list�keys�
new_string�
insert_before�insert_afterr<   �prettify�splitr;   �len)rU   �soup�tags_to_remove�tag_name�tag�comment�important_structure_tags�	table_tagr�   �
date_patterns�pattern�element�current�_�
removed_count�removed_in_pass�
allowed_attrs�
current_attrsrp   �end_comment�simplified_htmlr�   s                        @rS   �simplify_html_for_logistics_air�   �   s�  �� �
 ����}�5�D��N�
 #���=�=��*�C��M�M�O� +� #�
 �=�=�(N�=�O������ P�  #�u�� �]�]�#P�Q�	� �$�$�Y�/��!�!���f����(E�E�$�(�(��0��]�]�F� �f����(E�E�	 R� 6�7W�X�M� ���}�}�B�J�J�w�,?�}�@�G��n�n�G��1�X���w�|�|�,�0�0��9�%�n�n�G�� � A� !� �}�}�T�"���9�9�9��	�	���s�>r�s�s�s�,�0�0��5� �Z�Z�F��&�+�+�0�4�4�V�<�� "� #� �M�
����D�M�M�$�/�0�C��.�� �x�x�^�^�� �<�<�d�<�+�+��
�
���1�$����"�
� 1� �a���% �* �}�}�T�"���8�8�8� G�J�:�#�W�-��i�!��"�K�0��X��u�~�
�M� �x�x�=�(� $�S�Y�Y��
� ��!3�!3�!5�6�D��=����#:�:��I�I�d�O� 7�
 !%�S�Y�Y��
� ��!3�!3�!5�6�D��n�Fm�n�n�n��I�I�d�O� 7�/ #�: ���w�'���/�/�"F�G��
���G�$��o�o�&H�I��
���;�'� (� �i�i�]�����1F�1F�t�1L�]�]�O�	��}�o�]�
;�<�	��s�#;�<�=�=S�
T�U��rn   �	bl_numberc                 �<  �  [        S5        [        U 5      n[        S[        U 5       S[        U5       35        [        S[        S9nSU S3n[
        R                  " 5       nUR                  R                  R                  SS	US
./S9n[
        R                  " 5       n [        SUU[        US5      (       a  UR                  R                  OS
[        US5      (       a  UR                  R                  OS
[        US5      (       a  UR                  R                  OS
USSSS9
  SU S3n	[#        U	SSS9 n
U
R%                  UR&                  S
   R(                  R*                  5        SSS5        [        SU	 35        SU S3n[#        USSS9 n
U
R%                  S5        U
R%                  S[-        U5       S35        U
R%                  SU S35        [        US5      (       a�  U
R%                  S 5        U
R%                  S!UR                  R                   S35        U
R%                  S"UR                  R                   S35        U
R%                  S#UR                  R                   S35        U
R%                  S$5        / S%QnU HN  n
[        Xm5      (       a&   [/        Xm5      nU
R%                  S&U
 S'U S35        M9  U
R%                  S&U
 S*35        MP     SSS5        [        S+U 35        UR&                  S
   R(                  R*                  $ ! [          a  n[        SU 35         SnAGN
SnAff = f! , (       d  f       GN�= f! [          a#  nU
R%                  S&U
 S(U S)35         SnAM�  SnAff = f! , (       d  f       N�= f! [          a�  n[        S,U 35         [
        R                  " 5       n[        SS-[1        5       ;   a  WOUUS
S
S
USSS.[3        U5      S/9  O%! [          a  n[        SU 35         SnAOSnAff = f SnAgSnAff = f)0uK   使用文字AI分析页面HTML内容，提取提单号的各个日期信息u   🔧 正在简化HTML内容...u#   📊 HTML简化完成，原长度: u
   , 简化后: r   r
   uG
  
        你是一个专业的物流追踪信息分析专家。请分析以下经过结构化处理的HTML内容，提取所有与船期、物流相关的日期信息。

        **HTML结构说明：**
        - 表格区域用 <!-- LOGISTICS_TABLE_START --> 和 <!-- LOGISTICS_TABLE_END --> 标记
        - 保留了完整的表格结构（table、tr、td、th等）以维持上下文关系
        - 日期信息通常在 x-text 属性或标签文本中

        **分析要求：**
        1. **上下文理解**：
           - 重点关注表格结构中的日期信息
           - 通过表头（th）、行标签、相邻单元格理解日期含义
           - 识别日期所在行的完整信息（位置、描述、状态等）

        2. **日期类型识别**：
           - 起运港相关：ETD (Estimated Time of Departure), ATD (Actual Time of Departure), 装船日期
           - 目的港相关：ETA (Estimated Time of Arrival), ATA (Actual Time of Arrival), POD ETA, 到港日期
           - 其他重要日期：转运日期、Price Calculation Date、Latest move日期、Full Transshipment Loaded等

        3. **语言支持**：
           - 支持识别中文日期描述（如："预计到港时间"、"实际离港时间"等）
           - 支持英文及船期专业缩写（如：POD ETA、ETD、ATA、Full Transshipment Loaded等）
           - 支持混合语言环境

        4. **智能解析**：
           - 从HTML的data-value、x-text等属性中提取日期
           - 识别表格、列表中的日期信息及其上下文
           - 理解日期的完整含义（通过表格行的其他列信息）
           - 提取相关的地点、船只、状态信息

        5. **输出格式**：
           请按以下JSON格式输出，按时间倒序排列（最新日期在前）：
           ```json
           {
             "dates": [
               {
                 "date": "YYYY-MM-DD",
                 "original_format": "原始格式（如：28/07/2025）",
                 "type": "日期类型（如：POD_ETA、ETD、转运、装船等）",
                 "location": "地点（如：Shanghai CN、Rodman PA等）",
                 "description": "详细描述（如：Estimated Time of Arrival、Full Transshipment Loaded等）",
                 "status": "状态（如：estimated、actual、completed等）",
                 "vessel_info": "船只信息（如有）",
                 "context": "从表格行中提取的完整上下文信息"
               }
             ]
           }
           ```

        **待分析的HTML内容：**
        u�   

        请仔细分析表格结构，确保每个日期都包含完整的上下文信息，不要遗漏任何重要的物流日期。
        zdoubao-seed-1-6-flash-250615r   r   )r    r!   r#   r   �logistics_systemr   r'   r(   r2   N�ai_analysis_result_�.txt�w�utf-8��encodingu!   💾 AI分析结果已保存到: �full_api_response_u   完整API响应对象:
u   响应类型: r   u   响应对象: z

u   Token使用情况:
u   - 提示token: u   - 完成token: u   - 总计token: u   响应对象的重要属性:
)�id�object�createdr    rD   r#   �system_fingerprintr   z: u   : 无法获取 (z)
u   : 属性不存在
u"   📊 完整API响应已保存到: u   AI分析出错: r)   r5   r6   )r;   r�   r�   r   �ARK_API_KEYr
   r=   r?   r@   rA   r   rB   r#   r+   r,   r-   rC   �open�writerD   rE   r   r   �getattrrJ   rK   )rU   r�   r�   �text_client�promptr)   rN   r*   rO   �ai_result_file�f�full_response_file�important_attrsr�   r�   rR   s                   rS   �analyze_shipment_datesr�     s  � �^�
�.�/�8��F��
�3�C��4E�3F�m�TW�Xg�Th�Si�j�k� �?��
��
2	�d 
�� 	�e5��p  �|�|�~���#�#�/�/�6�6�0� #�%��� 7� 
�� !����
�	?��4�)�+�>E�h�PW�>X�>X�h�n�n�:�:�^_�FM�h�X_�F`�F`�(�.�.�"B�"B�fg�<C�H�g�<V�<V�X�^�^�8�8�\]�%�,�#� �
�  /�y�k��>��
�.�#��
8�A�
�G�G�H�$�$�Q�'�/�/�7�7�8� 9�
�1�.�1A�B�C�  2�)��D�A��
�$�c�G�
<��
�G�G�.�0�
�G�G�n�T�(�^�$4�B�7�8�
�G�G�n�X�J�d�3�4� �x��)�)����.�0����/�(�.�.�*F�*F�)G�r�J�K����/�(�.�.�*J�*J�)K�2�N�O����/�(�.�.�*E�*E�)F�d�K�L� 
�G�G�4�6�l�O�'���8�*�*�C� '�� 7�����"�T�F�"�U�G�2� 6�7� �G�G�b���&9�:�;� (� =�2 	�2�3E�2F�G�H�����"�*�*�2�2�2��M � 	?��2�8�*�=�>�>��	?��
 9�
8��4 %� C����"�T�F�*:�1�#�S� A�B�B��C��) =�
<��8 � �
� ���$�%�	?�$�L�L�N�M��4�-;�v�x�-G�\�]�+��"#��%�,�#��!�!�f�
�� � 	?��2�8�*�=�>�>��	?�� ��-�s�   �BN �"BL �)N �:3L>�-'N �DN �!#M�N �8N �
L;�"L6�0N �6L;�;N �>
M
�N �
M=�M8�2N �8M=�=N � 
N�
N �
P�P�*AO,�+P�,
P�6P	�P�	P�P�P�image_bytes�x�yr9   c                 ��  � [         R                  " [        R                  " U 5      5      n[        R
                  " U5      nUS:X  a  SO	US:X  a  SOSnSnUR
                  X-
  X'-
  X-   X'-   /USS9  UR                  US	-
  X!S	-   U/US
S9  UR                  XS	-
  XS	-   /US
S9  UR                  US-   US-
  4S
U SU S3US9  [        R                  " 5       nUR                  USS9  UR                  5       $ )u)   在截图上标记AI识别的坐标位置�click�redr   �blue�green�
   ry   )�outline�width�   �   )�fillr�   �   �(�,�))r�   �PNG)�format)r   r�   �io�BytesIOr   �Draw�ellipser�   r   �save�getvalue)	r�   r�   r�   r9   r&   �draw�color�radius�outputs	            rS   �mark_coordinates_on_imager�   �  s  � � 
�J�J�r�z�z�+�.�/�E��>�>�%� �D� �w�&�E�f��6F�F�G�E� �F��L�L�!�(�A�H�a�h���9�5�PQ�L�R� 	�I�I�q��t�Q�"��a� �u�A�I�6��I�I�q�B�$��R�4� �u�A�I�6� 	�I�I�q��t�Q�r�T�l�a��s�!�A�3�a�L�u�I�5� �Z�Z�\�F�	�J�J�v�e�J�$��?�?��rn   c                 �  � [        5        nUR                  R                  SS9nUR                  5       n [	        S5        UR                  SSS9  [	        S5        UR
                  S5         UR                  S	5      nUR                  5       S
:�  a  UR                  5         [	        S5        UR
                  S5        UR                  S
5      nUR                  SSS9  UR                  5         UR                  U 5        [	        SU  35        UR                  R                  S5        [	        S5        [	        S5        UR
                  S5        SnUR                  USS9  [	        SU 35        [	        S5        UR                  5       nSU  S3nSU  S3n	[!        USSS 9 n
U
R#                  U5        S!S!S!5        [	        S"U 35        [%        U5      n[!        U	SSS 9 n
U
R#                  U5        S!S!S!5        [	        S#U	 35        [	        S$['        U5      ['        U5      -  S%-  S& S'35        [)        Xp5      nU(       a�  [	        S(U 35        S)U  S3n
[!        U
SSS 9 n
U
R#                  S*U  S+35        U
R#                  S,[+        S-5      R,                  R/                  5       R1                  S.5       S+35        U
R#                  S/5        U
R#                  U5        S!S!S!5        [	        S0U
 35        O[	        S15          UR3                  5         [	        S25        S!S!S!5        g!!    GN�= f! , (       d  f       GN�= f! , (       d  f       GNU= f! , (       d  f       Nz= f!   [	        S35         NU= f! [4         aO  n[	        S4U 35         S5nUR                  USS9  [	        S6U 35         S!nAO!   [	        S75          S!nAO= fS!nAff = f UR3                  5         [	        S25        OE!   [	        S35         O6= f!  UR3                  5         [	        S25        f !   [	        S35         f = f= fS!S!S!5        g!! , (       d  f       g!= f)8u.   
运行视觉AI代理，自动化网页操作
F)�headlessu   ➡️ 导航到MSC网站...z'https://www.msc.com/en/track-a-shipmenti`�  )�timeoutu   ➡️ 处理Cookie弹窗...i�  z#onetrust-accept-btn-handlerr   u+   ➡️ 定位输入框并输入提单号...i�  z#trackingNumber�visiblei'  )�stater�   u   ✅ 已输入提单号: �Enteru   ✅ 已按回车触发查询u   ⏳ 等待查询结果...i0u  zfinal_result.pngT)�path�	full_pageu'   ✅ 查询结果长截图已保存为: u%   🤖 正在用AI分析页面内容...�page_content_original_r�   �page_content_simplified_r�   r�   r�   Nu#   📄 原始HTML内容已保存为: u#   📄 简化HTML内容已保存为: u   📊 HTML压缩率: �d   z.1f�%u   📋 AI分析结果:
r�   u   提单号: r   u   分析时间: r
   z%Y-%m-%d %H:%M:%Sz3==================================================
u!   📄 AI分析结果已保存为: u   ❌ AI分析失败u   ✅ 浏览器已关闭u%   ⚠️ 关闭浏览器时出现问题u+   💥 自动化流程中发生严重错误: zerror_screenshot.pngu"   📸 错误长截图已保存为: u   ⚠️ 无法保存错误截图)r   �chromium�launch�new_pager;   �goto�wait_for_timeout�locator�countr�   �wait_forr�   �keyboard�press�
screenshotr   r�   r�   r�   r�   r�   �
__import__r
   r=   �strftime�closerC   )r�   r�   �browser�page�
cookie_button�
input_locator�final_screenshot_path�	page_html�original_html_path�simplified_html_pathr�   �simplified_html_preview�ai_analysis�ai_result_pathrR   �error_screenshot_paths                   rS   �run_visual_agentr"  �  s  � � 
�	�a��*�*�#�#�U�#�3�����!��a	?��0�1��I�I�?��I�O� 
�0�1��!�!�$�'�
� $���-K� L�
� �&�&�(�1�,�!�'�'�)�
 
�?�@��!�!�$�'� !�L�L�):�;�M� 
�"�"��E�"�B� 
���!� 
���y�)��,�Y�K�8�9� 
�M�M����(��0�1� 
�-�.��!�!�%�(� %7�!��O�O�!6�$�O�G��;�<Q�;R�S�T� 
�9�:�����I� $:�)��D�!I��%=�i�[��#M� ��(�#��@�A����	�"� A��7�8J�7K�L�M� 'E�Y�&O�#��*�C�'�B�a����/�0� C��7�8L�7M�N�O��(��-D�)E�c�)�n�)T�UX�)X�Y\�(]�]^�_�`�0��F�K���.�{�m�<�=� $7�y�k��!F���.�#��@�A��G�G�k�)��B�7�8��G�G�n�Z�
�-C�-L�-L�-P�-P�-R�-[�-[�\o�-p�,q�qs�t�u��G�G�O�,��G�G�K�(�	 A�
 �9�.�9I�J�K��*�+��
?��
�
���.�/�G 
�	��$
���P A�@�� C�B�� A�@��0
?��=�>�� � 	9��?��s�C�D�
9�(>�%����%:�d��K��:�;P�:Q�R�S�S��
9��7�8�8��	9��
?��
�
���.�/��
?��=�>��	
?��
�
���.�/��
?��=�>��K 
�	�	�s�   �*P=�7M6�.5L'�#D M6�#L/�5,M6�!M�3A/M6�"A=M�"M6�M$�'L,�)M6�/
L>	�9M6�
M	�M6�
M!	�M6�$
M3�1P=�6
O� O
� N4�/P�4
O�O
�P�O
�
O�P�O/�.P=�/
O>�<P=�P1�P�P1�
P.	�,P1�1P=�=
Q�__main__�MEDUJ0618622)#�os�base64rH   �openair   �playwright.sync_apir   r   �PILr   r   r�   �bs4r   r	   r�   r
   �	db_loggerr   r�   r;   �exitr>   rK   r�   r�   rT   r�   r�   �bytes�intr�   r"  �__name__�bill_of_lading_number� rn   rS   �<module>r2     s  �� 	� 
� � � 7�  � 	� &� 	� � (� 5���8�8��	�
@�A��F�	�
7��
��tM�� tM�#� tM�� tM�� tM�n~�� ~�� ~�B`�� `�� `�� `�F�5� �S� �S� �#� �RW� �4i?�� i?�V �z��*���*�+� rn   