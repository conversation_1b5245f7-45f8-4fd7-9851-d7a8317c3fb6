#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
船公司数据迁移脚本
将 carrier_lookup.py 中的 COMPANY_DATA 字典迁移到数据库
"""

import sqlite3
import sys
import os

# 添加父目录到路径，以便导入 utils 模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.carrier_lookup import COMPANY_DATA

def migrate_data():
    """迁移 COMPANY_DATA 到数据库"""
    db_path = 'db/carriers.db'
    
    if not os.path.exists(db_path):
        print("错误: db/carriers.db 不存在，请先运行 init_carrier_database.py")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 清空现有数据
    cursor.execute("DELETE FROM carrier_bl_patterns")
    cursor.execute("DELETE FROM carrier_scac_prefixes")
    cursor.execute("DELETE FROM carriers")
    
    print(f"开始迁移 {len(COMPANY_DATA)} 个船公司数据...")
    
    migrated_count = 0
    
    for company_name, company_info in COMPANY_DATA.items():
        try:
            # 提取公司代码（从公司名称中提取英文缩写）
            company_code = extract_company_code(company_name)
            
            # 提取或推断输入元素ID和搜索按钮ID
            input_element_id, search_button_id = extract_ui_elements(company_name, company_info)
            
            # 插入船公司主记录
            cursor.execute("""
                INSERT INTO carriers (company_name, company_code, international_site, chinese_site, tracking_site, input_element_id, search_button_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                company_name,
                company_code,
                company_info.get("international_site"),
                company_info.get("chinese_site"),
                company_info.get("tracking_site"),
                input_element_id,
                search_button_id
            ))
            
            carrier_id = cursor.lastrowid
            
            # 插入SCAC前缀
            scac_prefixes = company_info.get("scac_prefixes", [])
            for scac in scac_prefixes:
                cursor.execute("""
                    INSERT INTO carrier_scac_prefixes (carrier_id, scac_prefix)
                    VALUES (?, ?)
                """, (carrier_id, scac))
            
            # 插入提单号规则
            bl_patterns = company_info.get("bl_patterns", [])
            for pattern in bl_patterns:
                cursor.execute("""
                    INSERT INTO carrier_bl_patterns (carrier_id, pattern)
                    VALUES (?, ?)
                """, (carrier_id, pattern))
            
            migrated_count += 1
            print(f"✓ 已迁移: {company_name} (SCAC: {len(scac_prefixes)}, 规则: {len(bl_patterns)})")
            
        except Exception as e:
            print(f"✗ 迁移失败: {company_name} - {str(e)}")
    
    conn.commit()
    conn.close()
    
    print(f"\n迁移完成！成功迁移 {migrated_count}/{len(COMPANY_DATA)} 个船公司")
    return True

def extract_company_code(company_name):
    """从公司名称中提取公司代码"""
    # 提取括号中的英文缩写
    if "(" in company_name and ")" in company_name:
        start = company_name.find("(")
        end = company_name.find(")", start)
        if start != -1 and end != -1:
            content = company_name[start+1:end]
            # 提取第一个英文部分作为代码
            parts = content.split()
            for part in parts:
                if part.isalpha() and part.isupper():
                    return part
    
    # 如果没有找到，使用公司名称的第一个单词
    first_word = company_name.split()[0]
    return first_word.upper()

def extract_ui_elements(company_name, company_info):
    """根据公司名称和信息推断UI元素ID"""
    tracking_site = company_info.get("tracking_site", "")
    
    # 根据不同船公司的网站特征推断UI元素
    ui_mapping = {
        "MSC": ("#trackingNumber", None),  # MSC使用回车键
        "Maersk": ("#tracking-input", "#tracking-search-btn"),
        "CMA CGM": ("#container-number", "#search-button"),
        "COSCO": ("#cargoTrackingInput", "#cargoTrackingBtn"),
        "Hapag-Lloyd": ("#tracking-input", "#search-btn"),
        "ONE": ("#trackingNumber", "#searchButton"),
        "Evergreen": ("#containerNumber", "#searchBtn"),
        "HMM": ("#trackingInput", "#trackingBtn"),
        "Yang Ming": ("#containerNo", "#searchButton"),
        "ZIM": ("#tracking-number", "#track-button"),
        "OOCL": ("#trackingNumber", "#trackButton"),
        "PIL": ("#containerNumber", "#searchBtn"),
        "Wan Hai": ("#trackingNo", "#searchButton"),
        "SM Line": ("#containerNumber", "#searchBtn"),
        "CU Lines": ("#trackingNumber", "#searchButton"),
        "Emirates": ("#containerNo", "#trackBtn"),
        "Global Feeder": ("#trackingInput", "#searchBtn"),
        "RCL": ("#containerNumber", "#searchButton"),
        "Tailwind": ("#trackingNo", "#searchBtn"),
        "Swire": ("#containerNumber", "#trackButton"),
        "MPC": ("#trackingNumber", "#searchBtn"),
        "NYK": ("#containerNo", "#searchButton"),
        "Matson": ("#trackingInput", "#trackBtn"),
        "Turkon": ("#containerNumber", "#searchButton"),
        "DFDS": ("#trackingNo", "#searchBtn"),
        "Dole": ("#containerNumber", "#trackButton"),
        "Sinotrans": ("#trackingNumber", "#searchBtn"),
        "Kuehne": ("#trackingInput", "#searchButton"),
        "DHL": ("#trackingNumber", "#trackBtn")
    }
    
    # 尝试匹配公司名称
    for key, (input_id, button_id) in ui_mapping.items():
        if key.upper() in company_name.upper():
            return input_id, button_id
    
    # 默认值：通用的输入框和搜索按钮ID
    return "#trackingNumber", "#searchButton"

def verify_migration():
    """验证迁移结果"""
    conn = sqlite3.connect('db/carriers.db')
    cursor = conn.cursor()
    
    # 统计迁移结果
    cursor.execute("SELECT COUNT(*) FROM carriers")
    carrier_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM carrier_scac_prefixes")
    scac_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM carrier_bl_patterns")
    pattern_count = cursor.fetchone()[0]
    
    print("\n=== 迁移验证结果 ===")
    print(f"船公司总数: {carrier_count}")
    print(f"SCAC前缀总数: {scac_count}")
    print(f"提单规则总数: {pattern_count}")
    
    # 显示前5个船公司的详细信息
    cursor.execute("""
        SELECT c.company_name, c.company_code,
               GROUP_CONCAT(DISTINCT s.scac_prefix) as scac_prefixes,
               COUNT(DISTINCT p.id) as pattern_count
        FROM carriers c
        LEFT JOIN carrier_scac_prefixes s ON c.id = s.carrier_id
        LEFT JOIN carrier_bl_patterns p ON c.id = p.carrier_id
        GROUP BY c.id, c.company_name, c.company_code
        ORDER BY c.company_name
        LIMIT 5
    """)
    
    results = cursor.fetchall()
    
    print("\n前5个船公司示例:")
    for row in results:
        print(f"- {row[1]}: {row[0]}")
        print(f"  SCAC: {row[2] or '无'}")
        print(f"  规则数: {row[3]}")
    
    conn.close()
    
    return carrier_count == len(COMPANY_DATA)

if __name__ == "__main__":
    print("船公司数据迁移工具")
    print("=" * 50)
    
    if migrate_data():
        if verify_migration():
            print("\n✅ 数据迁移成功！")
            print("\n下一步建议:")
            print("1. 修改 carrier_lookup.py 使用数据库查询")
            print("2. 备份原始的 COMPANY_DATA 字典")
            print("3. 测试新的数据库查询功能")
        else:
            print("\n⚠️ 数据迁移可能不完整，请检查")
    else:
        print("\n❌ 数据迁移失败")