#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3

def check_shipment_dates_schema():
    """检查shipment_dates表的结构"""
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    
    try:
        # 获取表结构
        cursor.execute("PRAGMA table_info(shipment_dates)")
        columns = cursor.fetchall()
        
        print("📋 shipment_dates表结构:")
        print("=" * 50)
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='shipment_dates'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ shipment_dates表不存在")
        else:
            print(f"✅ shipment_dates表存在，共有 {len(columns)} 列")
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
    finally:
        conn.close()

def check_shipment_records_schema():
    """检查shipment_records表的结构"""
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    
    try:
        # 获取表结构
        cursor.execute("PRAGMA table_info(shipment_records)")
        columns = cursor.fetchall()
        
        print("\n📋 shipment_records表结构:")
        print("=" * 50)
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        print(f"✅ shipment_records表存在，共有 {len(columns)} 列")
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_shipment_dates_schema()
    check_shipment_records_schema()