#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的数据存储问题调试脚本
避免使用emoji和特殊字符
"""

import os
import sys
import sqlite3
from datetime import datetime

# 只导入需要的模块，避免playwright依赖
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from task_manager import TaskManager
from shipment_manager import ShipmentManager

def check_database_connectivity():
    """检查数据库连接性"""
    print("=== 数据库连接性检查 ===")
    
    # 检查货运记录数据库
    shipment_db_path = 'db/shipment_records.db'
    task_db_path = 'db/task_queue.db'
    
    print(f"货运记录数据库: {os.path.exists(shipment_db_path)} ({shipment_db_path})")
    print(f"任务队列数据库: {os.path.exists(task_db_path)} ({task_db_path})")
    
    if os.path.exists(shipment_db_path):
        try:
            conn = sqlite3.connect(shipment_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM shipment_records")
            count = cursor.fetchone()[0]
            print(f"货运记录表中有 {count} 条记录")
            
            cursor.execute("SELECT COUNT(*) FROM shipment_dates")
            dates_count = cursor.fetchone()[0]
            print(f"时间节点表中有 {dates_count} 条记录")
            conn.close()
        except Exception as e:
            print(f"货运记录数据库连接失败: {e}")
    
    if os.path.exists(task_db_path):
        try:
            conn = sqlite3.connect(task_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM task_queue")
            count = cursor.fetchone()[0]
            print(f"任务队列表中有 {count} 条记录")
            conn.close()
        except Exception as e:
            print(f"任务队列数据库连接失败: {e}")

def test_direct_database_insert():
    """直接测试数据库插入，跳过业务逻辑"""
    print("\n=== 直接数据库插入测试 ===")
    
    try:
        # 直接连接数据库进行插入测试
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 插入测试记录
        cursor.execute('''
            INSERT INTO shipment_records 
            (bill_of_lading, carrier_company, status, created_by)
            VALUES (?, ?, ?, ?)
        ''', ('DIRECT_TEST_001', 'MSC', '测试状态', 'direct_test'))
        
        record_id = cursor.lastrowid
        conn.commit()
        print(f"直接插入成功，记录ID: {record_id}")
        
        # 验证插入
        cursor.execute('SELECT * FROM shipment_records WHERE id = ?', (record_id,))
        record = cursor.fetchone()
        if record:
            print(f"验证成功: 提单号={record[1]}, 船公司={record[4]}")
        
        # 插入时间节点测试
        cursor.execute('''
            INSERT INTO shipment_dates 
            (shipment_id, date, type, location, description, status)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (record_id, '2025-01-15', 'ETD', 'Shanghai CN', 'Test Departure', 'estimated'))
        
        conn.commit()
        print("时间节点插入成功")
        
        # 验证时间节点
        cursor.execute('SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?', (record_id,))
        dates_count = cursor.fetchone()[0]
        print(f"该记录的时间节点数量: {dates_count}")
        
        conn.close()
        return record_id
        
    except Exception as e:
        print(f"直接数据库插入失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_shipment_manager_direct():
    """测试ShipmentManager的直接数据库操作"""
    print("\n=== ShipmentManager直接测试 ===")
    
    try:
        # 创建ShipmentManager但不触发任务创建
        manager = ShipmentManager()
        
        # 直接连接数据库插入，模拟create_shipment_record的核心逻辑
        conn = manager._get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO shipment_records 
            (bill_of_lading, container_number, carrier_company, 
             estimated_arrival_time, evidence_screenshot, remarks, created_by, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('MANAGER_TEST_001', None, 'MSC', None, None, '直接测试', 'manager_test', '排队中'))
        
        record_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"ShipmentManager直接插入成功，记录ID: {record_id}")
        
        # 测试添加时间节点
        test_dates_data = [
            {
                'date': '2025-01-20',
                'original_format': '20/01/2025',
                'event_type': 'ETD',
                'location': 'Shanghai CN',
                'event': 'Estimated Time of Departure',
                'status': 'estimated',
                'vessel_info': 'TEST VESSEL',
                'context': '测试上下文'
            }
        ]
        
        success = manager.add_shipment_dates(str(record_id), test_dates_data)
        print(f"添加时间节点: {'成功' if success else '失败'}")
        
        # 验证完整记录
        record = manager.get_shipment_record(str(record_id))
        if record:
            print(f"完整记录验证成功:")
            print(f"  提单号: {record['bill_of_lading']}")
            print(f"  状态: {record['status']}")
            print(f"  时间节点数量: {len(record.get('dates', []))}")
        
        return record_id
        
    except Exception as e:
        print(f"ShipmentManager直接测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_task_completion_simulation():
    """模拟任务完成后的数据更新"""
    print("\n=== 任务完成数据更新模拟 ===")
    
    try:
        manager = ShipmentManager()
        
        # 先创建一个基础记录
        conn = manager._get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO shipment_records 
            (bill_of_lading, carrier_company, status, created_by, remarks)
            VALUES (?, ?, ?, ?, ?)
        ''', ('COMPLETION_TEST_001', 'MSC', '处理中', 'completion_test', '任务ID: test-task-001'))
        
        record_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"基础记录创建成功，ID: {record_id}")
        
        # 模拟任务完成数据
        mock_result_data = {
            'estimated_arrival_time': '2025-03-15',
            'estimated_arrival_port': 'Los Angeles CA',
            'dates_data': [
                {
                    'date': '2025-02-01',
                    'original_format': '01/02/2025',
                    'event_type': 'ETD',
                    'location': 'Shanghai CN', 
                    'event': 'Estimated Time of Departure',
                    'status': 'estimated',
                    'vessel_info': 'MSC COMPLETION TEST',
                    'context': '模拟任务完成数据'
                },
                {
                    'date': '2025-03-15',
                    'original_format': '15/03/2025',
                    'event_type': 'ETA',
                    'location': 'Los Angeles CA',
                    'event': 'Estimated Time of Arrival', 
                    'status': 'estimated',
                    'vessel_info': 'MSC COMPLETION TEST',
                    'context': '模拟预计到港时间'
                }
            ]
        }
        
        # 测试更新预计到港时间
        success = manager._update_estimated_arrival_time(str(record_id), mock_result_data['estimated_arrival_time'])
        print(f"更新预计到港时间: {'成功' if success else '失败'}")
        
        # 测试添加时间节点
        success = manager.add_shipment_dates(str(record_id), mock_result_data['dates_data'])
        print(f"添加时间节点: {'成功' if success else '失败'}")
        
        # 验证最终结果
        final_record = manager.get_shipment_record(str(record_id))
        if final_record:
            print(f"最终验证成功:")
            print(f"  预计到港时间: {final_record.get('estimated_arrival_time', 'None')}")
            print(f"  时间节点数量: {len(final_record.get('dates', []))}")
            
            for i, date_info in enumerate(final_record.get('dates', [])):
                print(f"    节点{i+1}: {date_info.get('date')} - {date_info.get('description')} @ {date_info.get('location')}")
        
        return record_id
        
    except Exception as e:
        print(f"任务完成模拟失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def main():
    """主函数"""
    print("开始简化数据流调试...")
    
    # 检查数据库连接
    check_database_connectivity()
    
    # 直接数据库插入测试
    direct_record_id = test_direct_database_insert()
    
    # ShipmentManager直接测试
    manager_record_id = test_shipment_manager_direct()
    
    # 任务完成数据更新模拟
    completion_record_id = test_task_completion_simulation()
    
    print("\n调试完成！")
    print("如果所有测试都成功，说明数据存储逻辑是正常的")
    print("问题可能在于：")
    print("1. task_executor.py中的AI分析结果解析")
    print("2. task_processor.py中的回调机制")
    print("3. 任务ID和货运记录ID的关联")

if __name__ == "__main__":
    main()