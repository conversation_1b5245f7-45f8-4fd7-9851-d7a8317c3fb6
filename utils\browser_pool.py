#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器资源池管理器
实现多Context并行网页抓取，大幅提升抓取速度
"""

import os
import time
import threading
import atexit
import queue
from typing import Dict, Optional, List, Tuple
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erConte<PERSON><PERSON>, TimeoutError as PlaywrightTimeoutError
from datetime import datetime
from collections import defaultdict
import traceback


class BrowserPool:
    """浏览器资源池 - 管理多个BrowserContext实现并行抓取"""
    
    def __init__(self, max_contexts: int = 4, max_concurrent_per_site: int = 2):
        """
        初始化浏览器资源池
        
        Args:
            max_contexts: 最大Context数量 (推荐4-8)
            max_concurrent_per_site: 每个网站最大并发数 (避免被反爬虫)
        """
        self.max_contexts = max_contexts
        self.max_concurrent_per_site = max_concurrent_per_site
        
        # 浏览器实例管理
        self._playwright = None
        self._browser: Optional[Browser] = None
        self._browser_lock = threading.Lock()
        
        # Context池管理
        self._available_contexts = queue.Queue()
        self._active_contexts = {}  # context_id -> context_info
        self._context_lock = threading.Lock()
        
        # 网站并发控制 
        self._site_concurrency = defaultdict(int)  # site_domain -> concurrent_count
        self._site_lock = threading.Lock()
        
        # 统计信息
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'contexts_created': 0,
            'contexts_reused': 0,
            'start_time': time.time()
        }
        
        self._initialized = False
        print(f"[BROWSER_POOL] 初始化浏览器资源池 (最大Context: {max_contexts}, 每站点并发: {max_concurrent_per_site})")
    
    def _ensure_browser_ready(self):
        """确保浏览器实例已启动并准备就绪"""
        with self._browser_lock:
            if self._browser is not None and not self._browser.is_closed():
                return
            
            try:
                print("[BROWSER_POOL] 启动共享浏览器实例...")
                if self._playwright:
                    self._playwright.stop()
                
                self._playwright = sync_playwright().start()
                self._browser = self._playwright.chromium.launch(
                    headless=False,
                    args=[
                        '--no-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )
                print("[BROWSER_POOL] ✅ 浏览器实例启动完成")
                
            except Exception as e:
                print(f"[BROWSER_POOL] ❌ 浏览器启动失败: {e}")
                raise
    
    def _create_context(self) -> Tuple[str, BrowserContext]:
        """创建新的BrowserContext"""
        self._ensure_browser_ready()
        
        context_id = f"ctx_{int(time.time() * 1000)}_{threading.current_thread().ident}"
        context = self._browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self._stats['contexts_created'] += 1
        print(f"[BROWSER_POOL] 创建新Context: {context_id}")
        return context_id, context
    
    def acquire_context(self, site_domain: str) -> Tuple[str, BrowserContext]:
        """
        获取可用的BrowserContext
        
        Args:
            site_domain: 目标网站域名 (用于并发控制)
            
        Returns:
            Tuple[context_id, context]: Context ID和Context实例
        """
        with self._site_lock:
            # 检查网站并发限制
            if self._site_concurrency[site_domain] >= self.max_concurrent_per_site:
                raise Exception(f"网站 {site_domain} 并发数已达上限 ({self.max_concurrent_per_site})")
            
            self._site_concurrency[site_domain] += 1
        
        try:
            # 尝试获取可用的Context
            try:
                context_id, context = self._available_contexts.get_nowait()
                self._stats['contexts_reused'] += 1
                print(f"[BROWSER_POOL] 复用Context: {context_id} (站点: {site_domain})")
            except queue.Empty:
                # 创建新Context
                with self._context_lock:
                    if len(self._active_contexts) >= self.max_contexts:
                        raise Exception(f"Context池已满 ({self.max_contexts})")
                
                context_id, context = self._create_context()
            
            # 标记Context为活跃状态
            with self._context_lock:
                self._active_contexts[context_id] = {
                    'context': context,
                    'site_domain': site_domain,
                    'acquired_time': time.time(),
                    'thread_id': threading.current_thread().ident
                }
            
            return context_id, context
            
        except Exception:
            # 发生错误时释放网站并发计数
            with self._site_lock:
                self._site_concurrency[site_domain] -= 1
            raise
    
    def release_context(self, context_id: str, reuse: bool = True):
        """
        释放BrowserContext
        
        Args:
            context_id: Context ID
            reuse: 是否重用Context (False则关闭)
        """
        context_info = None
        
        with self._context_lock:
            context_info = self._active_contexts.pop(context_id, None)
        
        if not context_info:
            print(f"[BROWSER_POOL] ⚠️ Context {context_id} 未找到或已释放")
            return
        
        context = context_info['context']
        site_domain = context_info['site_domain']
        
        # 释放网站并发计数
        with self._site_lock:
            self._site_concurrency[site_domain] -= 1
        
        try:
            if reuse and not context.is_closed():
                # 清理Context状态（清除缓存、Cookie等）
                try:
                    context.clear_cookies()
                    context.clear_permissions()
                    # 关闭所有页面但保留Context
                    for page in context.pages:
                        if not page.is_closed():
                            page.close()
                except Exception as cleanup_error:
                    print(f"[BROWSER_POOL] ⚠️ Context清理失败: {cleanup_error}")
                    reuse = False
            
            if reuse and not context.is_closed():
                # 重新放入可用队列
                self._available_contexts.put((context_id, context))
                print(f"[BROWSER_POOL] Context已回收复用: {context_id}")
            else:
                # 关闭Context
                if not context.is_closed():
                    context.close()
                print(f"[BROWSER_POOL] Context已关闭: {context_id}")
                
        except Exception as e:
            print(f"[BROWSER_POOL] Context释放异常: {e}")
            try:
                if not context.is_closed():
                    context.close()
            except:
                pass
    
    def get_site_domain(self, url: str) -> str:
        """从URL提取域名"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except:
            return url.split('/')[2].lower() if '://' in url else 'unknown'
    
    def get_pool_status(self) -> Dict:
        """获取资源池状态信息"""
        with self._context_lock, self._site_lock:
            uptime = time.time() - self._stats['start_time']
            
            return {
                'max_contexts': self.max_contexts,
                'active_contexts': len(self._active_contexts),
                'available_contexts': self._available_contexts.qsize(),
                'site_concurrency': dict(self._site_concurrency),
                'stats': {
                    **self._stats,
                    'uptime_seconds': uptime,
                    'avg_tasks_per_minute': (self._stats['completed_tasks'] / uptime * 60) if uptime > 0 else 0
                },
                'browser_status': 'running' if (self._browser and not self._browser.is_closed()) else 'stopped'
            }
    
    def close_all(self):
        """关闭所有资源"""
        print("[BROWSER_POOL] 开始关闭所有资源...")
        
        # 关闭所有活跃Context
        with self._context_lock:
            for context_id, context_info in self._active_contexts.items():
                try:
                    context_info['context'].close()
                except:
                    pass
            self._active_contexts.clear()
        
        # 关闭可用Context队列中的Context
        while not self._available_contexts.empty():
            try:
                context_id, context = self._available_contexts.get_nowait()
                context.close()
            except:
                break
        
        # 关闭浏览器
        with self._browser_lock:
            try:
                if self._browser and not self._browser.is_closed():
                    self._browser.close()
            except:
                pass
            
            try:
                if self._playwright:
                    self._playwright.stop()
            except:
                pass
            
            self._browser = None
            self._playwright = None
        
        print("[BROWSER_POOL] ✅ 所有资源已关闭")


# 全局资源池实例
_global_pool: Optional[BrowserPool] = None
_pool_lock = threading.Lock()


def get_browser_pool(max_contexts: int = 4, max_concurrent_per_site: int = 2) -> BrowserPool:
    """获取全局浏览器资源池实例"""
    global _global_pool
    with _pool_lock:
        if _global_pool is None:
            _global_pool = BrowserPool(max_contexts, max_concurrent_per_site)
            # 注册退出时清理
            atexit.register(_global_pool.close_all)
        return _global_pool


if __name__ == "__main__":
    # 测试代码
    pool = get_browser_pool(max_contexts=2)
    
    try:
        print("🧪 测试BrowserPool...")
        
        # 获取Context
        ctx_id, ctx = pool.acquire_context("www.msc.com")
        print(f"获取到Context: {ctx_id}")
        
        # 创建页面测试
        page = ctx.new_page()
        page.goto("https://www.msc.com")
        print(f"页面标题: {page.title()}")
        page.close()
        
        # 释放Context
        pool.release_context(ctx_id)
        
        # 查看状态
        status = pool.get_pool_status()
        print(f"资源池状态: {status}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
    finally:
        pool.close_all()