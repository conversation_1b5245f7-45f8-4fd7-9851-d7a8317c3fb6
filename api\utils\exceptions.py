#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API异常处理
定义自定义异常和异常处理器
"""

from typing import Dict, Any
from datetime import datetime
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import J<PERSON>NResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from api.utils.logger import api_logger

class APIError(Exception):
    """API基础异常"""
    
    def __init__(self, message: str, error_code: str = "API_ERROR", status_code: int = 500, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)

class TaskNotFoundError(APIError):
    """任务不存在异常"""
    
    def __init__(self, task_id: str):
        super().__init__(
            message=f"任务不存在: {task_id}",
            error_code="TASK_NOT_FOUND",
            status_code=404,
            details={"task_id": task_id}
        )

class InvalidRequestError(APIError):
    """无效请求异常"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="INVALID_REQUEST",
            status_code=400,
            details=details
        )

class RateLimitExceededError(APIError):
    """请求频率限制异常"""
    
    def __init__(self, message: str = "请求频率超出限制"):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=429
        )

class ServiceUnavailableError(APIError):
    """服务不可用异常"""
    
    def __init__(self, message: str = "服务暂时不可用"):
        super().__init__(
            message=message,
            error_code="SERVICE_UNAVAILABLE",
            status_code=503
        )

class AuthenticationError(APIError):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401
        )

class AuthorizationError(APIError):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403
        )

# 异常处理器
async def api_error_handler(request: Request, exc: APIError):
    """API异常处理器"""
    api_logger.error(
        f"API异常: {exc.error_code} - {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "url": str(request.url),
            "method": request.method
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "details": exc.details,
            "timestamp": datetime.now().isoformat()
        }
    )

async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    api_logger.error(
        f"HTTP异常: {exc.status_code} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "url": str(request.url),
            "method": request.method
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}",
            "timestamp": datetime.now().isoformat()
        }
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    api_logger.error(
        f"请求验证异常: {exc.errors()}",
        extra={
            "errors": exc.errors(),
            "url": str(request.url),
            "method": request.method
        }
    )
    
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "请求参数验证失败",
            "error_code": "VALIDATION_ERROR",
            "details": {
                "errors": exc.errors()
            },
            "timestamp": datetime.now().isoformat()
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    api_logger.error(
        f"未处理异常: {type(exc).__name__} - {str(exc)}",
        extra={
            "exception_type": type(exc).__name__,
            "url": str(request.url),
            "method": request.method
        },
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "内部服务器错误",
            "error_code": "INTERNAL_SERVER_ERROR",
            "timestamp": datetime.now().isoformat()
        }
    )

def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    app.add_exception_handler(APIError, api_error_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    api_logger.info("异常处理器设置完成")