#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试任务完成流程的测试脚本
用于验证AI结果处理和数据更新的完整流程
"""

import os
import sys
import time
from shipment_manager import ShipmentManager
from task_manager import TaskManager
from task_processor import TaskProcessor

def test_task_completion_flow():
    """
    测试任务完成流程
    """
    print("🔍 开始调试任务完成流程")
    print("=" * 60)
    
    # 初始化管理器
    shipment_manager = ShipmentManager()
    task_manager = TaskManager()
    
    # 创建带回调的任务处理器
    def completion_callback(task_id: str, result_data: dict = None):
        print(f"\n📞 回调函数被调用: task_id={task_id}")
        print(f"📊 回调接收到的数据: {result_data}")
        return shipment_manager.handle_task_completion(task_id, result_data)
    
    task_processor = TaskProcessor(completion_callback=completion_callback)
    
    try:
        # 1. 创建测试货运记录
        print("\n📦 步骤1: 创建测试货运记录")
        print("-" * 40)
        
        record_id = shipment_manager.create_shipment_record(
            bill_of_lading="TEST123456",
            container_number="TESTU1234567",
            carrier_company="测试船公司",
            remarks="调试测试记录"
        )
        
        if not record_id:
            print("❌ 创建货运记录失败")
            return
        
        print(f"✅ 创建货运记录成功: {record_id}")
        
        # 2. 创建关联任务
        print("\n📋 步骤2: 创建关联任务")
        print("-" * 40)
        
        task_id = task_manager.create_task(
            tracking_number="TEST123456",
            task_type="bill_of_lading",
            creator_id="debug_test",
            creator_name="调试测试",
            carrier="测试船公司",
            remarks=f"货运记录ID:{record_id},自动创建"
        )
        
        if not task_id:
            print("❌ 创建任务失败")
            return
        
        print(f"✅ 创建任务成功: {task_id}")
        
        # 3. 模拟AI分析结果
        print("\n🤖 步骤3: 模拟AI分析结果")
        print("-" * 40)
        
        # 创建模拟的AI结果文件
        ai_result_content = '''
这是AI分析的货运信息：

```json
{
  "dates": [
    {
      "date": "2024-01-15",
      "type": "DEPARTURE",
      "location": "上海港",
      "description": "货物装船离港",
      "status": "已完成",
      "vessel_info": {
        "name": "测试轮",
        "voyage": "001E"
      }
    },
    {
      "date": "2024-01-25",
      "type": "POD_ETA",
      "location": "洛杉矶港",
      "description": "预计到达目的港",
      "status": "预计",
      "vessel_info": {
        "name": "测试轮",
        "voyage": "001E"
      }
    }
  ]
}
```

分析完成。
'''
        
        ai_result_file = f"ai_analysis_result_TEST123456.txt"
        with open(ai_result_file, 'w', encoding='utf-8') as f:
            f.write(ai_result_content)
        
        print(f"✅ 创建AI结果文件: {ai_result_file}")
        
        # 4. 模拟任务处理完成
        print("\n⚙️ 步骤4: 模拟任务处理")
        print("-" * 40)
        
        # 更新任务状态为处理中
        task_manager.update_task_status(task_id, "处理中")
        
        # 模拟处理过程
        result_data = {
            'success': True,
            'summary': '查询成功，获取到货运信息',
            'ai_result': ai_result_content,
            'result_files': [ai_result_file]
        }
        
        # 调用任务处理器的解析方法
        parsed_result = task_processor._parse_ai_result(ai_result_content)
        result_data.update(parsed_result)
        
        print(f"📊 解析后的结果数据: {result_data}")
        
        # 5. 更新任务状态为已完成并触发回调
        print("\n🎯 步骤5: 完成任务并触发回调")
        print("-" * 40)
        
        task_manager.update_task_status(task_id, "已完成")
        
        # 手动调用回调函数（模拟TaskProcessor的行为）
        callback_success = completion_callback(task_id, result_data)
        
        if callback_success:
            print("✅ 回调处理成功")
        else:
            print("❌ 回调处理失败")
        
        # 6. 验证数据更新结果
        print("\n🔍 步骤6: 验证数据更新结果")
        print("-" * 40)
        
        # 检查货运记录是否更新
        updated_record = shipment_manager.get_shipment_record(record_id)
        if updated_record:
            print(f"📦 货运记录状态: {updated_record.get('status')}")
            print(f"📅 预计到港时间: {updated_record.get('estimated_arrival_time')}")
        
        # 检查时间节点是否添加
        details = shipment_manager.get_shipment_details(record_id)
        if details and 'dates' in details:
            print(f"📊 时间节点数量: {len(details['dates'])}")
            for i, date_item in enumerate(details['dates']):
                print(f"  📌 节点{i+1}: {date_item.get('event_date')} - {date_item.get('event_description')}")
        
        print("\n✅ 调试流程完成")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        print(f"📋 错误详情: {traceback.format_exc()}")
    
    finally:
        # 清理测试文件
        try:
            if os.path.exists("ai_analysis_result_TEST123456.txt"):
                os.remove("ai_analysis_result_TEST123456.txt")
                print("🗑️ 清理测试文件完成")
        except:
            pass

if __name__ == "__main__":
    test_task_completion_flow()