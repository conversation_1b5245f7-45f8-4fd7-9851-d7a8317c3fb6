#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化货运记录数据库
创建货运记录主表和时间节点子表
"""

import sqlite3
import os
from datetime import datetime

def init_shipment_database():
    """
    初始化货运记录数据库
    """
    db_path = 'shipment_records.db'
    
    # 如果数据库文件已存在，先备份
    if os.path.exists(db_path):
        backup_path = f'shipment_records_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        os.rename(db_path, backup_path)
        print(f"📦 已备份现有数据库到: {backup_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 创建货运记录主表
        cursor.execute('''
        CREATE TABLE shipment_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bill_of_lading VARCHAR(100),
            container_number VARCHAR(100),
            status VARCHAR(50) NOT NULL DEFAULT '待处理',
            carrier_company VARCHAR(100),
            estimated_arrival_time DATETIME,
            evidence_screenshot TEXT,
            remarks TEXT,
            created_by VARCHAR(100) NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_by VARCHAR(100),
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建货运时间节点子表
        cursor.execute('''
        CREATE TABLE shipment_dates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            shipment_id INTEGER NOT NULL,
            date DATE NOT NULL,
            original_format VARCHAR(50),
            type VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            description VARCHAR(200),
            status VARCHAR(50),
            vessel_info VARCHAR(200),
            context TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (shipment_id) REFERENCES shipment_records(id) ON DELETE CASCADE
        )
        ''')
        
        # 创建主表索引
        cursor.execute('CREATE INDEX idx_shipment_bill_of_lading ON shipment_records(bill_of_lading)')
        cursor.execute('CREATE INDEX idx_shipment_container_number ON shipment_records(container_number)')
        cursor.execute('CREATE INDEX idx_shipment_status ON shipment_records(status)')
        cursor.execute('CREATE INDEX idx_shipment_carrier_company ON shipment_records(carrier_company)')
        cursor.execute('CREATE INDEX idx_shipment_created_at ON shipment_records(created_at)')
        
        # 创建子表索引
        cursor.execute('CREATE INDEX idx_dates_shipment_id ON shipment_dates(shipment_id)')
        cursor.execute('CREATE INDEX idx_dates_date ON shipment_dates(date)')
        cursor.execute('CREATE INDEX idx_dates_type ON shipment_dates(type)')
        cursor.execute('CREATE INDEX idx_dates_location ON shipment_dates(location)')
        
        # 创建updated_at字段的自动更新触发器
        cursor.execute('''
        CREATE TRIGGER update_shipment_records_timestamp 
        AFTER UPDATE ON shipment_records
        FOR EACH ROW
        BEGIN
            UPDATE shipment_records 
            SET updated_at = CURRENT_TIMESTAMP 
            WHERE id = NEW.id;
        END
        ''')
        
        # 提交事务
        conn.commit()
        
        print("✅ 货运记录数据库初始化成功！")
        print("\n📋 已创建的表结构:")
        
        # 显示主表结构
        cursor.execute("PRAGMA table_info(shipment_records)")
        columns = cursor.fetchall()
        print("\n🚢 shipment_records (货运记录主表):")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # 显示子表结构
        cursor.execute("PRAGMA table_info(shipment_dates)")
        columns = cursor.fetchall()
        print("\n📅 shipment_dates (时间节点子表):")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # 显示索引信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name IN ('shipment_records', 'shipment_dates')")
        indexes = cursor.fetchall()
        print("\n🔍 已创建的索引:")
        for idx in indexes:
            if not idx[0].startswith('sqlite_'):
                print(f"  - {idx[0]}")
        
        # 显示触发器信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
        triggers = cursor.fetchall()
        print("\n⚡ 已创建的触发器:")
        for trigger in triggers:
            print(f"  - {trigger[0]}")
        
        print(f"\n💾 数据库文件: {os.path.abspath(db_path)}")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    init_shipment_database()