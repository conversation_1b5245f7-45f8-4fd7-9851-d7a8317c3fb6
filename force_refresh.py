#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制刷新货运记录状态的工具
"""

import sqlite3
from shipment_manager import ShipmentManager

def force_refresh_status():
    """强制刷新所有货运记录状态"""
    print("=== 强制刷新货运记录状态 ===\n")
    
    shipment_manager = ShipmentManager()
    
    # 获取所有非"已完成"状态的记录
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    cursor.execute("""
        SELECT id, bill_of_lading, container_number, status
        FROM shipment_records 
        WHERE status != '已完成'
        ORDER BY updated_at DESC
    """)
    
    records = cursor.fetchall()
    conn.close()
    
    if not records:
        print("所有记录都已经是'已完成'状态，无需刷新")
        return
    
    print(f"找到 {len(records)} 个需要检查的记录:")
    
    for record in records:
        record_id, bill, container, current_status = record
        tracking_number = bill or container
        
        print(f"\n检查记录ID {record_id} ({tracking_number})")
        print(f"  当前状态: {current_status}")
        
        # 检查对应的任务完成情况
        task_conn = sqlite3.connect('db/task_queue.db')
        task_cursor = task_conn.cursor()
        
        # 查找最新的网页抓取任务
        task_cursor.execute("""
            SELECT status, completed_at
            FROM task_queue 
            WHERE tracking_number = ? AND task_stage = 'scraping'
            ORDER BY created_at DESC
            LIMIT 1
        """, (tracking_number,))
        
        scraping_result = task_cursor.fetchone()
        
        # 查找最新的AI分析任务  
        task_cursor.execute("""
            SELECT status, completed_at
            FROM task_queue 
            WHERE tracking_number = ? AND task_stage = 'ai_analysis'
            ORDER BY created_at DESC
            LIMIT 1
        """, (tracking_number,))
        
        ai_result = task_cursor.fetchone()
        task_conn.close()
        
        # 判断应该更新到什么状态
        new_status = None
        
        if ai_result and ai_result[0] == 'completed':
            new_status = "已完成"
            print(f"  AI分析已完成，应更新为: {new_status}")
        elif scraping_result and scraping_result[0] == 'completed' and not ai_result:
            new_status = None  # 不再显示AI分析中
            print(f"  网页抓取已完成但无AI分析，跳过状态变更")
        elif scraping_result and scraping_result[0] == 'processing':
            new_status = "处理中"
            print(f"  抓取进行中，应更新为: {new_status}")
        elif scraping_result and scraping_result[0] == 'failed':
            print(f"  网页抓取失败，保持当前状态: {current_status}")
        else:
            print(f"  无法确定状态，保持当前状态: {current_status}")

        # 执行状态更新
        if new_status and new_status != current_status:
            print(f"  执行状态更新: {current_status} -> {new_status}")
            success = shipment_manager.update_shipment_status(
                record_id, new_status, "force_refresh"
            )
            if success:
                print(f"  ✓ 状态更新成功")
            else:
                print(f"  ✗ 状态更新失败")
        else:
            print(f"  状态无需更新")

if __name__ == "__main__":
    force_refresh_status()