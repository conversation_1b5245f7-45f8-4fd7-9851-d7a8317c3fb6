#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试截图保存修复功能
验证 task_executor.py 中的截图保存异常处理是否正常工作
"""

import os
import sys
from datetime import datetime
from utils.file_manager import get_file_manager
from task_executor import run_visual_agent

def test_screenshot_saving():
    """
    测试截图保存功能
    """
    print("=" * 60)
    print("测试截图保存修复功能")
    print("=" * 60)
    
    # 测试提单号
    test_bl_number = "MEDUJ0618622"
    
    print(f"\n📋 测试提单号: {test_bl_number}")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取文件管理器
    file_mgr = get_file_manager()
    
    # 记录执行前的文件数量
    before_files = file_mgr.get_files_for_tracking(test_bl_number)
    before_count = len(before_files)
    print(f"\n📁 执行前文件记录数量: {before_count}")
    
    try:
        # 执行任务
        print("\n🚀 开始执行任务...")
        run_visual_agent(test_bl_number)
        
        # 检查执行后的文件
        after_files = file_mgr.get_files_for_tracking(test_bl_number)
        after_count = len(after_files)
        print(f"\n📁 执行后文件记录数量: {after_count}")
        
        if after_count > before_count:
            # 获取最新的文件记录
            latest_record = after_files[0]
            print(f"\n📂 最新文件夹: {latest_record['folder_name']}")
            print(f"📍 文件夹路径: {latest_record['folder_path']}")
            
            # 检查文件列表
            files_in_folder = latest_record['files']
            print(f"\n📄 文件夹中的文件 ({len(files_in_folder)} 个):")
            
            has_screenshot = False
            has_ai_result = False
            
            for file_info in files_in_folder:
                file_name = file_info['name']
                file_size = file_info['size']
                print(f"  - {file_name} ({file_size} bytes)")
                
                if file_name == 'final_result.png':
                    has_screenshot = True
                elif file_name.startswith('ai_analysis_result_'):
                    has_ai_result = True
            
            # 验证结果
            print(f"\n✅ 验证结果:")
            print(f"  📸 包含截图文件: {'是' if has_screenshot else '否'}")
            print(f"  🤖 包含AI分析结果: {'是' if has_ai_result else '否'}")
            
            if has_screenshot and has_ai_result:
                print(f"\n🎉 测试成功！截图保存功能正常工作")
                return True
            elif has_ai_result and not has_screenshot:
                print(f"\n⚠️ 警告：AI分析完成但截图缺失")
                return False
            else:
                print(f"\n❌ 测试失败：文件不完整")
                return False
        else:
            print(f"\n❌ 测试失败：没有生成新的文件记录")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False
    
    finally:
        print(f"\n🕐 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

def check_recent_files():
    """
    检查最近的文件记录，查看截图保存情况
    """
    print("\n🔍 检查最近的文件记录...")
    
    file_mgr = get_file_manager()
    
    # 检查几个测试提单号的文件
    test_numbers = ["MEDUJ0618622", "MEDUVS935363"]
    
    for bl_number in test_numbers:
        print(f"\n📋 检查提单号: {bl_number}")
        files = file_mgr.get_files_for_tracking(bl_number)
        
        if files:
            print(f"  📁 找到 {len(files)} 个文件记录")
            
            # 检查最近的5个记录
            for i, record in enumerate(files[:5]):
                folder_name = record['folder_name']
                files_in_folder = record['files']
                
                has_screenshot = any(f['name'] == 'final_result.png' for f in files_in_folder)
                has_ai_result = any(f['name'].startswith('ai_analysis_result_') for f in files_in_folder)
                
                status = "✅" if has_screenshot and has_ai_result else "⚠️" if has_ai_result else "❌"
                print(f"  {status} {folder_name} - 文件数: {len(files_in_folder)} (截图: {'有' if has_screenshot else '无'}, AI: {'有' if has_ai_result else '无'})")
        else:
            print(f"  📭 未找到文件记录")

if __name__ == "__main__":
    # 首先检查现有文件状态
    check_recent_files()
    
    # 询问是否执行测试
    print("\n❓ 是否要执行新的截图保存测试？(y/n): ", end="")
    choice = input().strip().lower()
    
    if choice in ['y', 'yes', '是']:
        success = test_screenshot_saving()
        if success:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 测试失败，请检查日志")
    else:
        print("\n👋 测试已取消")