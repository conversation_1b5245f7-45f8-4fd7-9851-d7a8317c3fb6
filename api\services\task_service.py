#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务服务层
复用现有任务管理系统，为API提供服务
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 确保能找到项目根目录的模块
os.chdir(str(project_root))

try:
    # 使用绝对路径导入
    import importlib.util
    
    # 导入 task_manager
    task_manager_path = project_root / "task_manager.py"
    spec = importlib.util.spec_from_file_location("task_manager", task_manager_path)
    task_manager_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(task_manager_module)
    TaskManager = task_manager_module.TaskManager
    
    # 导入 carrier_database
    carrier_db_path = project_root / "utils" / "carrier_database.py"
    spec = importlib.util.spec_from_file_location("carrier_database", carrier_db_path)
    carrier_db_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(carrier_db_module)
    CarrierDatabase = carrier_db_module.CarrierDatabase

    # 导入 file_manager
    file_manager_path = project_root / "utils" / "file_manager.py"
    spec = importlib.util.spec_from_file_location("file_manager", file_manager_path)
    file_manager_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(file_manager_module)
    get_file_manager = file_manager_module.get_file_manager
    
except Exception as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录下运行API服务")
    print(f"项目根目录: {project_root}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path[:3]}")
    raise

from api.models.schemas import (
    TaskResponse,
    TaskListResponse,
    CarrierInfo,
    CarrierListResponse,
    TaskStats,
    QueryStats,
    StatsResponse,
    TaskLogsResponse,
    TaskLogEntry,
    TaskStatus,
    TaskPriority
)

class TaskService:
    """任务服务类"""
    
    def __init__(self):
        self.task_manager = TaskManager()
        self.carrier_db = CarrierDatabase()
        # 文件管理器实例
        self.file_manager = get_file_manager()
    
    async def create_shipment_query(
        self,
        container_number: str,
        carrier_code: Optional[str] = None,
        priority: str = "normal",
        callback_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> TaskResponse:
        """创建船期查询任务"""
        
        # 创建任务
        task_id = await asyncio.to_thread(
            self.task_manager.create_task,
            tracking_number=container_number,
            task_type="container",
            creator_id="api_user",
            creator_name="API用户",
            carrier=carrier_code,
            priority=0 if priority == "normal" else 1,
            remarks=f"API创建任务 - {container_number}"
        )
        
        # 获取任务详情
        task_info = await asyncio.to_thread(
            self.task_manager.get_task_by_id,
            task_id
        )
        
        return self._convert_to_task_response(task_info)
    
    async def get_task_status(self, task_id: str) -> Optional[TaskResponse]:
        """获取任务状态"""
        
        task_info = await asyncio.to_thread(
            self.task_manager.get_task_by_id,
            task_id
        )
        
        if not task_info:
            return None
        
        return self._convert_to_task_response(task_info)
    
    async def get_task_list(
        self,
        filters: Optional[Dict[str, Any]] = None,
        page: int = 1,
        page_size: int = 20
    ) -> TaskListResponse:
        """获取任务列表"""
        
        # 适配旧版 TaskManager：没有通用 get_tasks 方法，改用状态过滤与分页组合
        # 默认按 created_at 降序返回最近的若干条
        all_tasks: List[Dict[str, Any]] = []
        status_filter = None
        if filters and 'status' in filters:
            status_filter = filters['status']
        
        if status_filter:
            # 直接按状态获取一定数量
            tasks_data = await asyncio.to_thread(
                self.task_manager.get_tasks_by_status,
                status_filter,
                page * page_size
            )
            all_tasks.extend(tasks_data)
        else:
            # 没有状态过滤时，聚合常见状态，取较多数量后自行分页
            statuses = ['pending', 'processing', 'completed', 'cancelled']
            for s in statuses:
                tasks = await asyncio.to_thread(
                    self.task_manager.get_tasks_by_status,
                    s,
                    page * page_size
                )
                all_tasks.extend(tasks)
            # 根据 created_at 进行降序排序并去重
            seen = set()
            unique_sorted = []
            for t in sorted(all_tasks, key=lambda x: x.get('created_at', ''), reverse=True):
                if t['id'] not in seen:
                    seen.add(t['id'])
                    unique_sorted.append(t)
            all_tasks = unique_sorted
        
        total = len(all_tasks)
        start = (page - 1) * page_size
        end = start + page_size
        page_items = all_tasks[start:end]

        # 预取物流节点数量，使用单次数据库连接，避免为每条任务重复连接
        try:
            import sqlite3
            numbers = [t.get('tracking_number') for t in page_items if t.get('tracking_number')]
            counts_map = {}
            if numbers:
                placeholders = ','.join(['?'] * len(numbers))
                conn = sqlite3.connect('db/shipment_records.db')
                cur = conn.cursor()
                # 同时匹配提单号和箱号，聚合每条货运记录对应的节点数量
                sql = f'''
                    SELECT COALESCE(sr.bill_of_lading, sr.container_number) AS num,
                           COUNT(sd.id) AS cnt
                    FROM shipment_records sr
                    LEFT JOIN shipment_dates sd ON sd.shipment_id = sr.id
                    WHERE (sr.bill_of_lading IN ({placeholders}) OR sr.container_number IN ({placeholders}))
                    GROUP BY sr.id
                '''
                cur.execute(sql, numbers + numbers)
                for row in cur.fetchall():
                    num, cnt = row[0], int(row[1] or 0)
                    if num:
                        counts_map[num] = cnt
                conn.close()
            # 回填到任务字典，供 _convert_to_task_response 直接使用
            for t in page_items:
                tn = t.get('tracking_number')
                if tn in counts_map:
                    t['precomputed_tracking_points_count'] = counts_map[tn]
        except Exception:
            # 预取失败时忽略，后续按旧逻辑单条查询（但会较慢）
            pass

        tasks = [self._convert_to_task_response(task) for task in page_items]

        return TaskListResponse(
            tasks=tasks,
            total=total,
            page=page,
            page_size=page_size,
            has_next=end < total
        )
    
    async def cancel_task(self, task_id: str, reason: Optional[str] = None) -> bool:
        """取消任务"""
        
        return await asyncio.to_thread(
            self.task_manager.cancel_task,
            task_id,
            reason
        )
    
    async def retry_task(
        self,
        task_id: str,
        reason: Optional[str] = None,
        reset_attempts: bool = False
    ) -> Optional[TaskResponse]:
        """重试任务"""
        
        success = await asyncio.to_thread(
            self.task_manager.retry_task,
            task_id,
            reason,
            reset_attempts
        )
        
        if not success:
            return None
        
        # 获取更新后的任务信息
        return await self.get_task_status(task_id)
    
    async def get_task_logs(
        self,
        task_id: str,
        limit: int = 100
    ) -> Optional[TaskLogsResponse]:
        """获取任务日志"""
        
        logs_data = await asyncio.to_thread(
            self.task_manager.get_task_logs,
            task_id,
            limit
        )
        
        if not logs_data:
            return None
        
        logs = [
            TaskLogEntry(
                timestamp=log.get('timestamp', datetime.now()),
                level=log.get('level', 'INFO'),
                message=log.get('message', ''),
                details=log.get('details')
            )
            for log in logs_data.get('logs', [])
        ]
        
        return TaskLogsResponse(
            task_id=task_id,
            logs=logs,
            total=len(logs)
        )
    
    async def get_supported_carriers(self, supported_only: bool = True) -> CarrierListResponse:
        """获取支持的船公司列表"""
        
        carriers_data = await asyncio.to_thread(
            self.carrier_db.get_all_carriers
        )
        
        carriers = []
        for carrier in carriers_data:
            if supported_only and not carrier.get('supported', False):
                continue
            
            carriers.append(CarrierInfo(
                code=carrier.get('code', ''),
                name=carrier.get('name', ''),
                website=carrier.get('website'),
                supported=carrier.get('supported', False),
                last_updated=carrier.get('last_updated')
            ))
        
        return CarrierListResponse(
            carriers=carriers,
            total=len(carriers)
        )
    
    async def get_task_stats(self) -> TaskStats:
        """获取任务统计信息"""
        
        # 获取各状态的任务数量（与数据库状态命名保持一致）
        statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled']
        all_tasks = []
        
        for status in statuses:
            tasks = await asyncio.to_thread(
                self.task_manager.get_tasks_by_status, status, 1000  # 限制每个状态最多1000个任务
            )
            all_tasks.extend(tasks)
        
        # 统计各状态任务数量
        total = len(all_tasks)
        completed = sum(1 for task in all_tasks if task.get('status') == 'completed')
        running = sum(1 for task in all_tasks if task.get('status') in ('processing', 'running'))
        pending = sum(1 for task in all_tasks if task.get('status') == 'pending')
        failed = sum(1 for task in all_tasks if task.get('status') == 'failed')
        cancelled = sum(1 for task in all_tasks if task.get('status') == 'cancelled')
        
        # 计算成功率
        success_rate = (completed / total * 100) if total > 0 else 0.0
        
        # 计算平均处理时长（简化版本）
        completed_tasks = [task for task in all_tasks if task.get('status') == 'completed']
        average_duration = 0.0
        if completed_tasks:
            durations = []
            for task in completed_tasks:
                if task.get('started_at') and task.get('completed_at'):
                    try:
                        start_time = datetime.fromisoformat(task['started_at'].replace('Z', '+00:00'))
                        end_time = datetime.fromisoformat(task['completed_at'].replace('Z', '+00:00'))
                        duration = (end_time - start_time).total_seconds()
                        durations.append(duration)
                    except:
                        continue
            if durations:
                average_duration = sum(durations) / len(durations)
        
        return TaskStats(
            total=total,
            completed=completed,
            running=running,
            pending=pending,
            failed=failed,
            cancelled=cancelled,
            success_rate=round(success_rate, 2),
            average_duration=round(average_duration, 2)
        )
    
    async def get_query_stats(self) -> StatsResponse:
        """获取查询统计信息"""
        
        stats_data = await asyncio.to_thread(
            self.task_manager.get_statistics
        )
        
        # 转换统计数据
        def convert_stats(data: Dict[str, Any]) -> QueryStats:
            return QueryStats(
                total_queries=data.get('total_queries', 0),
                successful_queries=data.get('successful_queries', 0),
                failed_queries=data.get('failed_queries', 0),
                pending_queries=data.get('pending_queries', 0),
                average_duration=data.get('average_duration', 0.0),
                success_rate=data.get('success_rate', 0.0)
            )
        
        return StatsResponse(
            today=convert_stats(stats_data.get('today', {})),
            this_week=convert_stats(stats_data.get('this_week', {})),
            this_month=convert_stats(stats_data.get('this_month', {})),
            all_time=convert_stats(stats_data.get('all_time', {}))
        )

    def _build_result_files(self, task_info: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """根据任务信息与文件系统推断结果文件路径"""
        try:
            tracking_number = task_info.get('tracking_number') or task_info.get('container_number')
        except Exception:
            tracking_number = None
        if not tracking_number:
            return None

        result_files: Dict[str, str] = {}

        # 从结果字段中提取
        result = task_info.get('result') or {}
        if isinstance(result, dict):
            screenshot = result.get('evidence_screenshot') or result.get('screenshot')
            if isinstance(screenshot, str) and screenshot:
                result_files['screenshot'] = screenshot

        # 从文件系统推断
        folder_records = []
        try:
            folder_records = self.file_manager.get_files_for_tracking(tracking_number) or []
        except Exception:
            folder_records = []
        
        # 扁平化为文件列表
        flat_files = []  # List[Dict[name, path, size, modified]]
        for folder in folder_records:
            for f in folder.get('files', []):
                # 规范化 path，确保是字符串
                p = f.get('path')
                if not isinstance(p, str):
                    continue
                flat_files.append({
                    'name': f.get('name', os.path.basename(p)),
                    'path': p,
                    'size': f.get('size'),
                    'modified': f.get('modified')
                })
        
        if flat_files:
            def pick_latest(substrings: List[str]) -> Optional[str]:
                candidates = [f for f in flat_files if any(s in f.get('name', '') for s in substrings)]
                if not candidates:
                    return None
                # 优先使用记录的修改时间，否则回退到文件系统时间
                def modified_ts(fi: Dict[str, Any]) -> float:
                    m = fi.get('modified')
                    try:
                        return m.timestamp() if m else os.path.getmtime(fi['path'])
                    except Exception:
                        return 0.0
                try:
                    picked = max(candidates, key=modified_ts)
                except Exception:
                    picked = candidates[-1]
                return picked.get('path')

            # 映射不同类型文件
            mapping: Dict[str, List[str]] = {
                'screenshot': ['final_result.png', 'error_screenshot.png', 'screenshot', '.png'],
                'ai_analysis': ['ai_analysis_result_', 'ai_analysis', '.txt'],
                'html_original': ['page_content_original_', 'original_', '.txt'],
                'html_simplified': ['page_content_simplified_', 'simplified_', '.txt'],
            }
            base_dir = getattr(self.file_manager, 'base_dir', 'files')
            for key, patterns in mapping.items():
                if key in result_files:
                    continue
                picked_path = pick_latest(patterns)
                if picked_path:
                    # 返回相对 files 的路径，统一为 URL 友好的正斜杠
                    try:
                        rel = os.path.relpath(picked_path, base_dir).replace('\\', '/')
                    except Exception:
                        rel = picked_path.replace('\\', '/')
                    result_files[key] = rel

        return result_files or None
    
    def _convert_to_task_response(self, task_info: Dict[str, Any]) -> TaskResponse:
        """将任务信息转换为API响应格式"""

        # 状态转换 - Map db status to API status
        status_mapping = {
            'pending': TaskStatus.PENDING,
            'processing': TaskStatus.RUNNING,  # DB uses 'processing', API uses 'running'
            'completed': TaskStatus.COMPLETED,
            'failed': TaskStatus.FAILED,
            'cancelled': TaskStatus.CANCELLED
        }

        # 优先级转换 - DB uses integers, API uses strings
        priority_int = task_info.get('priority', 0)
        if priority_int >= 2:
            priority = TaskPriority.URGENT
        elif priority_int == 1:
            priority = TaskPriority.HIGH
        else:
            priority = TaskPriority.NORMAL

        # Parse datetime strings if they exist
        def parse_datetime(dt_str):
            if not dt_str:
                return None
            try:
                if isinstance(dt_str, str):
                    return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt_str
            except:
                return None

        # 获取物流节点数量：优先使用批量预取的结果，避免频繁连接数据库
        tracking_points_count = task_info.get('precomputed_tracking_points_count', 0) or 0
        if not tracking_points_count:
            try:
                from shipment_manager import ShipmentManager
                shipment_manager = ShipmentManager()
                tracking_number = task_info.get('tracking_number', '')

                if tracking_number:
                    # 搜索对应的货运记录
                    shipments = shipment_manager.search_shipment_records(
                        bill_of_lading=tracking_number
                    )
                    if not shipments:
                        shipments = shipment_manager.search_shipment_records(
                            container_number=tracking_number
                        )

                    if shipments:
                        import sqlite3
                        conn = sqlite3.connect('db/shipment_records.db')
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (shipments[0]['id'],))
                        tracking_points_count = cursor.fetchone()[0]
                        conn.close()
            except Exception:
                # 如果获取失败，保持默认值0
                pass

        # 构建result，包含物流节点信息
        result = task_info.get('result', {}) or {}
        if isinstance(result, dict):
            result['tracking_points_count'] = tracking_points_count

        return TaskResponse(
            task_id=task_info.get('id', ''),  # DB field is 'id', not 'task_id'
            container_number=task_info.get('tracking_number', ''),  # DB field is 'tracking_number'
            carrier_code=task_info.get('carrier'),  # DB field is 'carrier'
            status=status_mapping.get(task_info.get('status', 'pending'), TaskStatus.PENDING),
            priority=priority,
            progress=task_info.get('progress', 0),
            created_at=parse_datetime(task_info.get('created_at')) or datetime.now(),
            updated_at=parse_datetime(task_info.get('updated_at')) or datetime.now(),
            started_at=parse_datetime(task_info.get('started_at')),
            completed_at=parse_datetime(task_info.get('completed_at')),
            error_message=task_info.get('error_message'),
            result=result,
            result_files=self._build_result_files(task_info),
            metadata=task_info.get('metadata', {})
        )

# 依赖注入
_task_service_instance = None


def get_task_service() -> TaskService:
    """获取任务服务实例（单例模式）"""
    global _task_service_instance
    if _task_service_instance is None:
        _task_service_instance = TaskService()
    return _task_service_instance