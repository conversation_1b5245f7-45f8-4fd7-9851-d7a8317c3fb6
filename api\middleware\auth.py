#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证中间件
提供JWT令牌验证和用户权限检查
"""

from typing import Optional, Annotated
from fastapi import Depends, HTTPException, status, Header
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from api.services.auth_service import AuthService, get_current_user
from api.models.user_schemas import UserInfo

# HTTP Bearer 认证方案
security = HTTPBearer()

async def get_authorization_header(
    authorization: Annotated[str | None, Header()] = None
) -> Optional[str]:
    """
    从请求头获取Authorization令牌
    
    Args:
        authorization: Authorization头
        
    Returns:
        提取的令牌，如果不存在则返回None
    """
    if authorization and authorization.startswith("Bearer "):
        return authorization[7:]  # 去掉 "Bearer " 前缀
    return None

async def get_current_user_optional(
    token: Optional[str] = Depends(get_authorization_header)
) -> Optional[UserInfo]:
    """
    获取当前用户（可选，无令牌时返回None）
    
    Args:
        token: JWT令牌
        
    Returns:
        用户信息，如果未认证则返回None
    """
    if not token:
        return None
    
    user_info = get_current_user(token)
    return user_info

async def get_current_user_required(
    token: Optional[str] = Depends(get_authorization_header)
) -> UserInfo:
    """
    获取当前用户（必须认证）
    
    Args:
        token: JWT令牌
        
    Returns:
        用户信息
        
    Raises:
        HTTPException: 如果未认证或认证无效
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_info = get_current_user(token)
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否激活
    if not AuthService.is_active_user(user_info.user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户账户已被禁用",
        )
    
    return user_info

def require_permission(permission: str):
    """
    权限检查依赖注入装饰器
    
    Args:
        permission: 需要的权限
        
    Returns:
        依赖注入函数
    """
    async def permission_dependency(
        current_user: UserInfo = Depends(get_current_user_required)
    ) -> UserInfo:
        if not AuthService.check_permission(current_user.user_id, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {permission}",
            )
        return current_user
    
    return permission_dependency

def require_role(*roles: str):
    """
    角色检查依赖注入装饰器
    
    Args:
        roles: 需要的角色列表
        
    Returns:
        依赖注入函数
    """
    async def role_dependency(
        current_user: UserInfo = Depends(get_current_user_required)
    ) -> UserInfo:
        if current_user.role.value not in roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要以下角色之一: {', '.join(roles)}",
            )
        return current_user
    
    return role_dependency

# 常用权限检查依赖
RequireQueryCreate = Depends(require_permission("query:create"))
RequireQueryView = Depends(require_permission("query:view"))
RequireQueryExport = Depends(require_permission("query:export"))
RequireBatchCreate = Depends(require_permission("batch:create"))
RequireAdmin = Depends(require_role("超级管理员"))
RequireVIPOrAdmin = Depends(require_role("VIP用户", "超级管理员"))

# 用户依赖注入快捷方式
CurrentUser = Depends(get_current_user_required)
CurrentUserOptional = Depends(get_current_user_optional)