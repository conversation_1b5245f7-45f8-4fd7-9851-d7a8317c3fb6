#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
两阶段任务处理器
支持网页抓取和AI分析的分离式异步处理
"""

import os
import sys
import time
import threading
from datetime import datetime
from typing import List, Dict, Optional
import json
import traceback

from task_manager import TaskManager
from scraping_executor import ScrapingExecutor
from ai_analysis_executor import AIAnalysisExecutor
from utils.carrier_lookup import get_company_info
from utils.file_manager import get_file_manager


class TwoStageTaskProcessor:
    """
    两阶段任务处理器类
    支持网页抓取和AI分析的分离式处理
    """

    def __init__(self, max_scraping_tasks: int = 2, max_ai_tasks: int = 1, 
                 check_interval: int = 5, completion_callback=None):
        """
        初始化两阶段任务处理器

        Args:
            max_scraping_tasks: 最大并发网页抓取任务数
            max_ai_tasks: 最大并发AI分析任务数
            check_interval: 检查任务队列的间隔（秒）
            completion_callback: 任务完成时的回调函数，接收(task_id, stage, result_data)参数
        """
        self.task_manager = TaskManager()
        self.max_scraping_tasks = max_scraping_tasks
        self.max_ai_tasks = max_ai_tasks
        self.check_interval = check_interval
        self.completion_callback = completion_callback
        self.running = False
        self.active_scraping_tasks = {}  # 正在处理的抓取任务
        self.active_ai_tasks = {}  # 正在处理的AI分析任务
        self.scraping_threads = []
        self.ai_threads = []
        
        # 创建执行器实例
        self.scraping_executor = ScrapingExecutor(max_scraping_tasks)
        self.ai_executor = AIAnalysisExecutor(max_ai_tasks)

    def process_scraping_task(self, task: Dict) -> bool:
        """
        处理网页抓取任务

        Args:
            task: 任务信息字典

        Returns:
            bool: 处理是否成功
        """
        task_id = task['id']
        tracking_number = task['tracking_number']
        task_type = task['task_type']

        print(f"[SCRAPING] 开始处理抓取任务: {task['task_name']} (ID: {task_id})")

        try:
            # 更新任务状态为处理中
            self.task_manager.update_task_status(task_id, "processing")

            # 根据跟踪号获取承运人信息
            carrier_info = get_company_info(tracking_number)

            if carrier_info:
                # 使用检测到的承运人信息
                url = carrier_info.get('tracking_site', 'https://www.msc.com/en/track-a-shipment')
                input_element_id = carrier_info.get('input_element_id', '#trackingNumber')
                search_button_id = carrier_info.get('search_button_id', None)
                company_name = carrier_info.get('company', '未知')

                print(f"[SCRAPING] 检测到承运人: {company_name}")
                print(f"[SCRAPING] 使用追踪网站: {url}")
                print(f"[SCRAPING] 输入框ID: {input_element_id}")
                print(f"[SCRAPING] 搜索按钮ID: {search_button_id}")
            else:
                # 如果无法检测承运人，使用默认的MSC配置
                url = "https://www.msc.com/en/track-a-shipment"
                input_element_id = "#trackingNumber"
                search_button_id = None
                print(f"[SCRAPING] 无法检测承运人，使用默认MSC配置")

            # 调用网页抓取执行器
            scraping_result = self.scraping_executor.execute_task(task)

            if scraping_result.get('success', False):
                # 抓取成功，更新任务状态和原始数据路径
                data_folder_path = scraping_result.get('data', {}).get('raw_data_path', '')
                self.task_manager.update_task_status(
                    task_id,
                    "completed",
                    result_summary=f"网页抓取完成，数据已保存到: {data_folder_path}"
                )
                
                # 更新原始数据路径
                if data_folder_path:
                    self.task_manager.update_raw_data_path(task_id, data_folder_path)
                
                print(f"[SCRAPING] 抓取任务处理成功: {task['task_name']}")

                # 自动创建AI分析任务
                # 传递原始任务的remarks信息（包含货运记录ID）
                original_remarks = task.get('remarks', '')
                ai_remarks = f"基于抓取任务 {task_id} 自动创建"
                if original_remarks:
                    ai_remarks = f"{original_remarks},{ai_remarks}"
                
                ai_task_id = self.task_manager.create_ai_analysis_task(
                    parent_task_id=task_id,
                    raw_data_path=data_folder_path,
                    priority=task.get('priority', 0),
                    remarks=ai_remarks
                )
                
                if ai_task_id:
                    print(f"[SCRAPING] 已自动创建AI分析任务: {ai_task_id}")
                else:
                    print(f"[SCRAPING] 警告：创建AI分析任务失败")

                # 调用完成回调
                if self.completion_callback:
                    try:
                        self.completion_callback(task_id, 'scraping', scraping_result)
                    except Exception as callback_error:
                        print(f"[SCRAPING] 回调函数执行失败: {callback_error}")

                return True
            else:
                # 抓取失败
                error_msg = scraping_result.get('error', '网页抓取失败')
                error_category = scraping_result.get('error_category', '未知错误')
                user_suggestion = scraping_result.get('user_suggestion', '请检查网络或重试')
                
                # 构建详细的错误摘要
                detailed_summary = f"网页抓取失败 - {error_category}: {error_msg}"
                
                self.task_manager.update_task_status(
                    task_id,
                    "completed",
                    error_message=detailed_summary,
                    result_summary=f"抓取失败({error_category})"
                )
                print(f"[SCRAPING] 抓取任务处理失败: {task['task_name']} - {error_category}")
                print(f"[SCRAPING] 💡 用户建议: {user_suggestion}")
                return False

        except Exception as e:
            error_msg = f"抓取任务处理异常: {str(e)}"
            print(f"[SCRAPING] {error_msg}")
            print(f"[SCRAPING] 错误详情: {traceback.format_exc()}")

            # 更新任务状态为失败
            self.task_manager.update_task_status(
                task_id,
                "completed",
                error_message=error_msg,
                result_summary="抓取任务异常"
            )
            return False
        finally:
            # 从活跃任务列表中移除
            if task_id in self.active_scraping_tasks:
                del self.active_scraping_tasks[task_id]

    def process_ai_analysis_task(self, task: Dict) -> bool:
        """
        处理AI分析任务

        Args:
            task: 任务信息字典

        Returns:
            bool: 处理是否成功
        """
        task_id = task['id']
        tracking_number = task['tracking_number']
        raw_data_path = task.get('raw_data_path')

        print(f"[AI_ANALYSIS] 开始处理AI分析任务: {task['task_name']} (ID: {task_id})")

        try:
            # 更新任务状态为处理中
            self.task_manager.update_task_status(task_id, "processing")

            if not raw_data_path:
                raise ValueError("原始数据路径为空")

            # 调用AI分析执行器
            ai_result = self.ai_executor.execute_task(task)

            if ai_result.get('success', False):
                # AI分析成功
                ai_data = ai_result.get('data', {})
                ai_analysis = ai_data.get('ai_result', '')
                parsed_data = ai_data  # 已经解析好的数据
                
                # 构建结果摘要
                summary_parts = [f"AI分析完成"]
                if parsed_data.get('estimated_arrival_time'):
                    summary_parts.append(f"预计到港: {parsed_data['estimated_arrival_time']}")
                if parsed_data.get('dates_data'):
                    summary_parts.append(f"识别到 {len(parsed_data['dates_data'])} 个时间节点")
                
                result_summary = ", ".join(summary_parts)

                self.task_manager.update_task_status(
                    task_id,
                    "completed",
                    result_summary=result_summary
                )
                
                print(f"[AI_ANALYSIS] AI分析任务处理成功: {task['task_name']}")

                # 调用完成回调
                if self.completion_callback:
                    try:
                        # 合并解析后的数据到结果中
                        callback_data = ai_result.copy()
                        callback_data.update({
                            'parsed_data': parsed_data,
                            'estimated_arrival_time': parsed_data.get('estimated_arrival_time'),
                            'estimated_arrival_port': parsed_data.get('estimated_arrival_port'),
                            'dates_data': parsed_data.get('dates_data', [])
                        })
                        self.completion_callback(task_id, 'ai_analysis', callback_data)
                    except Exception as callback_error:
                        print(f"[AI_ANALYSIS] 回调函数执行失败: {callback_error}")

                return True
            else:
                # AI分析失败
                error_msg = ai_result.get('error', 'AI分析失败')
                self.task_manager.update_task_status(
                    task_id,
                    "completed",
                    error_message=error_msg,
                    result_summary="AI分析失败"
                )
                print(f"[AI_ANALYSIS] AI分析任务处理失败: {task['task_name']} - {error_msg}")
                return False

        except Exception as e:
            error_msg = f"AI分析任务处理异常: {str(e)}"
            print(f"[AI_ANALYSIS] {error_msg}")
            print(f"[AI_ANALYSIS] 错误详情: {traceback.format_exc()}")

            # 更新任务状态为失败
            self.task_manager.update_task_status(
                task_id,
                "completed",
                error_message=error_msg,
                result_summary="AI分析任务异常"
            )
            return False
        finally:
            # 从活跃任务列表中移除
            if task_id in self.active_ai_tasks:
                del self.active_ai_tasks[task_id]

    def _parse_ai_result(self, ai_result: str) -> Dict:
        """
        解析AI分析结果，提取关键信息
        """
        try:
            print(f"[AI_ANALYSIS] 开始解析AI结果，内容长度: {len(ai_result)}")
            
            data = None
            # 查找JSON内容
            lower = ai_result.lower()
            start = lower.find('```json')
            if start != -1:
                end = lower.find('```', start + 7)
                if end != -1:
                    json_str = ai_result[start + 7:end].strip()
                    try:
                        data = json.loads(json_str)
                        print(f"[AI_ANALYSIS] JSON解析成功，keys: {list(data.keys())}")
                    except json.JSONDecodeError as je:
                        print(f"[AI_ANALYSIS] JSON解析失败: {je}")

            # 如果没有找到JSON格式，尝试直接解析
            if data is None:
                i = ai_result.find('{')
                if i != -1:
                    depth = 0
                    j = i
                    while j < len(ai_result):
                        ch = ai_result[j]
                        if ch == '{':
                            depth += 1
                        elif ch == '}':
                            depth -= 1
                            if depth == 0:
                                try:
                                    candidate = ai_result[i:j + 1]
                                    data = json.loads(candidate)
                                    print(f"[AI_ANALYSIS] JSON解析成功（扫描方式），keys: {list(data.keys())}")
                                except Exception:
                                    pass
                                break
                        j += 1

            if data is None:
                print("[AI_ANALYSIS] 未能解析到有效JSON，返回空结果")
                return {}

            result = {
                'dates_data': [],
                'estimated_arrival_time': None,
                'estimated_arrival_port': None,
            }

            # 提取预计到港时间和港口
            eta = data.get('estimated_arrival_time')
            if not eta:
                # 查找可能的同义字段
                for k in ('eta', 'estimated_eta', 'estimated_arrival', 'arrival_eta', 'pod_eta', 'eta_date'):
                    if k in data and data[k]:
                        eta = data[k]
                        print(f"[AI_ANALYSIS] 使用同义字段 '{k}' 作为 ETA")
                        break
            
            result['estimated_arrival_time'] = eta
            result['estimated_arrival_port'] = data.get('estimated_arrival_port') or data.get('pod') or data.get('port')
            
            print(f"[AI_ANALYSIS] AI识别的预计到港时间: {result['estimated_arrival_time']}")
            print(f"[AI_ANALYSIS] AI识别的预计到港港口: {result['estimated_arrival_port']}")

            # 提取时间节点数据
            dates_src = data.get('dates') or data.get('dates_data') or []
            if dates_src:
                print(f"[AI_ANALYSIS] 找到时间节点数组，包含 {len(dates_src)} 个时间节点")
                for i, date_item in enumerate(dates_src):
                    date_record = {
                        'date': date_item.get('date'),
                        'location': date_item.get('location', ''),
                        'event': date_item.get('description') or date_item.get('event', ''),
                        'status': date_item.get('status', ''),
                        'vessel_info': date_item.get('vessel_info', ''),
                        'event_type': date_item.get('type') or date_item.get('event_type', ''),
                    }
                    result['dates_data'].append(date_record)
                    print(f"  [AI_ANALYSIS] 时间节点 {i+1}: {date_record['date']} - {date_record['event_type']}")
            else:
                print(f"[AI_ANALYSIS] JSON数据中未找到dates/dates_data字段")

            print(f"[AI_ANALYSIS] 解析AI结果成功，提取到 {len(result['dates_data'])} 个时间节点")
            return result

        except Exception as e:
            print(f"[AI_ANALYSIS] 解析AI结果失败: {e}")
            print(f"[AI_ANALYSIS] 错误详情: {traceback.format_exc()}")
            return {}

    def scraping_worker_thread(self):
        """
        网页抓取工作线程
        """
        while self.running:
            try:
                # 检查是否有空闲的抓取槽位
                if len(self.active_scraping_tasks) >= self.max_scraping_tasks:
                    time.sleep(1)
                    continue

                # 获取待处理的抓取任务
                pending_tasks = self.task_manager.get_pending_tasks_by_stage('scraping', limit=1)

                if not pending_tasks:
                    time.sleep(self.check_interval)
                    continue

                task = pending_tasks[0]
                task_id = task['id']

                # 添加到活跃任务列表
                self.active_scraping_tasks[task_id] = {
                    'task': task,
                    'start_time': datetime.now()
                }

                # 处理抓取任务
                self.process_scraping_task(task)

            except Exception as e:
                print(f"[SCRAPING] 抓取工作线程异常: {e}")
                time.sleep(5)

    def ai_worker_thread(self):
        """
        AI分析工作线程
        """
        while self.running:
            try:
                # 检查是否有空闲的AI分析槽位
                if len(self.active_ai_tasks) >= self.max_ai_tasks:
                    time.sleep(1)
                    continue

                # 获取待处理的AI分析任务
                pending_tasks = self.task_manager.get_pending_tasks_by_stage('ai_analysis', limit=1)

                if not pending_tasks:
                    time.sleep(self.check_interval)
                    continue

                task = pending_tasks[0]
                task_id = task['id']

                # 添加到活跃任务列表
                self.active_ai_tasks[task_id] = {
                    'task': task,
                    'start_time': datetime.now()
                }

                # 处理AI分析任务
                self.process_ai_analysis_task(task)

            except Exception as e:
                print(f"[AI_ANALYSIS] AI分析工作线程异常: {e}")
                time.sleep(5)

    def start(self):
        """
        启动两阶段任务处理器
        """
        if self.running:
            print("[WARNING] 两阶段任务处理器已在运行中")
            return

        print(f"[INFO] 启动两阶段任务处理器")
        print(f"[INFO] 网页抓取并发数: {self.max_scraping_tasks}")
        print(f"[INFO] AI分析并发数: {self.max_ai_tasks}")
        print(f"[INFO] 检查间隔: {self.check_interval}秒")
        
        self.running = True

        # 启动网页抓取工作线程
        for i in range(self.max_scraping_tasks):
            worker = threading.Thread(
                target=self.scraping_worker_thread,
                name=f"ScrapingWorker-{i+1}"
            )
            worker.daemon = True
            worker.start()
            self.scraping_threads.append(worker)

        # 启动AI分析工作线程
        for i in range(self.max_ai_tasks):
            worker = threading.Thread(
                target=self.ai_worker_thread,
                name=f"AIWorker-{i+1}"
            )
            worker.daemon = True
            worker.start()
            self.ai_threads.append(worker)

        total_threads = len(self.scraping_threads) + len(self.ai_threads)
        print(f"[SUCCESS] 两阶段任务处理器启动成功，{total_threads} 个工作线程已启动")
        print(f"[SUCCESS] - 网页抓取线程: {len(self.scraping_threads)} 个")
        print(f"[SUCCESS] - AI分析线程: {len(self.ai_threads)} 个")

    def stop(self):
        """
        停止两阶段任务处理器
        """
        if not self.running:
            print("[WARNING] 两阶段任务处理器未在运行")
            return

        print("[STOP] 正在停止两阶段任务处理器...")
        self.running = False

        # 等待所有工作线程结束
        all_threads = self.scraping_threads + self.ai_threads
        for worker in all_threads:
            worker.join(timeout=10)

        self.scraping_threads.clear()
        self.ai_threads.clear()
        print("[SUCCESS] 两阶段任务处理器已停止")

    def get_status(self) -> Dict:
        """
        获取处理器状态

        Returns:
            Dict: 状态信息
        """
        return {
            'running': self.running,
            'scraping': {
                'active_tasks_count': len(self.active_scraping_tasks),
                'max_concurrent_tasks': self.max_scraping_tasks,
                'worker_threads_count': len(self.scraping_threads),
                'active_tasks': {
                    task_id: {
                        'task_name': info['task']['task_name'],
                        'start_time': info['start_time'].isoformat(),
                        'duration_seconds': (datetime.now() - info['start_time']).total_seconds()
                    }
                    for task_id, info in self.active_scraping_tasks.items()
                }
            },
            'ai_analysis': {
                'active_tasks_count': len(self.active_ai_tasks),
                'max_concurrent_tasks': self.max_ai_tasks,
                'worker_threads_count': len(self.ai_threads),
                'active_tasks': {
                    task_id: {
                        'task_name': info['task']['task_name'],
                        'start_time': info['start_time'].isoformat(),
                        'duration_seconds': (datetime.now() - info['start_time']).total_seconds()
                    }
                    for task_id, info in self.active_ai_tasks.items()
                }
            }
        }


def run_two_stage_processor_daemon():
    """
    以守护进程模式运行两阶段任务处理器
    """
    processor = TwoStageTaskProcessor(
        max_scraping_tasks=2, 
        max_ai_tasks=1, 
        check_interval=3
    )

    try:
        processor.start()

        print("[INFO] 两阶段任务处理器状态监控 (按 Ctrl+C 停止)")
        print("=" * 60)

        while True:
            time.sleep(10)

            # 显示状态信息
            status = processor.get_status()
            stage_stats = processor.task_manager.get_stage_statistics()

            print(f"\n[TIME] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"[网页抓取] 活跃任务: {status['scraping']['active_tasks_count']}/{status['scraping']['max_concurrent_tasks']}")
            print(f"[AI分析] 活跃任务: {status['ai_analysis']['active_tasks_count']}/{status['ai_analysis']['max_concurrent_tasks']}")
            
            if stage_stats:
                print(f"[统计] 抓取阶段: {stage_stats.get('scraping', {})}")
                print(f"[统计] AI分析阶段: {stage_stats.get('ai_analysis', {})}")

            # 显示活跃任务详情
            all_active = list(status['scraping']['active_tasks'].items()) + list(status['ai_analysis']['active_tasks'].items())
            if all_active:
                print("[活跃任务]:")
                for task_id, task_info in all_active:
                    stage = "抓取" if task_id in status['scraping']['active_tasks'] else "AI分析"
                    print(f"  - [{stage}] {task_info['task_name']} (运行 {task_info['duration_seconds']:.0f}秒)")

    except KeyboardInterrupt:
        print("\n[STOP] 收到停止信号，正在关闭两阶段任务处理器...")
    finally:
        processor.stop()
        print("👋 两阶段任务处理器已关闭")


if __name__ == "__main__":
    # 运行两阶段任务处理器守护进程
    run_two_stage_processor_daemon()