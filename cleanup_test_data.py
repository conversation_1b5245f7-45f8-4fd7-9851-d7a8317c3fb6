#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库测试数据清理工具
"""

import sqlite3
import os
from datetime import datetime

def check_database_records():
    """检查数据库中的记录"""
    print("🔍 检查货运记录数据库...")
    
    db_path = "db/shipment_records.db"
    if not os.path.exists(db_path):
        print("❌ 货运记录数据库不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查看最近10条记录
        cursor.execute("""
            SELECT id, bill_of_lading, container_number, carrier_company, created_by, created_at, status
            FROM shipment_records 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        print(f"📊 最近10条货运记录：")
        print("-" * 100)
        
        test_records = []
        for i, record in enumerate(records, 1):
            created_time = record['created_at']
            print(f"{i:2}. ID: {record['id']}")
            print(f"    提单号: {record['bill_of_lading'] or 'N/A'}")
            print(f"    箱号: {record['container_number'] or 'N/A'}")
            print(f"    船公司: {record['carrier_company'] or 'N/A'}")
            print(f"    创建者: {record['created_by'] or 'N/A'}")
            print(f"    状态: {record['status'] or 'N/A'}")
            print(f"    创建时间: {created_time}")
            
            # 检查是否是测试记录
            if (record['bill_of_lading'] and 'TEST' in record['bill_of_lading'].upper()) or \
               (record['container_number'] and 'TEST' in record['container_number'].upper()) or \
               (record['created_by'] and 'test' in record['created_by'].lower()):
                test_records.append(record)
                print("    ⚠️  [可能是测试记录]")
            
            print()
        
        if test_records:
            print(f"🧪 发现 {len(test_records)} 条可能的测试记录")
            choice = input("是否要删除这些测试记录？(y/n): ").lower().strip()
            
            if choice == 'y':
                for record in test_records:
                    cursor.execute("DELETE FROM shipment_records WHERE id = ?", (record['id'],))
                    print(f"✅ 已删除测试记录: {record['id']} - {record['bill_of_lading'] or record['container_number']}")
                
                conn.commit()
                print(f"🎉 已清理 {len(test_records)} 条测试记录")
            else:
                print("❌ 取消清理操作")
        else:
            print("✅ 没有发现明显的测试记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

def check_task_records():
    """检查任务队列中的记录"""
    print("\n🔍 检查任务队列数据库...")
    
    db_path = "db/task_queue.db"
    if not os.path.exists(db_path):
        print("❌ 任务队列数据库不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查看最近10条任务
        cursor.execute("""
            SELECT id, task_name, tracking_number, task_type, status, created_at
            FROM task_queue 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        
        print(f"📊 最近10条任务记录：")
        print("-" * 80)
        
        test_tasks = []
        for i, task in enumerate(tasks, 1):
            print(f"{i:2}. ID: {task['id']}")
            print(f"    任务名: {task['task_name'] or 'N/A'}")
            print(f"    跟踪号: {task['tracking_number'] or 'N/A'}")
            print(f"    类型: {task['task_type'] or 'N/A'}")
            print(f"    状态: {task['status'] or 'N/A'}")
            print(f"    创建时间: {task['created_at']}")
            
            # 检查是否是测试任务
            if (task['tracking_number'] and 'TEST' in task['tracking_number'].upper()) or \
               (task['task_name'] and 'test' in task['task_name'].lower()):
                test_tasks.append(task)
                print("    ⚠️  [可能是测试任务]")
            
            print()
        
        if test_tasks:
            print(f"🧪 发现 {len(test_tasks)} 条可能的测试任务")
            choice = input("是否要删除这些测试任务？(y/n): ").lower().strip()
            
            if choice == 'y':
                for task in test_tasks:
                    cursor.execute("DELETE FROM task_queue WHERE id = ?", (task['id'],))
                    print(f"✅ 已删除测试任务: {task['id']} - {task['tracking_number']}")
                
                conn.commit()
                print(f"🎉 已清理 {len(test_tasks)} 条测试任务")
            else:
                print("❌ 取消清理操作")
        else:
            print("✅ 没有发现明显的测试任务")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查任务数据库时出错: {e}")

def main():
    """主函数"""
    print("🧹 数据库测试数据清理工具")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    check_database_records()
    check_task_records()
    
    print("\n" + "=" * 50)
    print("✅ 清理完成！")
    print("💡 如果问题仍然存在，请检查：")
    print("   1. 是否有其他程序在后台运行")
    print("   2. 是否有定时任务在自动创建记录")
    print("   3. 输入框的默认值设置")

if __name__ == "__main__":
    main()