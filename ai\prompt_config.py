#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 提示词与规则的可维护配置加载器
- 从 config/prompts.json 读取可编辑的提示词
- 若配置缺失/解析失败，回退到内置默认文案
- 使用简单的占位符替换（避免 .format 与 JSON 大括号冲突）

占位符：
- 文本分析 prompt: {simplified_html}
- 视觉分析用户提示: {task_prompt} / {history_str}
"""
from __future__ import annotations
import json
import os
from typing import Dict

_CONFIG_CACHE: Dict | None = None
_CONFIG_PATH = os.path.join('config', 'prompts.json')


def _load_config() -> Dict:
    global _CONFIG_CACHE
    if _CONFIG_CACHE is not None:
        return _CONFIG_CACHE
    try:
        with open(_CONFIG_PATH, 'r', encoding='utf-8') as f:
            _CONFIG_CACHE = json.load(f)
            return _CONFIG_CACHE
    except Exception:
        _CONFIG_CACHE = {}
        return _CONFIG_CACHE


# 默认内置文案（与当前写死的逻辑等效）
_DEFAULT_TEXT_ANALYZER_TEMPLATE = """
你是一个专业的物流追踪信息分析专家。请分析以下经过结构化处理的HTML内容，提取所有与船期、物流相关的日期信息。

**输出格式**：请严格按以下JSON输出，并按时间倒序排列：
```json
{
  "estimated_arrival_time": "YYYY-MM-DD",
  "estimated_arrival_port": "港口",
  "dates": [
    {
      "date": "YYYY-MM-DD",
      "original_format": "原始格式",
      "type": "POD_ETA/ETD/转运等",
      "location": "地点",
      "description": "描述",
      "status": "estimated/actual",
      "vessel_info": "船只信息",
      "context": "上下文"
    }
  ]
}
```

**待分析的HTML内容**：
{simplified_html}
"""

_DEFAULT_VISION_SYSTEM_PROMPT = """
你是一个专业的网页自动化操作助手。你的任务是分析当前网页截图，并根据用户的目标任务，决定下一步应该执行的操作。

**可用操作类型：**
- click: 点击指定坐标
- type: 在当前焦点输入文本
- scroll: 滚动页面
- wait: 等待页面加载
- complete: 任务完成

**输出格式（必须为JSON）**：
{
  "action": "操作类型",
  "x": 坐标x（仅click时需要),
  "y": 坐标y（仅click时需要),
  "text": "输入文本（仅type时需要）",
  "reasoning": "操作理由和观察到的页面状态"
}
"""

_DEFAULT_VISION_USER_TEMPLATE = """
**当前任务：** {task_prompt}

**操作历史：**
{history_str}

**请分析当前截图，决定下一步操作：**
"""

def reload_prompt_config() -> None:
    """清空缓存，便于热加载"""
    global _CONFIG_CACHE
    _CONFIG_CACHE = None


def get_text_analyzer_prompt(simplified_html: str) -> str:
    cfg = _load_config().get('text_analyzer', {})
    tpl = cfg.get('prompt_template') or _DEFAULT_TEXT_ANALYZER_TEMPLATE
    return tpl.replace('{simplified_html}', simplified_html)


def get_vision_system_prompt() -> str:
    cfg = _load_config().get('vision_analyzer', {})
    return cfg.get('system_prompt') or _DEFAULT_VISION_SYSTEM_PROMPT


def get_vision_user_text(task_prompt: str, history_str: str) -> str:
    cfg = _load_config().get('vision_analyzer', {})
    tpl = cfg.get('user_text_template') or _DEFAULT_VISION_USER_TEMPLATE
    return tpl.replace('{task_prompt}', task_prompt).replace('{history_str}', history_str)

