#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试货运记录状态停留在"处理中"的问题
"""

import sqlite3
from datetime import datetime, timedelta

def debug_stuck_status():
    """调试状态卡住的问题"""
    print("=== 调试货运记录状态问题 ===\n")
    
    # 1. 检查所有处于"处理中"状态的记录
    print("1. 检查处于'处理中'状态的记录...")
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    cursor.execute("""
        SELECT id, bill_of_lading, container_number, status, updated_at, remarks
        FROM shipment_records
        WHERE status = '处理中'
        ORDER BY updated_at DESC
    """)
    records = cursor.fetchall()
    conn.close()
    
    if records:
        print(f"   找到 {len(records)} 个处于'网页抓取中'状态的记录:")
        for record in records:
            record_id, bill, container, status, updated_at, remarks = record
            tracking_number = bill or container
            print(f"   - 记录ID: {record_id}")
            print(f"     跟踪号: {tracking_number}")
            print(f"     状态: {status}")
            print(f"     更新时间: {updated_at}")
            print(f"     备注: {remarks}")
            print()
    else:
        print("   没有找到处于'网页抓取中'状态的记录")
        return
    
    # 2. 检查对应的任务状态
    print("2. 检查对应的任务状态...")
    task_conn = sqlite3.connect('db/task_queue.db')
    task_cursor = task_conn.cursor()
    
    for record in records:
        record_id, bill, container, status, updated_at, remarks = record
        tracking_number = bill or container
        
        print(f"   检查跟踪号: {tracking_number}")
        
        # 查找网页抓取任务
        task_cursor.execute("""
            SELECT id, task_name, status, task_stage, created_at, started_at, completed_at, error_message
            FROM task_queue 
            WHERE tracking_number = ? AND task_stage = 'scraping'
            ORDER BY created_at DESC
            LIMIT 3
        """, (tracking_number,))
        
        scraping_tasks = task_cursor.fetchall()
        
        if scraping_tasks:
            print(f"     网页抓取任务 ({len(scraping_tasks)}个):")
            for task in scraping_tasks:
                task_id, task_name, task_status, stage, created, started, completed, error = task
                print(f"       ID: {task_id}")
                print(f"       状态: {task_status}")
                print(f"       创建时间: {created}")
                print(f"       开始时间: {started}")
                print(f"       完成时间: {completed}")
                if error:
                    print(f"       错误信息: {error}")
                print()
        
        # 查找AI分析任务
        task_cursor.execute("""
            SELECT id, task_name, status, task_stage, created_at, started_at, completed_at, error_message
            FROM task_queue 
            WHERE tracking_number = ? AND task_stage = 'ai_analysis'
            ORDER BY created_at DESC
            LIMIT 3
        """, (tracking_number,))
        
        ai_tasks = task_cursor.fetchall()
        
        if ai_tasks:
            print(f"     AI分析任务 ({len(ai_tasks)}个):")
            for task in ai_tasks:
                task_id, task_name, task_status, stage, created, started, completed, error = task
                print(f"       ID: {task_id}")
                print(f"       状态: {task_status}")
                print(f"       创建时间: {created}")
                print(f"       开始时间: {started}")
                print(f"       完成时间: {completed}")
                if error:
                    print(f"       错误信息: {error}")
                print()
        
        if not scraping_tasks and not ai_tasks:
            print("     没有找到相关任务")
    
    task_conn.close()
    
    # 3. 检查任务处理器状态
    print("3. 检查最近的任务活动...")
    task_conn = sqlite3.connect('db/task_queue.db')
    task_cursor = task_conn.cursor()
    
    # 最近完成的任务
    task_cursor.execute("""
        SELECT tracking_number, task_stage, status, completed_at, error_message
        FROM task_queue 
        WHERE status IN ('completed', 'failed') 
        AND completed_at IS NOT NULL
        ORDER BY completed_at DESC
        LIMIT 10
    """)
    
    recent_tasks = task_cursor.fetchall()
    
    if recent_tasks:
        print("   最近完成的任务:")
        for task in recent_tasks:
            tracking, stage, status, completed, error = task
            print(f"   - {tracking} ({stage}): {status} at {completed}")
            if error:
                print(f"     错误: {error}")
    
    # 当前处理中的任务
    task_cursor.execute("""
        SELECT tracking_number, task_stage, started_at
        FROM task_queue 
        WHERE status = 'processing'
        ORDER BY started_at DESC
    """)
    
    processing_tasks = task_cursor.fetchall()
    
    if processing_tasks:
        print("\n   当前处理中的任务:")
        for task in processing_tasks:
            tracking, stage, started = task
            print(f"   - {tracking} ({stage}): 开始于 {started}")
    else:
        print("\n   当前没有处理中的任务")
    
    task_conn.close()
    
    print("\n=== 诊断建议 ===")
    print("请检查以上信息，常见问题:")
    print("1. 网页抓取任务失败 - 检查error_message")
    print("2. AI分析任务未创建 - 网页抓取可能未正确完成")
    print("3. 任务处理器未运行 - 检查定时任务处理器状态")
    print("4. 回调机制未触发 - 检查完成时间但状态未更新的情况")

if __name__ == "__main__":
    debug_stuck_status()