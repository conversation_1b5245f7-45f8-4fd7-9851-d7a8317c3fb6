#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件服务API路由
提供文件访问和下载服务
"""

import os
import mimetypes
from pathlib import Path
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import FileResponse, Response
from fastapi.security import HTT<PERSON>Bearer

from api.utils.logger import api_logger
from utils.file_manager import FileManager, get_file_manager

router = APIRouter()
security = HTTPBearer(auto_error=False)

@router.get("/files")
async def get_file(
    path: str = Query(..., description="文件路径"),
    download: bool = Query(False, description="是否作为下载"),
    file_manager: FileManager = Depends(get_file_manager)
):
    """
    获取文件
    
    - **path**: 文件路径（相对于files目录或绝对路径）
    - **download**: 是否强制下载（true时返回下载响应）
    """
    try:
        api_logger.info(f"请求文件: {path}")
        
        if not path:
            raise HTTPException(status_code=400, detail="文件路径不能为空")
        
        base_dir = Path(file_manager.base_dir).resolve()
        
        # 统一与规范化路径，并进行安全校验
        if os.path.isabs(path):
            file_path = Path(path).resolve()
            # 绝对路径必须在 files 基础目录下
            if not str(file_path).startswith(str(base_dir)):
                raise HTTPException(status_code=403, detail="访问被拒绝")
        else:
            # 相对路径基于 files 目录
            file_path = (base_dir / path).resolve()
            if not str(file_path).startswith(str(base_dir)):
                # 防止路径遍历
                raise HTTPException(status_code=403, detail="访问被拒绝")
        
        # 检查文件是否存在
        if not file_path.exists():
            api_logger.warning(f"文件不存在: {file_path}")
            raise HTTPException(status_code=404, detail="文件不存在")
        
        if not file_path.is_file():
            raise HTTPException(status_code=400, detail="不是有效文件")
        
        # 获取文件信息
        file_size = file_path.stat().st_size
        
        # 检查文件大小限制（例如最大100MB）
        max_size = 100 * 1024 * 1024  # 100MB
        if file_size > max_size:
            raise HTTPException(status_code=413, detail="文件过大")
        
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type is None:
            mime_type = 'application/octet-stream'
        
        # 设置文件名
        filename = file_path.name
        
        api_logger.info(f"返回文件: {file_path} (大小: {file_size}, 类型: {mime_type})")
        
        # 根据download参数决定响应类型
        if download:
            return FileResponse(
                path=str(file_path),
                media_type=mime_type,
                filename=filename,
                headers={"Content-Disposition": f"attachment; filename=\"{filename}\""}
            )
        else:
            # 直接显示文件（适用于图片、文档等）
            return FileResponse(
                path=str(file_path),
                media_type=mime_type,
                headers={"Content-Disposition": f"inline; filename=\"{filename}\""}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取文件异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.get("/files/list/{tracking_number}")
async def list_files_for_tracking(
    tracking_number: str,
    file_manager: FileManager = Depends(get_file_manager)
):
    """
    获取指定跟踪号的所有文件列表
    
    - **tracking_number**: 跟踪号（箱号或提单号）
    """
    try:
        api_logger.info(f"获取文件列表: {tracking_number}")
        
        files_data = file_manager.get_files_for_tracking(tracking_number)
        
        # 转换为API友好的格式
        result = []
        base_dir = Path(file_manager.base_dir).resolve()
        for folder_record in files_data:
            folder_info = {
                'folder_name': folder_record['folder_name'],
                'created': folder_record['created'].isoformat(),
                'files': []
            }
            
            for file_info in folder_record['files']:
                # 构建相对于files目录的路径（标准化为正斜杠，便于URL传参）
                relative_path = os.path.relpath(file_info['path'], file_manager.base_dir)
                relative_path = relative_path.replace('\\', '/')
                folder_info['files'].append({
                    'name': file_info['name'],
                    'path': relative_path,  # 相对路径（URL 友好）
                    'size': file_info['size'],
                    'modified': file_info['modified'].isoformat(),
                    'url': f"/api/files?path={relative_path}"  # 提供直接访问URL
                })
            
            result.append(folder_info)
        
        api_logger.info(f"文件列表获取成功: {tracking_number} - {len(result)} 个文件夹")
        
        return {
            'tracking_number': tracking_number,
            'folders': result,
            'total_folders': len(result),
            'total_files': sum(len(folder['files']) for folder in result)
        }
        
    except Exception as e:
        api_logger.error(f"获取文件列表异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.head("/files")
async def head_file(
    path: str = Query(..., description="文件路径"),
    file_manager: FileManager = Depends(get_file_manager)
):
    """
    获取文件头信息（不返回文件内容）
    用于检查文件是否存在和获取文件信息
    """
    try:
        if not path:
            raise HTTPException(status_code=400, detail="文件路径不能为空")
        
        base_dir = Path(file_manager.base_dir).resolve()
        
        # 构建并规范化文件路径
        if os.path.isabs(path):
            file_path = Path(path).resolve()
            if not str(file_path).startswith(str(base_dir)):
                raise HTTPException(status_code=403, detail="访问被拒绝")
        else:
            file_path = (base_dir / path).resolve()
            if not str(file_path).startswith(str(base_dir)):
                raise HTTPException(status_code=403, detail="访问被拒绝")
        
        if not file_path.exists() or not file_path.is_file():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 获取文件信息
        file_size = file_path.stat().st_size
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type is None:
            mime_type = 'application/octet-stream'
        
        # 返回空响应，但包含头信息
        return Response(
            content="",
            headers={
                "Content-Length": str(file_size),
                "Content-Type": mime_type,
                "Last-Modified": str(file_path.stat().st_mtime)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取文件头信息异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")