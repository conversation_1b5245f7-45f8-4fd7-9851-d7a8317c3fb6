[QUICK CREATE] 路由被调用，container_number: FULLTEST001
[ROUTER DEBUG] 用户 李乐 快速创建任务: FULLTEST001
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_4b43339d491a' container_number='FULLTEST001' carrier_code=None status='pending' priority='normal' user_id='001' created_at=datetime.datetime(2025, 8, 28, 8, 32, 58, 704235) updated_at=datetime.datetime(2025, 8, 28, 8, 32, 58, 704239) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 测试用户 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_fc62d964c81f' container_number='MEDUJ0618622' carrier_code='MSC' status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 28, 14, 41, 33, 204930) updated_at=datetime.datetime(2025, 8, 28, 14, 41, 33, 204959) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 测试用户 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_926bbcdc483c' container_number='MEDUJ0616089' carrier_code='MSC' status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 28, 15, 41, 44, 539456) updated_at=datetime.datetime(2025, 8, 28, 15, 41, 44, 539461) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_a7d057bdb370' container_number='MEDUJ0616089' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 10, 8, 4, 121662) updated_at=datetime.datetime(2025, 8, 29, 10, 8, 4, 121677) result=None
