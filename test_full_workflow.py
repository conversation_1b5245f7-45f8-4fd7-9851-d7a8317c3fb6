#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的任务执行流程
模拟从任务创建到数据存储的完整过程
"""

import os
import sys
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from task_manager import TaskManager
from shipment_manager import ShipmentManager
from task_processor import TaskProcessor

def test_full_workflow():
    """测试完整的工作流程"""
    print("=== 完整工作流程测试 ===")
    
    try:
        # 1. 创建ShipmentManager和TaskProcessor
        shipment_manager = ShipmentManager()
        
        def completion_callback(task_id, result_data):
            """任务完成回调函数"""
            print(f"[CALLBACK] 任务完成回调被调用: {task_id}")
            print(f"[CALLBACK] 结果数据: {result_data}")
            
            # 调用ShipmentManager的处理方法
            success = shipment_manager.handle_task_completion(task_id, result_data)
            print(f"[CALLBACK] 处理结果: {'成功' if success else '失败'}")
        
        # 创建带有回调的TaskProcessor
        task_processor = TaskProcessor(completion_callback=completion_callback)
        
        # 2. 创建一个测试货运记录
        record_id = shipment_manager.create_shipment_record(
            bill_of_lading="WORKFLOW_TEST_001",
            carrier_company="MSC",
            created_by="workflow_test"
        )
        
        print(f"[INFO] 创建货运记录成功，ID: {record_id}")
        
        # 3. 从货运记录的备注中提取任务ID
        record = shipment_manager.get_shipment_record(record_id)
        if record and record.get('remarks'):
            import re
            match = re.search(r'任务ID: ([a-f0-9-]+)', record['remarks'])
            if match:
                task_id = match.group(1)
                print(f"[INFO] 找到关联任务ID: {task_id}")
                
                # 4. 模拟AI分析结果
                mock_ai_result = '''
提单号: WORKFLOW_TEST_001
分析时间: 2025-01-08 10:00:00
==================================================

```json
{
  "estimated_arrival_time": "2025-04-01",
  "estimated_arrival_port": "Hamburg DE",
  "dates": [
    {
      "date": "2025-02-15",
      "original_format": "15/02/2025",
      "type": "ETD",
      "location": "Shanghai CN",
      "description": "Estimated Time of Departure",
      "status": "estimated",
      "vessel_info": "MSC WORKFLOW TEST",
      "context": "从上海港预计离港"
    },
    {
      "date": "2025-03-20",
      "original_format": "20/03/2025", 
      "type": "Transshipment",
      "location": "Singapore SG",
      "description": "Container Transshipment",
      "status": "estimated",
      "vessel_info": "MSC WORKFLOW TEST",
      "context": "新加坡转运"
    },
    {
      "date": "2025-04-01",
      "original_format": "01/04/2025",
      "type": "ETA",
      "location": "Hamburg DE",
      "description": "Estimated Time of Arrival",
      "status": "estimated",
      "vessel_info": "MSC WORKFLOW TEST",
      "context": "预计到达汉堡港"
    }
  ]
}
```

分析完成。该提单号包含3个重要时间节点，预计到港时间为2025-04-01，目的港为Hamburg DE。
                '''
                
                # 5. 模拟任务处理器解析AI结果
                parsed_data = task_processor._parse_ai_result(mock_ai_result)
                print(f"[INFO] AI结果解析完成: {len(parsed_data.get('dates_data', []))} 个时间节点")
                
                # 6. 模拟完整的任务完成数据
                complete_result_data = {
                    'estimated_arrival_time': parsed_data.get('estimated_arrival_time'),
                    'estimated_arrival_port': parsed_data.get('estimated_arrival_port'),
                    'dates_data': parsed_data.get('dates_data', []),
                    'ai_result': mock_ai_result,
                    'result_files': {
                        'ai_analysis': 'mock_ai_result.txt',
                        'screenshot': 'mock_screenshot.png'
                    }
                }
                
                # 7. 调用完成回调，模拟任务处理器的行为
                print(f"[INFO] 模拟调用任务完成回调...")
                completion_callback(task_id, complete_result_data)
                
                # 8. 验证最终结果
                final_record = shipment_manager.get_shipment_record(record_id)
                if final_record:
                    print(f"\n[SUCCESS] 工作流程测试成功！")
                    print(f"最终货运记录状态:")
                    print(f"  - 提单号: {final_record['bill_of_lading']}")
                    print(f"  - 状态: {final_record['status']}")
                    print(f"  - 预计到港时间: {final_record.get('estimated_arrival_time', 'None')}")
                    print(f"  - 时间节点数量: {len(final_record.get('dates', []))}")
                    
                    for i, date_info in enumerate(final_record.get('dates', [])):
                        print(f"    节点{i+1}: {date_info.get('date')} - {date_info.get('description')} @ {date_info.get('location')}")
                else:
                    print(f"[ERROR] 无法获取最终记录")
                    
            else:
                print(f"[ERROR] 无法从备注中提取任务ID")
        else:
            print(f"[ERROR] 记录或备注为空")
            
    except Exception as e:
        print(f"[ERROR] 工作流程测试失败: {e}")
        import traceback
        print(traceback.format_exc())

def test_ai_result_parsing():
    """专门测试AI结果解析功能"""
    print("\n=== AI结果解析测试 ===")
    
    # 创建TaskProcessor实例
    task_processor = TaskProcessor()
    
    # 测试用的AI结果
    test_ai_result = '''
这是一个测试的AI分析结果。

```json
{
  "estimated_arrival_time": "2025-05-15",
  "estimated_arrival_port": "Los Angeles CA",
  "dates": [
    {
      "date": "2025-03-01",
      "original_format": "01/03/2025",
      "type": "Loading",
      "location": "Shenzhen CN",
      "description": "Container Loading at Origin",
      "status": "actual",
      "vessel_info": "TEST VESSEL 001",
      "context": "货物装载"
    },
    {
      "date": "2025-05-15", 
      "original_format": "15/05/2025",
      "type": "ETA",
      "location": "Los Angeles CA",
      "description": "Estimated Time of Arrival",
      "status": "estimated",
      "vessel_info": "TEST VESSEL 001",
      "context": "预计到达洛杉矶"
    }
  ]
}
```

分析完成。
    '''
    
    try:
        parsed_data = task_processor._parse_ai_result(test_ai_result)
        
        print(f"[SUCCESS] AI解析测试成功:")
        print(f"  - 预计到港时间: {parsed_data.get('estimated_arrival_time')}")
        print(f"  - 预计到港港口: {parsed_data.get('estimated_arrival_port')}")
        print(f"  - 时间节点数量: {len(parsed_data.get('dates_data', []))}")
        
        for i, date_item in enumerate(parsed_data.get('dates_data', [])):
            print(f"    节点{i+1}: {date_item.get('date')} - {date_item.get('event')} @ {date_item.get('location')}")
            
    except Exception as e:
        print(f"[ERROR] AI解析测试失败: {e}")

def main():
    """主函数"""
    print("开始完整工作流程测试...")
    
    # 测试AI结果解析
    test_ai_result_parsing()
    
    # 测试完整工作流程
    test_full_workflow()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()