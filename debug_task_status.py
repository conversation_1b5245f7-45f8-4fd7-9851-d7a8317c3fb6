#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试任务状态问题
"""

import sqlite3
import os
from datetime import datetime

def debug_task_status():
    """调试任务状态问题"""
    print("🔍 开始调试任务状态问题...")
    
    if not os.path.exists('db/task_queue.db'):
        print("❌ 任务队列数据库不存在")
        return
    
    conn = sqlite3.connect('db/task_queue.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("\n1. 📊 查看所有任务状态分布:")
    cursor.execute("""
        SELECT task_stage, status, COUNT(*) as count
        FROM task_queue 
        GROUP BY task_stage, status
        ORDER BY task_stage, status
    """)
    
    stage_status = cursor.fetchall()
    for row in stage_status:
        print(f"   {row['task_stage']:15} {row['status']:15} {row['count']:3} 个")
    
    print("\n2. 📋 查看最近创建的任务详情:")
    cursor.execute("""
        SELECT id, task_name, tracking_number, task_stage, status, 
               created_at, started_at, completed_at, parent_task_id
        FROM task_queue 
        ORDER BY created_at DESC 
        LIMIT 10
    """)
    
    recent_tasks = cursor.fetchall()
    for task in recent_tasks:
        print(f"   ID: {task['id'][:8]}...")
        print(f"     任务名: {task['task_name']}")
        print(f"     跟踪号: {task['tracking_number']}")
        print(f"     阶段: {task['task_stage']}")
        print(f"     状态: {task['status']}")
        print(f"     创建: {task['created_at']}")
        print(f"     开始: {task['started_at']}")
        print(f"     完成: {task['completed_at']}")
        print(f"     父任务: {task['parent_task_id']}")
        print("     ---")
    
    print("\n3. 🔍 分析特定跟踪号的任务:")
    # 查找有多个任务的跟踪号
    cursor.execute("""
        SELECT tracking_number, COUNT(*) as task_count
        FROM task_queue 
        GROUP BY tracking_number
        HAVING COUNT(*) > 1
        ORDER BY task_count DESC
        LIMIT 3
    """)
    
    multi_tasks = cursor.fetchall()
    for row in multi_tasks:
        tracking_number = row['tracking_number']
        print(f"\n   跟踪号: {tracking_number} (共 {row['task_count']} 个任务)")
        
        cursor.execute("""
            SELECT task_stage, status, created_at, id
            FROM task_queue 
            WHERE tracking_number = ?
            ORDER BY created_at DESC
        """, (tracking_number,))
        
        tasks_for_tracking = cursor.fetchall()
        for task in tasks_for_tracking:
            print(f"     {task['task_stage']:15} {task['status']:15} {task['created_at']} {task['id'][:8]}...")
    
    print("\n4. ⚠️  检查问题模式:")
    
    # 检查是否有AI分析任务在没有对应抓取任务的情况下被创建
    cursor.execute("""
        SELECT COUNT(*) as ai_without_scraping
        FROM task_queue ai
        WHERE ai.task_stage = 'ai_analysis'
        AND NOT EXISTS (
            SELECT 1 FROM task_queue s 
            WHERE s.tracking_number = ai.tracking_number 
            AND s.task_stage = 'scraping'
        )
    """)
    ai_without_scraping = cursor.fetchone()['ai_without_scraping']
    print(f"   AI任务无对应抓取任务: {ai_without_scraping} 个")
    
    # 检查是否有完成的任务但显示错误状态
    cursor.execute("""
        SELECT tracking_number, task_stage, status, completed_at
        FROM task_queue 
        WHERE completed_at IS NOT NULL AND status != 'completed'
    """)
    inconsistent_status = cursor.fetchall()
    if inconsistent_status:
        print(f"   状态不一致的任务: {len(inconsistent_status)} 个")
        for task in inconsistent_status:
            print(f"     {task['tracking_number']} {task['task_stage']} {task['status']} (已有完成时间)")
    
    conn.close()
    print("\n✅ 调试完成")

def simulate_task_status_query(tracking_number):
    """模拟app.py中get_task_stages_status的查询逻辑"""
    print(f"\n🔍 模拟查询跟踪号: {tracking_number}")
    
    conn = sqlite3.connect('db/task_queue.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # 使用与app.py相同的查询
    cursor.execute("""
        SELECT task_stage, status, id, error_message 
        FROM task_queue 
        WHERE tracking_number = ? 
        AND status IN ('pending', 'processing', 'completed', 'failed')
        ORDER BY created_at DESC
    """, (tracking_number,))
    
    tasks = cursor.fetchall()
    print(f"   查询返回 {len(tasks)} 个任务:")
    
    scraping_status = {"status": "未开始", "clickable": False, "task_id": None}
    ai_status = {"status": "未开始", "clickable": False, "task_id": None}
    
    latest_scraping_task = None
    latest_ai_task = None
    
    for i, (task_stage, status, task_id, error_message) in enumerate(tasks):
        print(f"     {i+1}. {task_stage:15} {status:15} {task_id[:8]}...")
        
        if task_stage == 'scraping' and latest_scraping_task is None:
            latest_scraping_task = (task_stage, status, task_id, error_message)
        elif task_stage == 'ai_analysis' and latest_ai_task is None:
            latest_ai_task = (task_stage, status, task_id, error_message)
    
    print(f"\n   最终状态:")
    if latest_scraping_task:
        scraping_status["status"] = latest_scraping_task[1]
        print(f"     网页抓取: {scraping_status['status']}")
    else:
        print(f"     网页抓取: {scraping_status['status']}")
        
    if latest_ai_task:
        ai_status["status"] = latest_ai_task[1]
        print(f"     AI分析: {ai_status['status']}")
    else:
        print(f"     AI分析: {ai_status['status']}")
    
    conn.close()

if __name__ == "__main__":
    debug_task_status()
    
    # 如果有任务，模拟查询最新的跟踪号
    if os.path.exists('db/task_queue.db'):
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT tracking_number FROM task_queue ORDER BY rowid DESC LIMIT 1")
        result = cursor.fetchone()
        if result:
            simulate_task_status_query(result[0])
        conn.close()