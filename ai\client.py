#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI客户端配置模块
统一管理AI客户端的初始化和配置
"""

import os
from openai import OpenAI
from typing import Optional, Dict, Any

# 导入配置管理器
try:
    from utils.ai_config_manager import AIConfigManager
except ImportError:
    # 如果导入失败，使用相对导入
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.ai_config_manager import AIConfigManager

# 不再提供默认配置，必须从全局配置获取

class AIClient:
    """AI客户端管理类"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None, 
                 use_global_config: bool = True):
        """
        初始化AI客户端
        
        Args:
            api_key: API密钥，如果不提供则使用默认值或全局配置
            base_url: API基础URL，如果不提供则使用默认值或全局配置
            use_global_config: 是否使用全局配置，默认为True
        """
        self.use_global_config = use_global_config
        self.config_manager = None
        
        if use_global_config:
            try:
                self.config_manager = AIConfigManager()
            except Exception as e:
                print(f"警告：无法加载全局配置，将使用默认配置: {e}")
                self.use_global_config = False
        
        # 设置API配置
        if self.use_global_config and self.config_manager:
            # 尝试从全局配置获取默认提供商信息
            try:
                providers = self.config_manager.get_all_providers()
                if providers:
                    # 使用第一个活跃的提供商作为默认配置
                    default_provider = next((p for p in providers if p.get('is_active')), None)
                    if default_provider:
                        self.api_key = api_key or default_provider.get('api_key')
                        self.base_url = base_url or default_provider.get('base_url')
                        if not self.api_key or not self.base_url:
                            raise ValueError("全局配置中缺少必要的API密钥或基础URL")
                    else:
                        raise ValueError("没有找到活跃的AI提供商配置")
                else:
                    raise ValueError("没有找到任何AI提供商配置")
            except Exception as e:
                raise ValueError(f"无法加载全局配置: {e}")
        else:
            if not api_key or not base_url:
                raise ValueError("必须提供API密钥和基础URL")
            self.api_key = api_key
            self.base_url = base_url
        
        if not self.api_key or self.api_key == "在此处填入您的真实API密钥":
            raise ValueError("错误：请设置有效的API密钥")
        
        self._client = None
    
    @property
    def client(self) -> OpenAI:
        """获取OpenAI客户端实例（懒加载）"""
        if self._client is None:
            self._client = OpenAI(
                base_url=self.base_url,
                api_key=self.api_key,
            )
        return self._client
    
    def get_vision_model(self) -> str:
        """获取视觉模型名称"""
        return self._get_model_by_type('image')
    
    def get_text_model(self) -> str:
        """获取文本模型名称"""
        return self._get_model_by_type('text')
    
    def get_audio_model(self) -> str:
        """获取音频模型名称"""
        return self._get_model_by_type('audio')
    
    def get_video_model(self) -> str:
        """获取视频模型名称"""
        return self._get_model_by_type('video')
    
    def _get_model_by_type(self, model_type: str) -> str:
        """根据模型类型获取模型名称"""
        if not self.use_global_config or not self.config_manager:
            raise ValueError("必须启用全局配置才能获取模型")
        
        try:
            # 从全局配置获取启用的模型
            active_models = self.config_manager.get_active_models()
            model_info = active_models.get(model_type, {})
            
            # 检查模型是否启用
            if not model_info.get('is_enabled', False) or not model_info.get('provider_id'):
                raise ValueError(f"{model_type}模型未启用或未配置")
            
            # 获取提供商信息
            provider_id = model_info['provider_id']
            providers = self.config_manager.get_all_providers()
            provider = next((p for p in providers if p['id'] == provider_id), None)
            
            if not provider or not provider.get('is_active'):
                raise ValueError(f"{model_type}模型的提供商未找到或未激活")
            
            # 根据模型类型获取对应的模型名称
            model_field = f'{model_type}_model'
            model_name = provider.get(model_field)
            
            if not model_name:
                raise ValueError(f"提供商未配置{model_type}模型")
            
            return model_name
            
        except Exception as e:
            raise ValueError(f"获取{model_type}模型配置失败: {e}")
    
    def is_model_enabled(self, model_type: str) -> bool:
        """检查指定类型的模型是否启用"""
        if not self.use_global_config or not self.config_manager:
            return False  # 没有全局配置时模型不可用
        
        try:
            active_models = self.config_manager.get_active_models()
            model_info = active_models.get(model_type, {})
            return model_info.get('is_enabled', False) and bool(model_info.get('provider_id'))
        except Exception:
            return False

# 全局AI客户端实例
_global_client: Optional[AIClient] = None

def get_ai_client(api_key: Optional[str] = None, base_url: Optional[str] = None, 
                  use_global_config: bool = True) -> AIClient:
    """获取全局AI客户端实例
    
    注意：默认必须使用全局配置，如果全局配置不可用将抛出错误
    """
    global _global_client
    
    if _global_client is None:
        _global_client = AIClient(api_key, base_url, use_global_config)
    
    return _global_client

def reset_ai_client():
    """重置全局AI客户端实例"""
    global _global_client
    _global_client = None