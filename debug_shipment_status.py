#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试货运记录状态更新问题
"""

import sqlite3
import os

def debug_shipment_status_update():
    """调试货运记录状态更新问题"""
    
    # 连接货运记录数据库
    if not os.path.exists('db/shipment_records.db'):
        print("❌ 货运记录数据库不存在")
        return
        
    if not os.path.exists('db/task_queue.db'):
        print("❌ 任务队列数据库不存在")
        return
    
    print("🔍 检查货运记录和任务的关联状态...")
    
    # 连接两个数据库
    shipment_conn = sqlite3.connect('db/shipment_records.db')
    shipment_conn.row_factory = sqlite3.Row
    task_conn = sqlite3.connect('db/task_queue.db')
    task_conn.row_factory = sqlite3.Row
    
    shipment_cursor = shipment_conn.cursor()
    task_cursor = task_conn.cursor()
    
    # 查看最近的货运记录
    shipment_cursor.execute("""
        SELECT id, bill_of_lading, container_number, status, remarks, created_at, updated_at
        FROM shipment_records 
        ORDER BY created_at DESC
        LIMIT 5
    """)
    
    records = shipment_cursor.fetchall()
    print(f"最近的 {len(records)} 条货运记录:")
    
    for record in records:
        print(f"\n📋 记录ID: {record['id']}")
        print(f"   跟踪号: {record['bill_of_lading'] or record['container_number']}")
        print(f"   状态: {record['status']}")
        print(f"   备注: {record['remarks']}")
        print(f"   创建: {record['created_at']}")
        print(f"   更新: {record['updated_at']}")
        
        # 查找关联的任务
        tracking_number = record['bill_of_lading'] or record['container_number']
        
        print(f"   🔗 关联任务:")
        task_cursor.execute("""
            SELECT id, task_stage, status, created_at, remarks
            FROM task_queue 
            WHERE tracking_number = ?
            ORDER BY created_at DESC
            LIMIT 6
        """, (tracking_number,))
        
        tasks = task_cursor.fetchall()
        
        for i, task in enumerate(tasks, 1):
            task_id_short = task['id'][:8]
            print(f"      {i}. {task['task_stage']:15} {task['status']:12} {task['created_at']} ({task_id_short})")
            if task['remarks'] and '货运记录ID:' in task['remarks']:
                linked_record_id = task['remarks'].split('货运记录ID:')[1].split(',')[0].strip()
                if linked_record_id == str(record['id']):
                    print(f"         ✅ 正确关联到货运记录 {record['id']}")
                else:
                    print(f"         ❌ 关联到错误记录 {linked_record_id}")
            else:
                print(f"         ⚠️  任务未包含货运记录ID")
        
        # 检查最新AI分析任务的状态
        task_cursor.execute("""
            SELECT id, status, remarks, completed_at
            FROM task_queue 
            WHERE tracking_number = ? AND task_stage = 'ai_analysis'
            ORDER BY created_at DESC
            LIMIT 1
        """, (tracking_number,))
        
        latest_ai_task = task_cursor.fetchone()
        if latest_ai_task:
            print(f"   🤖 最新AI分析任务:")
            print(f"      状态: {latest_ai_task['status']}")
            print(f"      完成时间: {latest_ai_task['completed_at']}")
            
            # 检查状态匹配
            if latest_ai_task['status'] == 'completed' and record['status'] != '已完成':
                print(f"      ❌ 状态不匹配！AI任务已完成但货运记录状态是 '{record['status']}'")
            elif latest_ai_task['status'] == 'completed' and record['status'] == '已完成':
                print(f"      ✅ 状态匹配：都是已完成")
            else:
                print(f"      ℹ️  AI任务状态: {latest_ai_task['status']}, 货运记录状态: {record['status']}")
        else:
            print(f"   ⚠️  未找到AI分析任务")
    
    shipment_conn.close()
    task_conn.close()

if __name__ == "__main__":
    debug_shipment_status_update()