/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--color-border);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(0);
    border-color: #444;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.stat-title {
    font-size: 12px;
    color: var(--color-text-secondary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.stat-icon {
    width: 32px;
    height: 32px;
    background-color: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--color-text);
    margin-bottom: 4px;
}

.stat-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--color-text-secondary);
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

.stat-change i {
    width: 12px;
    height: 12px;
}

/* 任务卡片 */
.task-card {
    background: transparent;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 0;
    box-shadow: var(--shadow);
    border: 1px solid transparent;
    border-bottom: 1px solid var(--color-border);
    transition: var(--transition);
}

.task-card:hover {
    transform: translateY(0);
    background-color: rgba(255, 255, 255, 0.03);
    box-shadow: none;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.task-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 4px;
}

.task-meta {
    font-size: 12px;
    color: var(--color-text-secondary);
    display: flex;
    align-items: center;
    gap: 12px;
}

.task-meta i {
    width: 12px;
    height: 12px;
}

.task-actions {
    display: flex;
    gap: 8px;
}

.task-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
}

/* 状态徽章 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: capitalize;
    letter-spacing: 0.5px;
}

.status-badge i {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-pending {
    background-color: #4a3d24;
    color: #f7d88b;
}
.status-pending i { background-color: #f7d88b; }

.status-running {
    background-color: #2c3a5e;
    color: #a4c4f9;
}
.status-running i { background-color: #a4c4f9; }

.status-completed {
    background-color: #24463a;
    color: #92e2c2;
}
.status-completed i { background-color: #92e2c2; }

.status-failed {
    background-color: #5b2d2f;
    color: #f9a3a8;
}
.status-failed i { background-color: #f9a3a8; }


/* 进度条 */
.progress-bar {
    width: 100%;
    height: 6px;
    background-color: #333;
    border-radius: 3px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-fill {
    height: 100%;
    background-color: var(--color-primary);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 新建任务按钮 (Hero section) */
.create-task-hero {
    text-align: center;
    padding: 40px 20px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    color: var(--color-text);
    margin-bottom: 30px;
    border: 1px solid var(--color-border);
}

.create-task-hero h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
}

.create-task-hero p {
    font-size: 16px;
    color: var(--color-text-secondary);
    margin-bottom: 30px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.hero-btn {
    background: var(--color-primary);
    border: none;
    color: white;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.hero-btn:hover {
    background: #1d6fe6;
    transform: translateY(-2px);
}

/* 快速操作模板 */
.quick-templates {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.template-card {
    background: var(--bg-primary);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: 16px;
    cursor: pointer;
    transition: var(--transition);
}

.template-card:hover {
    border-color: var(--color-primary);
    background-color: #2a2a2a;
}

.template-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-title i {
    width: 16px;
    height: 16px;
    color: var(--color-primary);
}

.template-desc {
    font-size: 12px;
    color: var(--color-text-secondary);
    line-height: 1.4;
}

/* 数据表格 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--color-border);
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text-secondary);
}

.data-table th {
    background-color: #2a2a2a;
    font-weight: 600;
    color: var(--color-text);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tr:hover {
    background-color: #252525;
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* 筛选面板 */
.filter-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--color-border);
    margin-bottom: 20px;
}

.filter-group {
    margin-bottom: 16px;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.filter-tag {
    background-color: #333;
    color: var(--color-text-secondary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.filter-tag:hover {
    background-color: #444;
}

.filter-tag.active {
    background-color: var(--color-primary);
    color: white;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--color-text-secondary);
    background: var(--bg-secondary);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
}

.empty-state i {
    width: 64px;
    height: 64px;
    color: #444;
    margin-bottom: 16px;
}

.empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 8px;
}

.empty-state p {
    font-size: 14px;
    color: var(--color-text-secondary);
    margin-bottom: 20px;
}

/* 通知消息 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    pointer-events: none;
}

.notification {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    border-left: 4px solid;
    background: var(--bg-secondary);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.notification-show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification.notification-success {
    border-left-color: #10b981;
    background: #24463a;
}

.notification.notification-error {
    border-left-color: #ef4444;
    background: #5b2d2f;
}

.notification.notification-warning {
    border-left-color: #f59e0b;
    background: #4a3d24;
}

.notification.notification-info {
    border-left-color: var(--color-primary);
    background: #2c3a5e;
}

.notification-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.notification-header strong {
    flex: 1;
    font-size: 14px;
    font-weight: 600;
    color: var(--color-text);
}

.notification-close {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.notification-message {
    font-size: 13px;
    color: var(--color-text-secondary);
    margin-bottom: 4px;
}

.notification-time {
    font-size: 11px;
    color: #666;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    animation: fadeIn 0.2s ease;
}

.modal {
    background: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: slideIn 0.3s ease;
    border: 1px solid var(--color-border);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text);
}

.modal-body {
    padding: 20px 24px;
}

.modal-body p {
    margin: 0;
    color: var(--color-text-secondary);
    line-height: 1.5;
}

.modal-footer {
    padding: 16px 24px 20px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    border-top: 1px solid var(--color-border);
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(18, 18, 18, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1002;
}

.loading-spinner {
    text-align: center;
    color: var(--color-text);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #444;
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.loading-text {
    font-size: 14px;
    color: var(--color-text-secondary);
}

/* 表单静态控件 */
.form-control-static {
    padding: 10px 12px;
    background: var(--bg-primary);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    color: var(--color-text);
    font-size: 14px;
    min-height: 40px;
    display: flex;
    align-items: center;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}