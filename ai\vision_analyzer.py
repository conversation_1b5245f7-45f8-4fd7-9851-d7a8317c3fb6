#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视觉AI分析模块
处理截图分析和操作决策
"""

from datetime import datetime
from typing import List, Dict
from .client import get_ai_client
from db_logger import log_ai_call_simple
from .prompt_config import get_vision_system_prompt, get_vision_user_text

def get_ai_action(image_base64: str, task_prompt: str, history: List[str]) -> Dict:
    """
    调用豆包视觉大模型API，分析截图并返回下一步操作指令。

    Args:
        image_base64: 当前页面的Base64编码截图
        task_prompt: 当前需要完成的核心任务
        history: 之前的操作历史，为AI提供上下文
        
    Returns:
        包含操作指令的字典
    """
    print("🤖 AI 正在分析截图并决策...")
    
    # 将历史记录转换为字符串，方便AI理解
    history_str = "\n".join([f"- {h}" for h in history]) if history else "无"

    try:
        # 获取AI客户端
        ai_client = get_ai_client()
        
        # 记录AI调用开始时间
        request_time = datetime.now()
        
        response = ai_client.client.chat.completions.create(
            model=ai_client.get_vision_model(),
            messages=[
                {
                    "role": "system",
                    "content": get_vision_system_prompt()
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": get_vision_user_text(task_prompt, history_str)
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1000,
            temperature=0.1
        )
        
        # 记录AI调用结束时间
        response_time = datetime.now()
        
        # 提取AI的回复内容
        ai_response = response.choices[0].message.content.strip()
        print(f"🤖 AI 回复: {ai_response}")
        
        # 记录AI调用日志
        log_ai_call_simple(
            model=ai_client.get_vision_model(),
            request_time=request_time,
            response_time=response_time,
            prompt_tokens=response.usage.prompt_tokens if response.usage else 0,
            completion_tokens=response.usage.completion_tokens if response.usage else 0,
            total_tokens=response.usage.total_tokens if response.usage else 0,
            caller_id='vision_analyzer',
            content_type='vision',
            status='success'
        )
        
        # 尝试解析JSON回复
        try:
            import json
            result = json.loads(ai_response)
            return result
        except json.JSONDecodeError:
            print("[WARNING] AI回复不是有效的JSON格式，尝试提取关键信息...")
            # 如果JSON解析失败，返回一个默认的等待操作
            return {
                "action": "wait",
                "reasoning": f"AI回复解析失败: {ai_response}"
            }
            
    except Exception as e:
        print(f"[ERROR] AI调用失败: {str(e)}")
        
        # 记录失败的AI调用
        log_ai_call_simple(
            model=ai_client.get_vision_model(),
            request_time=request_time,
            response_time=datetime.now(),
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            caller_id='vision_analyzer',
            content_type='vision',
            status='error',
            error_message=str(e)
        )
        
        return {
            "action": "wait",
            "reasoning": f"AI调用失败: {str(e)}"
        }