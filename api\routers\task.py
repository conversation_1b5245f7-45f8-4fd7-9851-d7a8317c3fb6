#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理API路由
处理任务管理相关的HTTP请求
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from fastapi.responses import JSONResponse

from api.models.schemas import (
    TaskResponse,
    TaskListResponse,
    TaskCancelRequest,
    TaskRetryRequest,
    TaskLogsResponse,
    TaskStats,
    APIResponse,
    TaskStatus
)
from api.services.task_service import TaskService, get_task_service
from api.utils.logger import api_logger
from shipment_manager import ShipmentManager

router = APIRouter()

@router.get("/task/{task_id}", response_model=TaskResponse)
async def get_task_status(
    task_id: str = Path(..., description="任务ID"),
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务状态
    
    - **task_id**: 任务ID
    """
    try:
        api_logger.info(f"获取任务状态: {task_id}")
        
        task = await task_service.get_task_status(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        api_logger.info(f"任务状态获取成功: {task_id} - {task.status}")
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取任务状态异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.get("/tasks", response_model=TaskListResponse)
async def get_task_list(
    task_service: TaskService = Depends(get_task_service),
    status: Optional[TaskStatus] = Query(None, description="任务状态过滤"),
    container_number: Optional[str] = Query(None, description="集装箱号过滤"),
    carrier_code: Optional[str] = Query(None, description="船公司代码过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小")
):
    """
    获取任务列表
    
    - **status**: 任务状态过滤（可选）
    - **container_number**: 集装箱号过滤（可选）
    - **carrier_code**: 船公司代码过滤（可选）
    - **page**: 页码（默认1）
    - **page_size**: 每页大小（默认20，最大100）
    """
    try:
        api_logger.info(f"获取任务列表: page={page}, page_size={page_size}")
        
        filters = {}
        if status:
            filters['status'] = status.value
        if container_number:
            filters['container_number'] = container_number
        if carrier_code:
            filters['carrier_code'] = carrier_code
        
        task_list = await task_service.get_task_list(
            filters=filters,
            page=page,
            page_size=page_size
        )
        
        api_logger.info(f"任务列表获取成功: {len(task_list.tasks)} 个任务")
        return task_list
        
    except Exception as e:
        api_logger.error(f"获取任务列表异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.delete("/task/{task_id}", response_model=APIResponse)
async def cancel_task(
    task_id: str = Path(..., description="任务ID"),
    request: TaskCancelRequest = None,
    task_service: TaskService = Depends(get_task_service)
):
    """
    取消任务
    
    - **task_id**: 任务ID
    - **reason**: 取消原因（可选）
    """
    try:
        api_logger.info(f"取消任务: {task_id}")
        
        reason = request.reason if request else None
        success = await task_service.cancel_task(task_id, reason)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        api_logger.info(f"任务取消成功: {task_id}")
        return APIResponse(
            success=True,
            message="任务已取消",
            data={"task_id": task_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"取消任务异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.post("/task/{task_id}/retry", response_model=TaskResponse)
async def retry_task(
    task_id: str = Path(..., description="任务ID"),
    request: TaskRetryRequest = None,
    task_service: TaskService = Depends(get_task_service)
):
    """
    重试任务
    
    - **task_id**: 任务ID
    - **reason**: 重试原因（可选）
    - **reset_attempts**: 是否重置重试次数（可选）
    """
    try:
        api_logger.info(f"重试任务: {task_id}")
        
        reason = request.reason if request else None
        reset_attempts = request.reset_attempts if request else False
        
        task = await task_service.retry_task(task_id, reason, reset_attempts)
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在或无法重试")
        
        api_logger.info(f"任务重试成功: {task_id}")
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"重试任务异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.get("/task/{task_id}/logs", response_model=TaskLogsResponse)
async def get_task_logs(
    task_id: str = Path(..., description="任务ID"),
    task_service: TaskService = Depends(get_task_service),
    limit: int = Query(100, ge=1, le=1000, description="日志条数限制")
):
    """
    获取任务日志
    
    - **task_id**: 任务ID
    - **limit**: 日志条数限制（默认100，最大1000）
    """
    try:
        api_logger.info(f"获取任务日志: {task_id}")
        
        logs = await task_service.get_task_logs(task_id, limit)
        
        if not logs:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        api_logger.info(f"任务日志获取成功: {task_id} - {len(logs.logs)} 条日志")
        return logs
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取任务日志异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.get("/task/{task_id}/shipment-details")
async def get_task_shipment_details(
    task_id: str = Path(..., description="任务ID"),
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务对应的货运记录详情（包含物流节点）

    - **task_id**: 任务ID
    """
    try:
        api_logger.info(f"获取任务货运记录详情: {task_id}")

        # 首先获取任务信息
        task = await task_service.get_task_status(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 通过任务的tracking_number查找对应的货运记录
        shipment_manager = ShipmentManager()

        # 搜索货运记录（通过提单号或箱号）
        tracking_number = task.container_number
        api_logger.info(f"搜索货运记录，tracking_number: {tracking_number}")

        # 先尝试精确匹配
        shipments = shipment_manager.search_shipment_records(
            bill_of_lading=tracking_number
        )

        # 如果没找到，再尝试按箱号搜索
        if not shipments:
            shipments = shipment_manager.search_shipment_records(
                container_number=tracking_number
            )

        # 如果还没找到，尝试模糊搜索
        if not shipments:
            all_shipments = shipment_manager.search_shipment_records()
            shipments = [s for s in all_shipments if tracking_number in (s.get('bill_of_lading', '') or s.get('container_number', ''))]

        api_logger.info(f"找到 {len(shipments)} 条匹配的货运记录")

        if not shipments:
            # 如果没有找到货运记录，返回空的物流节点
            return {
                "task_id": task_id,
                "tracking_number": tracking_number,
                "shipment_found": False,
                "tracking_points": []
            }

        # 获取第一个匹配的货运记录的详情
        shipment = shipments[0]
        details = shipment_manager.get_shipment_details(shipment['id'])

        if not details:
            return {
                "task_id": task_id,
                "tracking_number": tracking_number,
                "shipment_found": True,
                "tracking_points": []
            }

        # 转换物流节点数据格式
        tracking_points = []
        for date_info in details.get('dates', []):
            tracking_points.append({
                "date": date_info.get('date'),
                "time": date_info.get('date'),
                "timestamp": date_info.get('created_at'),
                "status": date_info.get('status', ''),
                "event": date_info.get('description', ''),
                "event_type": date_info.get('type', ''),
                "location": date_info.get('location', ''),
                "description": date_info.get('description', ''),
                "detail": date_info.get('description', ''),
                "vessel_info": date_info.get('vessel_info', '')
            })

        api_logger.info(f"任务货运记录详情获取成功: {task_id} - {len(tracking_points)} 个物流节点")

        return {
            "task_id": task_id,
            "tracking_number": tracking_number,
            "shipment_found": True,
            "shipment_id": shipment['id'],
            "tracking_points": tracking_points,
            "estimated_arrival_time": details['basic_info'].get('estimated_arrival_time'),
            "carrier_company": details['basic_info'].get('carrier_company'),
            "status": details['basic_info'].get('status')
        }

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取任务货运记录详情异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.get("/tasks/stats", response_model=TaskStats)
async def get_task_stats(
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务统计信息
    
    返回任务的各种统计数据，包括总数、各状态数量、成功率等
    """
    try:
        api_logger.info("获取任务统计信息")
        
        stats = await task_service.get_task_stats()
        
        api_logger.info(f"任务统计信息获取成功: 总任务数 {stats.total}")
        return stats
        
    except Exception as e:
        api_logger.error(f"获取任务统计信息异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")