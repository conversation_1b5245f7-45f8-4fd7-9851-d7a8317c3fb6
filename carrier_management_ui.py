#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
船公司数据管理界面
提供船公司信息的增删改查功能
"""

import sys
import os
from typing import List, Dict, Optional
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit, QLabel,
    QDialog, QFormLayout, QTextEdit, QMessageBox, QHeaderView,
    QSplitter, QGroupBox, QComboBox, QCheckBox, QTabWidget
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QIcon

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.carrier_database import CarrierDatabase
from db.migrate_carrier_data import migrate_data, verify_migration

class CarrierEditDialog(QDialog):
    """船公司编辑对话框"""
    
    def __init__(self, parent=None, carrier_data=None):
        super().__init__(parent)
        self.carrier_data = carrier_data
        self.is_edit_mode = carrier_data is not None
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_carrier_data()
    
    def setup_ui(self):
        """设置界面"""
        title = "编辑船公司" if self.is_edit_mode else "添加船公司"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 创建表单
        form_layout = QFormLayout()
        
        # 基本信息
        self.company_name_edit = QLineEdit()
        self.company_code_edit = QLineEdit()
        self.international_site_edit = QLineEdit()
        self.chinese_site_edit = QLineEdit()
        self.tracking_site_edit = QLineEdit()
        self.input_element_id_edit = QLineEdit()
        self.search_button_id_edit = QLineEdit()
        
        form_layout.addRow("公司名称:", self.company_name_edit)
        form_layout.addRow("公司代码:", self.company_code_edit)
        form_layout.addRow("国际站:", self.international_site_edit)
        form_layout.addRow("中文站:", self.chinese_site_edit)
        form_layout.addRow("追踪页面:", self.tracking_site_edit)
        form_layout.addRow("输入框ID:", self.input_element_id_edit)
        form_layout.addRow("搜索按钮ID:", self.search_button_id_edit)
        
        layout.addLayout(form_layout)
        
        # SCAC前缀
        scac_group = QGroupBox("SCAC前缀")
        scac_layout = QVBoxLayout(scac_group)
        
        self.scac_edit = QTextEdit()
        self.scac_edit.setMaximumHeight(80)
        self.scac_edit.setPlaceholderText("每行一个SCAC前缀，例如:\nMEDU\nMSCU")
        scac_layout.addWidget(self.scac_edit)
        
        layout.addWidget(scac_group)
        
        # 提单号规则
        pattern_group = QGroupBox("提单号规则")
        pattern_layout = QVBoxLayout(pattern_group)
        
        self.pattern_edit = QTextEdit()
        self.pattern_edit.setMaximumHeight(80)
        self.pattern_edit.setPlaceholderText("每行一个正则表达式，例如:\nr\"^(177|MEDU)\\w*\"\nr\"^MSC\\w+\"")
        pattern_layout.addWidget(self.pattern_edit)
        
        layout.addWidget(pattern_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存")
        self.cancel_button = QPushButton("取消")
        
        self.save_button.clicked.connect(self.save_carrier)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def load_carrier_data(self):
        """加载船公司数据"""
        if not self.carrier_data:
            return
        
        self.company_name_edit.setText(self.carrier_data.get('company_name', ''))
        self.company_code_edit.setText(self.carrier_data.get('company_code', ''))
        self.international_site_edit.setText(self.carrier_data.get('international_site', ''))
        self.chinese_site_edit.setText(self.carrier_data.get('chinese_site', ''))
        self.tracking_site_edit.setText(self.carrier_data.get('tracking_site', ''))
        self.input_element_id_edit.setText(self.carrier_data.get('input_element_id', ''))
        self.search_button_id_edit.setText(self.carrier_data.get('search_button_id', ''))
        
        # 加载SCAC前缀
        scac_prefixes = self.carrier_data.get('scac_prefixes', [])
        self.scac_edit.setPlainText('\n'.join(scac_prefixes))
        
        # 加载提单号规则
        bl_patterns = self.carrier_data.get('bl_patterns', [])
        self.pattern_edit.setPlainText('\n'.join(bl_patterns))
    
    def save_carrier(self):
        """保存船公司数据"""
        # 验证必填字段
        if not self.company_name_edit.text().strip():
            QMessageBox.warning(self, "警告", "公司名称不能为空")
            return
        
        if not self.tracking_site_edit.text().strip():
            QMessageBox.warning(self, "警告", "追踪页面不能为空")
            return
        
        # 收集数据
        self.result_data = {
            'company_name': self.company_name_edit.text().strip(),
            'company_code': self.company_code_edit.text().strip(),
            'international_site': self.international_site_edit.text().strip(),
            'chinese_site': self.chinese_site_edit.text().strip(),
            'tracking_site': self.tracking_site_edit.text().strip(),
            'input_element_id': self.input_element_id_edit.text().strip(),
            'search_button_id': self.search_button_id_edit.text().strip(),
            'scac_prefixes': [line.strip() for line in self.scac_edit.toPlainText().split('\n') if line.strip()],
            'bl_patterns': [line.strip() for line in self.pattern_edit.toPlainText().split('\n') if line.strip()]
        }
        
        self.accept()

class CarrierManagementWindow(QMainWindow):
    """船公司管理主窗口"""
    
    def __init__(self):
        super().__init__()
        self.db = CarrierDatabase()
        self.setup_ui()
        self.load_carriers()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("船公司数据管理系统")
        
        # 获取屏幕尺寸并设置窗体为75%大小
        screen = QApplication.primaryScreen().geometry()
        width = int(screen.width() * 0.75)
        height = int(screen.height() * 0.75)
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索船公司名称或代码...")
        self.search_edit.textChanged.connect(self.search_carriers)
        
        # 按钮
        self.add_button = QPushButton("添加船公司")
        self.edit_button = QPushButton("编辑")
        self.delete_button = QPushButton("删除")
        self.refresh_button = QPushButton("刷新")
        self.restore_button = QPushButton("恢复原始数据")
        
        self.add_button.clicked.connect(self.add_carrier)
        self.edit_button.clicked.connect(self.edit_carrier)
        self.delete_button.clicked.connect(self.delete_carrier)
        self.refresh_button.clicked.connect(self.load_carriers)
        self.restore_button.clicked.connect(self.restore_original_data)
        
        toolbar_layout.addWidget(QLabel("搜索:"))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addWidget(self.restore_button)
        
        main_layout.addLayout(toolbar_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 船公司列表表格
        self.carrier_table = QTableWidget()
        self.setup_carrier_table()
        splitter.addWidget(self.carrier_table)
        
        # 详细信息面板
        detail_widget = self.create_detail_panel()
        splitter.addWidget(detail_widget)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
        main_layout.addWidget(splitter)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def setup_carrier_table(self):
        """设置船公司表格"""
        headers = ["ID", "公司名称", "公司代码", "追踪页面", "输入框ID", "搜索按钮ID", "SCAC前缀数", "规则数"]
        self.carrier_table.setColumnCount(len(headers))
        self.carrier_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.carrier_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.carrier_table.setAlternatingRowColors(True)
        self.carrier_table.setSortingEnabled(True)  # 启用排序功能
        
        # 调整列宽
        header = self.carrier_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 公司名称
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 公司代码
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 追踪页面
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 输入框ID
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 搜索按钮ID
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # SCAC前缀数
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 规则数
        
        # 连接选择事件
        self.carrier_table.itemSelectionChanged.connect(self.on_carrier_selected)
    
    def create_detail_panel(self):
        """创建详细信息面板"""
        detail_widget = QWidget()
        detail_widget.setFixedWidth(400)  # 设置固定宽度
        layout = QVBoxLayout(detail_widget)
        
        # 标题
        title_label = QLabel("船公司详细信息")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        # 创建标签页
        self.detail_tabs = QTabWidget()
        
        # 基本信息标签页
        basic_info_widget = QWidget()
        basic_layout = QFormLayout(basic_info_widget)
        
        # 设置表单布局的间距
        basic_layout.setVerticalSpacing(8)  # 适中的垂直间距
        basic_layout.setHorizontalSpacing(10)
        
        # 创建标签并设置文本换行
        self.detail_company_name = QLabel()
        self.detail_company_name.setWordWrap(True)
        self.detail_company_name.setStyleSheet("margin-bottom: 8px;")
        
        self.detail_company_code = QLabel()
        self.detail_company_code.setWordWrap(True)
        self.detail_company_code.setStyleSheet("margin-bottom: 8px;")
        
        self.detail_international_site = QLabel()
        self.detail_international_site.setWordWrap(True)
        self.detail_international_site.setOpenExternalLinks(True)
        self.detail_international_site.setMinimumHeight(20)  # 基础最小高度
        self.detail_international_site.setStyleSheet("margin-bottom: 8px;")
        
        self.detail_chinese_site = QLabel()
        self.detail_chinese_site.setWordWrap(True)
        self.detail_chinese_site.setOpenExternalLinks(True)
        self.detail_chinese_site.setMinimumHeight(20)  # 基础最小高度
        self.detail_chinese_site.setStyleSheet("margin-bottom: 8px;")
        
        self.detail_tracking_site = QLabel()
        self.detail_tracking_site.setWordWrap(True)
        self.detail_tracking_site.setOpenExternalLinks(True)
        self.detail_tracking_site.setMinimumHeight(20)  # 基础最小高度
        self.detail_tracking_site.setStyleSheet("margin-bottom: 8px;")
        
        self.detail_input_element_id = QLabel()
        self.detail_input_element_id.setWordWrap(True)
        self.detail_input_element_id.setStyleSheet("margin-bottom: 8px;")
        
        self.detail_search_button_id = QLabel()
        self.detail_search_button_id.setWordWrap(True)
        self.detail_search_button_id.setStyleSheet("margin-bottom: 8px;")
        
        basic_layout.addRow("公司名称:", self.detail_company_name)
        basic_layout.addRow("公司代码:", self.detail_company_code)
        basic_layout.addRow("国际站:", self.detail_international_site)
        basic_layout.addRow("中文站:", self.detail_chinese_site)
        basic_layout.addRow("追踪页面:", self.detail_tracking_site)
        basic_layout.addRow("输入框ID:", self.detail_input_element_id)
        basic_layout.addRow("搜索按钮ID:", self.detail_search_button_id)
        
        self.detail_tabs.addTab(basic_info_widget, "基本信息")
        
        # SCAC前缀标签页
        scac_widget = QWidget()
        scac_layout = QVBoxLayout(scac_widget)
        self.detail_scac_list = QTextEdit()
        self.detail_scac_list.setReadOnly(True)
        scac_layout.addWidget(self.detail_scac_list)
        self.detail_tabs.addTab(scac_widget, "SCAC前缀")
        
        # 提单号规则标签页
        pattern_widget = QWidget()
        pattern_layout = QVBoxLayout(pattern_widget)
        self.detail_pattern_list = QTextEdit()
        self.detail_pattern_list.setReadOnly(True)
        pattern_layout.addWidget(self.detail_pattern_list)
        self.detail_tabs.addTab(pattern_widget, "提单号规则")
        
        layout.addWidget(self.detail_tabs)
        layout.addStretch()
        
        return detail_widget
    
    def load_carriers(self):
        """加载船公司列表"""
        try:
            carriers = self.db.get_all_carriers()
            self.display_carriers(carriers)
            self.statusBar().showMessage(f"已加载 {len(carriers)} 个船公司")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载船公司数据失败: {str(e)}")
    
    def display_carriers(self, carriers: List[Dict]):
        """显示船公司列表"""
        # 临时禁用排序，避免数据填充时的冲突
        self.carrier_table.setSortingEnabled(False)
        self.carrier_table.setRowCount(len(carriers))
        
        for row, carrier in enumerate(carriers):
            # ID列 - 设置为数字类型以便正确排序
            id_item = QTableWidgetItem()
            id_item.setData(Qt.DisplayRole, carrier['id'])
            self.carrier_table.setItem(row, 0, id_item)
            
            self.carrier_table.setItem(row, 1, QTableWidgetItem(carrier['company_name']))
            self.carrier_table.setItem(row, 2, QTableWidgetItem(carrier['company_code']))
            self.carrier_table.setItem(row, 3, QTableWidgetItem(carrier['tracking_site']))
            self.carrier_table.setItem(row, 4, QTableWidgetItem(carrier.get('input_element_id', '')))
            self.carrier_table.setItem(row, 5, QTableWidgetItem(carrier.get('search_button_id', '')))
            
            # SCAC前缀数和规则数也设置为数字类型
            scac_count_item = QTableWidgetItem()
            scac_count_item.setData(Qt.DisplayRole, len(carrier['scac_prefixes']))
            self.carrier_table.setItem(row, 6, scac_count_item)
            
            pattern_count_item = QTableWidgetItem()
            pattern_count_item.setData(Qt.DisplayRole, len(carrier['bl_patterns']))
            self.carrier_table.setItem(row, 7, pattern_count_item)
            
            # 存储完整数据
            self.carrier_table.item(row, 0).setData(Qt.UserRole, carrier)
        
        # 重新启用排序功能
        self.carrier_table.setSortingEnabled(True)
    
    def search_carriers(self):
        """搜索船公司"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_carriers()
            return
        
        try:
            carriers = self.db.search_carriers(keyword)
            self.display_carriers(carriers)
            self.statusBar().showMessage(f"搜索到 {len(carriers)} 个船公司")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"搜索失败: {str(e)}")
    
    def on_carrier_selected(self):
        """船公司选择事件"""
        current_row = self.carrier_table.currentRow()
        if current_row >= 0:
            carrier_item = self.carrier_table.item(current_row, 0)
            if carrier_item:
                carrier_data = carrier_item.data(Qt.UserRole)
                self.display_carrier_details(carrier_data)
    
    def display_carrier_details(self, carrier_data: Dict):
        """显示船公司详细信息"""
        if not carrier_data:
            return
        
        # 基本信息
        self.detail_company_name.setText(carrier_data.get('company_name', ''))
        self.detail_company_code.setText(carrier_data.get('company_code', ''))
        
        # 网址字段设置为可点击的链接，并动态调整高度
        international_site = carrier_data.get('international_site', '')
        if international_site:
            self.detail_international_site.setText(f'<a href="{international_site}">{international_site}</a>')
            # 根据URL长度动态调整高度
            height = max(20, min(60, len(international_site) // 50 * 20 + 20))
            self.detail_international_site.setMinimumHeight(height)
        else:
            self.detail_international_site.setText('')
            self.detail_international_site.setMinimumHeight(20)
            
        chinese_site = carrier_data.get('chinese_site', '')
        if chinese_site:
            self.detail_chinese_site.setText(f'<a href="{chinese_site}">{chinese_site}</a>')
            # 根据URL长度动态调整高度
            height = max(20, min(60, len(chinese_site) // 50 * 20 + 20))
            self.detail_chinese_site.setMinimumHeight(height)
        else:
            self.detail_chinese_site.setText('')
            self.detail_chinese_site.setMinimumHeight(20)
            
        tracking_site = carrier_data.get('tracking_site', '')
        if tracking_site:
            self.detail_tracking_site.setText(f'<a href="{tracking_site}">{tracking_site}</a>')
            # 根据URL长度动态调整高度
            height = max(20, min(60, len(tracking_site) // 50 * 20 + 20))
            self.detail_tracking_site.setMinimumHeight(height)
        else:
            self.detail_tracking_site.setText('')
            self.detail_tracking_site.setMinimumHeight(20)
            
        self.detail_input_element_id.setText(carrier_data.get('input_element_id', ''))
        self.detail_search_button_id.setText(carrier_data.get('search_button_id', ''))
        
        # SCAC前缀
        scac_prefixes = carrier_data.get('scac_prefixes', [])
        self.detail_scac_list.setPlainText('\n'.join(scac_prefixes))
        
        # 提单号规则
        bl_patterns = carrier_data.get('bl_patterns', [])
        self.detail_pattern_list.setPlainText('\n'.join(bl_patterns))
    
    def add_carrier(self):
        """添加船公司"""
        dialog = CarrierEditDialog(self)
        if dialog.exec() == QDialog.Accepted:
            try:
                data = dialog.result_data
                carrier_id = self.db.add_carrier(
                    company_name=data['company_name'],
                    company_code=data['company_code'],
                    international_site=data['international_site'],
                    chinese_site=data['chinese_site'],
                    tracking_site=data['tracking_site'],
                    input_element_id=data['input_element_id'],
                    search_button_id=data['search_button_id'],
                    scac_prefixes=data['scac_prefixes'],
                    bl_patterns=data['bl_patterns']
                )
                QMessageBox.information(self, "成功", f"船公司添加成功，ID: {carrier_id}")
                self.load_carriers()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加船公司失败: {str(e)}")
    
    def edit_carrier(self):
        """编辑船公司"""
        current_row = self.carrier_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要编辑的船公司")
            return
        
        carrier_item = self.carrier_table.item(current_row, 0)
        carrier_data = carrier_item.data(Qt.UserRole)
        
        dialog = CarrierEditDialog(self, carrier_data)
        if dialog.exec() == QDialog.Accepted:
            try:
                data = dialog.result_data
                self.db.update_carrier(
                    carrier_id=carrier_data['id'],
                    company_name=data['company_name'],
                    company_code=data['company_code'],
                    international_site=data['international_site'],
                    chinese_site=data['chinese_site'],
                    tracking_site=data['tracking_site'],
                    input_element_id=data['input_element_id'],
                    search_button_id=data['search_button_id'],
                    scac_prefixes=data['scac_prefixes'],
                    bl_patterns=data['bl_patterns']
                )
                QMessageBox.information(self, "成功", "船公司更新成功")
                self.load_carriers()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"更新船公司失败: {str(e)}")
    
    def delete_carrier(self):
        """删除船公司"""
        current_row = self.carrier_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的船公司")
            return
        
        carrier_item = self.carrier_table.item(current_row, 0)
        carrier_data = carrier_item.data(Qt.UserRole)
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除船公司 '{carrier_data['company_name']}' 吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db.delete_carrier(carrier_data['id'])
                QMessageBox.information(self, "成功", "船公司删除成功")
                self.load_carriers()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除船公司失败: {str(e)}")
    
    def restore_original_data(self):
        """恢复原始数据"""
        reply = QMessageBox.question(
            self, "确认恢复", 
            "确定要恢复原始船公司数据吗？\n这将清空当前所有数据并重新导入原始配置。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 执行数据迁移
                migrate_data()
                
                # 验证迁移结果
                verify_migration()
                
                QMessageBox.information(self, "成功", "原始数据恢复成功")
                self.load_carriers()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"恢复原始数据失败: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("船公司数据管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = CarrierManagementWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()