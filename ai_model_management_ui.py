#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模型管理界面
提供AI服务提供商和模型配置的可视化管理
"""

import sys
from typing import Dict, List, Optional, Any

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QFrame, QGroupBox, QComboBox, QTextEdit,
    QMessageBox, QScrollArea, QGridLayout, QSpacerItem,
    QSizePolicy, QDialog, QDialogButtonBox, QFormLayout, QCheckBox,
    QSplitter, QTabWidget, QListWidget, QListWidgetItem, QMenu
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import <PERSON>Font, QPalette, QColor, QIcon, QAction

from utils.ai_config_manager import AIConfigManager


class ModernCard(QFrame):
    """现代化卡片组件"""

    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            ModernCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin: 8px;
            }
            ModernCard:hover {
                border: 1px solid #2196F3;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        if title:
            title_label = QLabel(title)
            title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
            title_label.setStyleSheet("color: #333333; margin-bottom: 8px;")
            layout.addWidget(title_label)





class ModelConfigDialog(QDialog):
    """模型配置对话框"""

    def __init__(self, model_type: str, providers: List[Dict[str, Any]],
                 current_config: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.model_type = model_type
        self.providers = providers
        self.current_config = current_config

        self.setWindowTitle(f"配置{model_type}模型")
        self.setModal(True)
        self.setFixedSize(350, 150)  # 固定窗口大小，避免大片空白

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 说明文字
        info_label = QLabel(f"请选择{self.model_type}模型的服务提供商：")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 提供商选择
        self.provider_combo = QComboBox()
        self.provider_combo.addItem("-- 禁用模型 --", None)
        self.provider_combo.setMinimumHeight(30)
        layout.addWidget(self.provider_combo)

        # 添加弹性空间，将按钮推到底部
        layout.addStretch()

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_data(self):
        """加载数据"""
        current_provider_id = self.current_config.get('provider_id')

        for provider in self.providers:
            # 根据模型类型获取对应的模型名称
            model_field_map = {
                'text': 'text_model',
                'image': 'image_model',
                'audio': 'audio_model',
                'video': 'video_model'
            }
            model_field = model_field_map.get(self.model_type, 'text_model')
            model_name = provider.get(model_field, '未设置')

            display_text = f"{provider['name']} ({model_name})"
            self.provider_combo.addItem(display_text, provider['id'])

            # 设置当前选中项
            if provider['id'] == current_provider_id:
                self.provider_combo.setCurrentIndex(self.provider_combo.count() - 1)

    def get_selected_provider_id(self) -> Optional[int]:
        """获取选中的提供商ID"""
        return self.provider_combo.currentData()


class ModelDialog(QDialog):
    """AI模型编辑对话框"""

    def __init__(self, model_data: Optional[Dict[str, Any]] = None, parent=None):
        super().__init__(parent)
        self.model_data = model_data
        self.setWindowTitle("编辑AI模型" if model_data else "添加AI模型")
        self.setModal(True)
        self.resize(500, 500)

        self.setup_ui()
        if model_data:
            self.load_data()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(16)

        # 表单
        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # 基本信息
        self.provider_name_edit = QLineEdit()
        self.provider_name_edit.setPlaceholderText("例如：豆包")
        form_layout.addRow("服务商名称*:", self.provider_name_edit)

        self.model_name_edit = QLineEdit()
        self.model_name_edit.setPlaceholderText("例如：doubao-seed-1-6-flash-250615")
        form_layout.addRow("模型名称*:", self.model_name_edit)

        # 模型类型
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItems(["文字", "图像", "音频", "视频"])
        form_layout.addRow("模型类型*:", self.model_type_combo)

        self.api_key_edit = QLineEdit()
        self.api_key_edit.setPlaceholderText("API密钥")
        form_layout.addRow("API Key*:", self.api_key_edit)

        self.base_url_edit = QLineEdit()
        self.base_url_edit.setPlaceholderText("例如：https://ark.cn-beijing.volces.com/api/v3")
        form_layout.addRow("Base URL*:", self.base_url_edit)

        # 备注
        self.remark_edit = QTextEdit()
        self.remark_edit.setMaximumHeight(80)
        self.remark_edit.setPlaceholderText("备注信息（可选）")
        form_layout.addRow("备注:", self.remark_edit)

        # 状态
        self.is_active = QCheckBox("启用该模型")
        self.is_active.setChecked(True)
        form_layout.addRow(self.is_active)

        layout.addLayout(form_layout)

        # 测试连接按钮
        test_button = QPushButton("测试连接")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #FFA726;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FF9800;
            }
        """)
        test_button.clicked.connect(self.test_connection)
        layout.addWidget(test_button)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 样式
        self.setStyleSheet("""
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border: 2px solid #2196F3;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ddd;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
        """)

    def load_data(self):
        """加载数据"""
        if not self.model_data:
            return

        self.provider_name_edit.setText(self.model_data.get('provider_name', ''))
        self.model_name_edit.setText(self.model_data.get('model_name', ''))

        # 设置模型类型
        model_type = self.model_data.get('model_type', '文字')
        type_map = {'text': '文字', 'image': '图像', 'audio': '音频', 'video': '视频'}
        display_type = type_map.get(model_type, model_type)
        index = self.model_type_combo.findText(display_type)
        if index >= 0:
            self.model_type_combo.setCurrentIndex(index)

        self.api_key_edit.setText(self.model_data.get('api_key', ''))
        self.base_url_edit.setText(self.model_data.get('base_url', ''))
        self.remark_edit.setPlainText(self.model_data.get('remark', ''))
        self.is_active.setChecked(bool(self.model_data.get('is_active', True)))

    def get_data(self) -> Dict[str, Any]:
        """获取表单数据"""
        # 模型类型映射
        type_map = {'文字': 'text', '图像': 'image', '音频': 'audio', '视频': 'video'}
        model_type = type_map.get(self.model_type_combo.currentText(), 'text')

        return {
            'provider_name': self.provider_name_edit.text().strip(),
            'model_name': self.model_name_edit.text().strip(),
            'model_type': model_type,
            'api_key': self.api_key_edit.text().strip(),
            'base_url': self.base_url_edit.text().strip(),
            'remark': self.remark_edit.toPlainText().strip(),
            'is_active': self.is_active.isChecked()
        }

    def test_connection(self):
        """测试连接"""
        data = self.get_data()

        if not data['provider_name']:
            QMessageBox.warning(self, "错误", "请输入服务商名称")
            return

        if not data['model_name']:
            QMessageBox.warning(self, "错误", "请输入模型名称")
            return

        if not data['api_key']:
            QMessageBox.warning(self, "错误", "请输入API Key")
            return

        if not data['base_url']:
            QMessageBox.warning(self, "错误", "请输入Base URL")
            return

        # 实际的连接测试逻辑
        try:
            # 导入AI客户端
            from ai.client import AIClient

            # 创建临时客户端进行测试
            test_client = AIClient(api_key=data['api_key'], base_url=data['base_url'])

            # 发送一个简单的测试请求
            response = test_client.client.chat.completions.create(
                model=data['model_name'],
                messages=[
                    {"role": "user", "content": "测试连接"}
                ],
                max_tokens=10,
                timeout=10  # 10秒超时
            )

            if response and response.choices:
                QMessageBox.information(self, "测试结果", "连接测试成功！API响应正常。")
            else:
                QMessageBox.warning(self, "测试结果", "连接测试失败：API响应异常。")

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower():
                QMessageBox.critical(self, "测试结果", "连接测试失败：请求超时，请检查网络连接和Base URL。")
            elif "unauthorized" in error_msg.lower() or "401" in error_msg:
                QMessageBox.critical(self, "测试结果", "连接测试失败：API Key无效，请检查密钥是否正确。")
            elif "not found" in error_msg.lower() or "404" in error_msg:
                QMessageBox.critical(self, "测试结果", "连接测试失败：模型不存在，请检查模型名称是否正确。")
            else:
                QMessageBox.critical(self, "测试结果", f"连接测试失败：{error_msg}")

    def accept(self):
        """验证并接受"""
        data = self.get_data()

        if not data['provider_name']:
            QMessageBox.warning(self, "错误", "请输入服务商名称")
            return

        if not data['model_name']:
            QMessageBox.warning(self, "错误", "请输入模型名称")
            return

        if not data['api_key']:
            QMessageBox.warning(self, "错误", "请输入API Key")
            return

        if not data['base_url']:
            QMessageBox.warning(self, "错误", "请输入Base URL")
            return

        super().accept()


class AIModelManagementUI(QMainWindow):
    """AI模型管理主界面"""

    def __init__(self):
        super().__init__()
        self.config_manager = AIConfigManager()
        self.setWindowTitle("AI模型管理")

        # 获取屏幕尺寸
        screen = QApplication.primaryScreen().geometry()
        screen_width = screen.width()
        screen_height = screen.height()

        # 设置窗体大小为屏幕的75%高度，保持合适的宽度
        window_width = min(1200, int(screen_width * 0.8))
        window_height = int(screen_height * 0.75)
        self.resize(window_width, window_height)

        # 设置最小尺寸
        self.setMinimumSize(800, 500)

        # 居中显示窗体
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.move(x, y)

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)



        # 主要内容区域 - 上下布局
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)

        # 上部分：全局启用模型管理
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 10)
        top_layout.setSpacing(8)

        models_title = QLabel("全局启用")
        models_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        models_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 4px 0;
                border-bottom: 2px solid #3498db;
            }
        """)
        top_layout.addWidget(models_title)

        # 全局启用模型表格
        self.models_table = QTableWidget()
        self.models_table.setColumnCount(3)
        self.models_table.setHorizontalHeaderLabels(["模型类型", "状态", "当前配置"])

        # 设置表格样式 - 更紧凑
        self.models_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
                gridline-color: #f0f0f0;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 6px 4px;
                border-bottom: 1px solid #f0f0f0;
                text-align: center;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 6px 4px;
                border: none;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
            }
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }
        """)

        # 设置表格属性
        self.models_table.verticalHeader().setVisible(False)
        self.models_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.models_table.setAlternatingRowColors(True)
        self.models_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 禁用编辑
        # 移除双击事件，只允许右键操作
        # self.models_table.cellDoubleClicked.connect(self.on_model_cell_double_clicked)

        # 设置右键菜单
        self.models_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.models_table.customContextMenuRequested.connect(self.show_models_context_menu)

        # 设置列宽
        header = self.models_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)            # 模型类型
        header.setSectionResizeMode(1, QHeaderView.Fixed)            # 状态
        header.setSectionResizeMode(2, QHeaderView.Stretch)          # 当前配置（自适应）

        # 设置固定宽度列
        self.models_table.setColumnWidth(0, 120)  # 模型类型列
        self.models_table.setColumnWidth(1, 80)   # 状态列

        # 设置固定高度以显示4行数据
        row_height = 35
        header_height = 30
        self.models_table.setFixedHeight(4 * row_height + header_height + 10)

        top_layout.addWidget(self.models_table)

        splitter.addWidget(top_widget)

        # 下部分：AI候选池管理
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        bottom_layout.setContentsMargins(0, 10, 0, 0)
        bottom_layout.setSpacing(8)

        # 服务提供商标题和按钮
        providers_header = QHBoxLayout()
        providers_header.setSpacing(10)

        providers_title = QLabel("AI候选池")
        providers_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        providers_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 4px 0;
                border-bottom: 2px solid #e74c3c;
            }
        """)
        providers_header.addWidget(providers_title)

        # 提示词编辑按钮
        edit_prompts_btn = QPushButton("✏️ 编辑AI提示词")
        edit_prompts_btn.setToolTip("在应用中编辑 config/prompts.json，保存后立即生效")
        edit_prompts_btn.setStyleSheet("""
            QPushButton { background-color: #1976D2; color: white; padding: 6px 12px; border: none; border-radius: 4px; font-weight: bold; }
            QPushButton:hover { background-color: #1565C0; }
        """)
        edit_prompts_btn.clicked.connect(self.open_prompt_editor)
        providers_header.addWidget(edit_prompts_btn)

        providers_header.addStretch()

        add_model_btn = QPushButton("+ 添加AI模型")
        add_model_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        add_model_btn.clicked.connect(self.add_model)
        providers_header.addWidget(add_model_btn)

        bottom_layout.addLayout(providers_header)

        # 服务提供商表格
        self.providers_table = QTableWidget()
        self.providers_table.setColumnCount(7)
        self.providers_table.setHorizontalHeaderLabels(["模型名称", "模型类型", "服务商", "状态", "API KEY", "Base URL", "备注"])

        # 设置表格样式 - 更紧凑
        self.providers_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
                gridline-color: #e8e8e8;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 6px 4px;
                border-bottom: 1px solid #f0f0f0;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 6px 4px;
                border: none;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
            }
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }
        """)

        # 设置表格属性
        self.providers_table.setAlternatingRowColors(True)
        self.providers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.providers_table.verticalHeader().setVisible(False)
        self.providers_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 禁用编辑

        # 设置右键菜单
        self.providers_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.providers_table.customContextMenuRequested.connect(self.show_providers_context_menu)

        # 设置列宽
        header = self.providers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)            # 模型名称
        header.setSectionResizeMode(1, QHeaderView.Fixed)            # 模型类型
        header.setSectionResizeMode(2, QHeaderView.Fixed)            # 服务商
        header.setSectionResizeMode(3, QHeaderView.Fixed)            # 状态
        header.setSectionResizeMode(4, QHeaderView.Fixed)            # API KEY
        header.setSectionResizeMode(5, QHeaderView.Fixed)            # Base URL
        header.setSectionResizeMode(6, QHeaderView.Stretch)          # 备注（自适应）

        # 设置固定宽度列
        self.providers_table.setColumnWidth(0, 180)  # 模型名称列
        self.providers_table.setColumnWidth(1, 80)   # 模型类型列
        self.providers_table.setColumnWidth(2, 80)   # 服务商列
        self.providers_table.setColumnWidth(3, 60)   # 状态列
        self.providers_table.setColumnWidth(4, 120)  # API KEY列
        self.providers_table.setColumnWidth(5, 200)  # Base URL列

        bottom_layout.addWidget(self.providers_table)

        splitter.addWidget(bottom_widget)

    def open_prompt_editor(self):
        from app import PromptEditorDialog
        try:
            dlg = PromptEditorDialog(self)
            dlg.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开提示词编辑器: {str(e)}")

        # 设置分割器比例 - 上部分增加高度以显示4行数据，下部分相应减少
        splitter.setSizes([200, 400])

        # 整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
        """)

    def load_data(self):
        """加载数据"""
        # 加载服务提供商
        self.providers = self.config_manager.get_all_providers()
        self.load_providers_table()

        # 加载启用的模型
        self.active_models = self.config_manager.get_active_models()
        self.load_models_list()

    def load_models_list(self):
        """加载模型表格"""
        # 设置表格行数
        model_types = {
            'text': '📝 文字模型',
            'image': '🖼️ 图像模型',
            'audio': '🎵 音频模型',
            'video': '🎬 视频模型'
        }

        self.models_table.setRowCount(len(model_types))

        for row, (model_type, display_name) in enumerate(model_types.items()):
            model_info = self.active_models.get(model_type, {
                'is_enabled': False,
                'provider_id': None,
                'provider_name': '',
                'model_name': ''
            })

            # 模型类型列
            type_item = QTableWidgetItem(display_name)
            type_item.setData(Qt.UserRole, model_type)
            self.models_table.setItem(row, 0, type_item)

            # 状态列 - 同时检查is_enabled和provider_id
            if model_info.get('is_enabled') and model_info.get('provider_id'):
                status_item = QTableWidgetItem("✅ 已启用")
                status_item.setBackground(QColor(232, 245, 232))
            else:
                status_item = QTableWidgetItem("❌ 未启用")
                status_item.setBackground(QColor(245, 245, 245))
            self.models_table.setItem(row, 1, status_item)

            # 当前配置列 - 同时检查is_enabled和provider_id
            if model_info.get('is_enabled') and model_info.get('provider_id'):
                # 查找提供商信息
                provider = None
                for p in self.providers:
                    if p['id'] == model_info['provider_id']:
                        provider = p
                        break

                if provider:
                    # 根据模型类型获取对应的模型名称
                    model_field_map = {
                        'text': 'text_model',
                        'image': 'image_model',
                        'audio': 'audio_model',
                        'video': 'video_model'
                    }
                    model_field = model_field_map.get(model_type, 'text_model')
                    model_name = provider.get(model_field, '未设置')
                    config_text = f"{provider['name']} ({model_name})"
                else:
                    config_text = "未配置"
            else:
                config_text = "未配置"

            config_item = QTableWidgetItem(config_text)
            self.models_table.setItem(row, 2, config_item)

    def load_providers_table(self):
        """加载服务提供商表格 - 每种模型单独一行"""
        # 收集所有模型数据
        model_rows = []

        for provider in self.providers:
            # 文字模型
            if provider.get('support_text') and provider.get('text_model'):
                model_rows.append({
                    'model_name': provider['text_model'],
                    'model_type': '文字',
                    'provider_name': provider['name'],
                    'provider_id': provider['id'],
                    'api_key': provider.get('api_key', ''),
                    'base_url': provider.get('base_url', ''),
                    'remarks': provider.get('remarks', ''),
                    'is_active': provider.get('is_active', False)
                })

            # 图像模型
            if provider.get('support_image') and provider.get('image_model'):
                model_rows.append({
                    'model_name': provider['image_model'],
                    'model_type': '图像',
                    'provider_name': provider['name'],
                    'provider_id': provider['id'],
                    'api_key': provider.get('api_key', ''),
                    'base_url': provider.get('base_url', ''),
                    'remarks': provider.get('remarks', ''),
                    'is_active': provider.get('is_active', False)
                })

            # 音频模型
            if provider.get('support_audio') and provider.get('audio_model'):
                model_rows.append({
                    'model_name': provider['audio_model'],
                    'model_type': '音频',
                    'provider_name': provider['name'],
                    'provider_id': provider['id'],
                    'api_key': provider.get('api_key', ''),
                    'base_url': provider.get('base_url', ''),
                    'remarks': provider.get('remarks', ''),
                    'is_active': provider.get('is_active', False)
                })

            # 视频模型
            if provider.get('support_video') and provider.get('video_model'):
                model_rows.append({
                    'model_name': provider['video_model'],
                    'model_type': '视频',
                    'provider_name': provider['name'],
                    'provider_id': provider['id'],
                    'api_key': provider.get('api_key', ''),
                    'base_url': provider.get('base_url', ''),
                    'remarks': provider.get('remarks', ''),
                    'is_active': provider.get('is_active', False)
                })

        # 设置表格行数
        self.providers_table.setRowCount(len(model_rows))

        # 填充表格数据
        for row, model_data in enumerate(model_rows):
            # 模型名称
            model_name_item = QTableWidgetItem(model_data['model_name'])
            model_name_item.setTextAlignment(Qt.AlignCenter)
            self.providers_table.setItem(row, 0, model_name_item)

            # 模型类型
            model_type_item = QTableWidgetItem(model_data['model_type'])
            model_type_item.setTextAlignment(Qt.AlignCenter)
            self.providers_table.setItem(row, 1, model_type_item)

            # 服务商
            provider_item = QTableWidgetItem(model_data['provider_name'])
            provider_item.setTextAlignment(Qt.AlignCenter)
            self.providers_table.setItem(row, 2, provider_item)

            # 状态
            status = "启用" if model_data['is_active'] else "停用"
            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignCenter)
            if model_data['is_active']:
                status_item.setBackground(QColor(232, 245, 232))
            else:
                status_item.setBackground(QColor(245, 245, 245))
            self.providers_table.setItem(row, 3, status_item)

            # API KEY
            api_key_display = model_data['api_key'][:10] + '...' if len(model_data['api_key']) > 10 else model_data['api_key']
            api_key_item = QTableWidgetItem(api_key_display)
            api_key_item.setTextAlignment(Qt.AlignCenter)
            self.providers_table.setItem(row, 4, api_key_item)

            # Base URL
            base_url_item = QTableWidgetItem(model_data['base_url'])
            base_url_item.setTextAlignment(Qt.AlignCenter)
            self.providers_table.setItem(row, 5, base_url_item)

            # 备注
            remarks_item = QTableWidgetItem(model_data['remarks'])
            remarks_item.setTextAlignment(Qt.AlignCenter)
            self.providers_table.setItem(row, 6, remarks_item)



    def add_model(self):
        """添加AI模型"""
        dialog = ModelDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                # 这里需要添加新的方法来处理单个模型的添加
                success = self.config_manager.add_single_model(**data)
                if success:
                    QMessageBox.information(self, "成功", "AI模型添加成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "错误", "添加AI模型失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加AI模型时发生错误：{str(e)}")

    def edit_model(self, model_data: Dict[str, Any]):
        """编辑AI模型"""
        dialog = ModelDialog(model_data, parent=self)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                # 这里需要添加新的方法来处理单个模型的更新
                success = self.config_manager.update_single_model(model_data, **data)
                if success:
                    QMessageBox.information(self, "成功", "AI模型更新成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "错误", "更新AI模型失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"更新AI模型时发生错误：{str(e)}")

    def delete_provider(self, provider: Dict[str, Any]):
        """删除服务提供商"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除服务提供商 '{provider['name']}' 吗？\n\n如果有关联的启用模型，将只是标记为停用。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.config_manager.delete_provider(provider['id'])
                if success:
                    QMessageBox.information(self, "成功", "服务提供商删除成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "错误", "删除服务提供商失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除服务提供商时发生错误：{str(e)}")

    def delete_model_config(self, model_data: Dict[str, Any]):
        """删除模型配置"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除模型 '{model_data['model_name']}' 的配置吗？\n\n这将清空该服务商的{model_data['model_type']}模型配置。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 找到对应的服务商
                provider = None
                for p in self.providers:
                    if p['id'] == model_data['provider_id']:
                        provider = p
                        break

                if provider:
                    # 根据模型类型清空对应的模型配置
                    model_field_map = {
                        '文字': 'text_model',
                        '图像': 'image_model',
                        '音频': 'audio_model',
                        '视频': 'video_model'
                    }

                    model_field = model_field_map.get(model_data['model_type'])
                    if model_field:
                        # 更新服务商数据，清空对应的模型字段
                        update_data = {k: v for k, v in provider.items()}
                        update_data[model_field] = ''

                        success = self.config_manager.update_provider(provider['id'], **update_data)
                        if success:
                            QMessageBox.information(self, "成功", "模型配置删除成功")
                            self.load_data()
                        else:
                            QMessageBox.warning(self, "错误", "删除模型配置失败")
                    else:
                        QMessageBox.warning(self, "错误", "未知的模型类型")
                else:
                    QMessageBox.warning(self, "错误", "未找到对应的服务商")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除模型配置时发生错误：{str(e)}")

    def bind_model(self, model_type):
        """绑定AI模型"""
        self.configure_model(model_type)

    def enable_model(self, model_type):
        """启用模型"""
        # 检查是否已有绑定的模型
        model_info = self.active_models.get(model_type, {})
        if not model_info.get('provider_id'):
            QMessageBox.warning(self, "警告", "请先绑定AI模型")
            return

        try:
            success = self.config_manager.set_active_model(model_type, model_info['provider_id'])
            if success:
                QMessageBox.information(self, "成功", f"{model_type}模型已启用")
                self.load_data()
            else:
                QMessageBox.warning(self, "错误", "启用模型失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启用模型时发生错误：{str(e)}")

    def disable_model(self, model_type):
        """停用模型"""
        try:
            success = self.config_manager.set_active_model(model_type, None)
            if success:
                QMessageBox.information(self, "成功", f"{model_type}模型已停用")
                self.load_data()
            else:
                QMessageBox.warning(self, "错误", "停用模型失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停用模型时发生错误：{str(e)}")

    def show_models_context_menu(self, position):
        """显示全局启用模型表格的右键菜单"""
        item = self.models_table.itemAt(position)
        if item is None:
            return

        row = item.row()
        model_type_item = self.models_table.item(row, 0)
        if model_type_item is None:
            return

        model_type = model_type_item.data(Qt.UserRole)
        model_info = self.active_models.get(model_type, {})

        menu = QMenu(self)

        # 绑定模型动作
        bind_action = QAction("🔗 绑定AI模型", self)
        bind_action.triggered.connect(lambda: self.bind_model(model_type))
        menu.addAction(bind_action)

        # 启用/停用动作
        if model_info.get('provider_id'):
            disable_action = QAction("❌ 停用模型", self)
            disable_action.triggered.connect(lambda: self.disable_model(model_type))
            menu.addAction(disable_action)
        else:
            enable_action = QAction("✅ 启用模型", self)
            enable_action.triggered.connect(lambda: self.enable_model(model_type))
            menu.addAction(enable_action)

        # 获取鼠标全局位置，确保菜单从鼠标位置向下展示
        global_pos = self.models_table.mapToGlobal(position)
        # 稍微向下偏移几个像素，确保菜单在鼠标下方显示
        global_pos.setY(global_pos.y() + 5)
        menu.exec(global_pos)

    def show_providers_context_menu(self, position):
        """显示AI候选池表格的右键菜单"""
        item = self.providers_table.itemAt(position)
        if item is None:
            return

        row = item.row()

        # 获取当前行的模型数据
        model_name_item = self.providers_table.item(row, 0)
        model_type_item = self.providers_table.item(row, 1)
        provider_name_item = self.providers_table.item(row, 2)

        if not all([model_name_item, model_type_item, provider_name_item]):
            return

        # 构建模型数据
        model_data = {
            'model_name': model_name_item.text(),
            'model_type': model_type_item.text(),
            'provider_name': provider_name_item.text(),
        }

        # 从providers列表中找到完整的数据
        for provider in self.providers:
            provider_models = []
            if provider.get('support_text') and provider.get('text_model'):
                provider_models.append({'name': provider['text_model'], 'type': '文字'})
            if provider.get('support_image') and provider.get('image_model'):
                provider_models.append({'name': provider['image_model'], 'type': '图像'})
            if provider.get('support_audio') and provider.get('audio_model'):
                provider_models.append({'name': provider['audio_model'], 'type': '音频'})
            if provider.get('support_video') and provider.get('video_model'):
                provider_models.append({'name': provider['video_model'], 'type': '视频'})

            for model in provider_models:
                if (model['name'] == model_data['model_name'] and
                    model['type'] == model_data['model_type'] and
                    provider['name'] == model_data['provider_name']):
                    model_data.update({
                        'provider_id': provider['id'],
                        'api_key': provider.get('api_key', ''),
                        'base_url': provider.get('base_url', ''),
                        'remarks': provider.get('remarks', ''),
                        'is_active': provider.get('is_active', False)
                    })
                    break

        menu = QMenu(self)

        # 编辑动作
        edit_action = QAction("✏️ 编辑模型", self)
        edit_action.triggered.connect(lambda: self.edit_model(model_data))
        menu.addAction(edit_action)

        # 删除动作
        delete_action = QAction("🗑️ 删除模型", self)
        delete_action.triggered.connect(lambda: self.delete_model_config(model_data))
        menu.addAction(delete_action)

        # 获取鼠标全局位置，确保菜单从鼠标位置向下展示
        global_pos = self.providers_table.mapToGlobal(position)
        # 稍微向下偏移几个像素，确保菜单在鼠标下方显示
        global_pos.setY(global_pos.y() + 5)
        menu.exec(global_pos)

    # 已移除双击事件处理，只保留右键菜单操作
    # def on_model_cell_double_clicked(self, row, column):
    #     """处理模型表格双击事件"""
    #     # 获取模型类型
    #     type_item = self.models_table.item(row, 0)
    #     if type_item:
    #         model_type = type_item.data(Qt.UserRole)
    #         self.configure_model(model_type)

    def configure_model(self, model_type: str):
        """配置模型"""
        # 获取支持该模型类型的提供商
        support_field_map = {
            'text': 'text',
            'image': 'image',
            'audio': 'audio',
            'video': 'video'
        }
        support_type = support_field_map.get(model_type, 'text')
        available_providers = self.config_manager.get_providers_by_support_type(support_type)

        if not available_providers:
            QMessageBox.warning(self, "警告", f"没有找到支持{model_type}模型的服务提供商")
            return

        # 创建选择对话框
        dialog = ModelConfigDialog(model_type, available_providers,
                                 self.active_models.get(model_type, {}), self)
        if dialog.exec() == QDialog.Accepted:
            provider_id = dialog.get_selected_provider_id()
            if provider_id:
                # 更新模型配置
                success = self.config_manager.update_active_model(model_type, provider_id)
                if success:
                    QMessageBox.information(self, "成功", f"{model_type}模型配置已更新")
                    self.load_data()  # 重新加载数据
                else:
                    QMessageBox.warning(self, "错误", "更新模型配置失败")
            else:
                # 禁用模型
                success = self.config_manager.update_active_model(model_type, None)
                if success:
                    QMessageBox.information(self, "成功", f"{model_type}模型已禁用")
                    self.load_data()  # 重新加载数据
                else:
                    QMessageBox.warning(self, "错误", "禁用模型失败")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    window = AIModelManagementUI()
    window.show()

    sys.exit(app.exec())


if __name__ == '__main__':
    main()