#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
承运人校验API路由
处理承运人识别和校验相关的HTTP请求
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Body
from api.models.user_schemas import (
    CarrierValidationRequest,
    BatchValidationRequest,
    CarrierValidationResponse,
    BatchValidationResponse,
    APIResponse
)
from api.services.carrier_service import CarrierValidationService, get_carrier_validation_service
from api.middleware.auth import get_current_user_required, get_current_user_optional
from api.models.user_schemas import UserInfo
from api.utils.logger import api_logger

router = APIRouter()

@router.post("/carriers/validate", response_model=CarrierValidationResponse)
async def validate_carrier(
    request: CarrierValidationRequest,
    carrier_service: CarrierValidationService = Depends(get_carrier_validation_service),
    current_user: UserInfo = Depends(get_current_user_optional)
):
    """
    单个承运人校验
    
    - **tracking_number**: 提单号/箱号
    
    可选认证（有用户登录时记录用户信息）
    """
    try:
        user_info = f" (用户: {current_user.name})" if current_user else ""
        api_logger.info(f"承运人校验请求: {request.tracking_number}{user_info}")
        
        result = carrier_service.validate_tracking_number(request.tracking_number)
        
        success = result.is_valid
        api_logger.info(f"承运人校验结果: {request.tracking_number} - {'成功' if success else '失败'}")
        
        return CarrierValidationResponse(
            success=success,
            result=result
        )
        
    except Exception as e:
        api_logger.error(f"承运人校验异常: {str(e)}")
        raise HTTPException(status_code=500, detail="校验服务异常")

@router.post("/carriers/validate-batch", response_model=BatchValidationResponse)
async def validate_carriers_batch(
    request: BatchValidationRequest,
    carrier_service: CarrierValidationService = Depends(get_carrier_validation_service),
    current_user: UserInfo = Depends(get_current_user_optional)
):
    """
    批量承运人校验
    
    - **tracking_numbers**: 提单号/箱号列表（最多200个）
    - **deduplicate**: 是否去重（可选，默认true）
    
    可选认证（有用户登录时记录用户信息）
    """
    try:
        user_info = f" (用户: {current_user.name})" if current_user else ""
        api_logger.info(f"批量承运人校验请求: {len(request.tracking_numbers)}个{user_info}")
        
        if len(request.tracking_numbers) > 200:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量校验数量不能超过200个"
            )
        
        result = carrier_service.batch_validate_tracking_numbers(
            tracking_numbers=request.tracking_numbers,
            deduplicate=request.deduplicate
        )
        
        api_logger.info(f"批量承运人校验完成: 总数{result.summary.get('total')}, 成功{result.summary.get('valid')}, 失败{result.summary.get('invalid')}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"批量承运人校验异常: {str(e)}")
        raise HTTPException(status_code=500, detail="批量校验服务异常")

@router.post("/carriers/parse-batch", response_model=APIResponse)
async def parse_batch_input(
    text_input: str = Body(..., description="批量输入文本", embed=True),
    carrier_service: CarrierValidationService = Depends(get_carrier_validation_service),
    current_user: UserInfo = Depends(get_current_user_optional)
):
    """
    解析批量输入文本
    
    - **text_input**: 批量输入的文本（支持多种分隔符）
    
    返回解析后的提单号列表，但不进行校验
    """
    try:
        user_info = f" (用户: {current_user.name})" if current_user else ""
        api_logger.info(f"解析批量输入文本{user_info}")
        
        tracking_numbers = carrier_service.parse_batch_input(text_input)
        
        api_logger.info(f"批量文本解析完成: 解析出{len(tracking_numbers)}个提单号")
        
        return APIResponse(
            success=True,
            message=f"解析成功，共{len(tracking_numbers)}个提单号",
            data={
                "tracking_numbers": tracking_numbers,
                "count": len(tracking_numbers)
            }
        )
        
    except Exception as e:
        api_logger.error(f"批量文本解析异常: {str(e)}")
        raise HTTPException(status_code=500, detail="解析服务异常")

@router.post("/carriers/validate-text", response_model=BatchValidationResponse)
async def validate_text_batch(
    text_input: str = Body(..., description="批量输入文本", embed=True),
    deduplicate: bool = Body(True, description="是否去重", embed=True),
    carrier_service: CarrierValidationService = Depends(get_carrier_validation_service),
    current_user: UserInfo = Depends(get_current_user_optional)
):
    """
    解析并校验批量输入文本（一步完成）
    
    - **text_input**: 批量输入的文本（支持多种分隔符）
    - **deduplicate**: 是否去重（可选，默认true）
    
    这是前端最常用的接口，将解析和校验合并为一步
    """
    try:
        user_info = f" (用户: {current_user.name})" if current_user else ""
        api_logger.info(f"文本批量校验请求{user_info}")
        
        # 解析文本
        tracking_numbers = carrier_service.parse_batch_input(text_input)
        
        if not tracking_numbers:
            return BatchValidationResponse(
                success=False,
                results=[],
                summary={
                    "total": 0,
                    "valid": 0,
                    "invalid": 0,
                    "error": "未解析出有效的提单号"
                }
            )
        
        if len(tracking_numbers) > 200:
            tracking_numbers = tracking_numbers[:200]
        
        # 批量校验
        result = carrier_service.batch_validate_tracking_numbers(
            tracking_numbers=tracking_numbers,
            deduplicate=deduplicate
        )
        
        api_logger.info(f"文本批量校验完成: 总数{result.summary.get('total')}, 成功{result.summary.get('valid')}, 失败{result.summary.get('invalid')}")
        
        return result
        
    except Exception as e:
        api_logger.error(f"文本批量校验异常: {str(e)}")
        raise HTTPException(status_code=500, detail="文本校验服务异常")