#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据模型定义
定义请求和响应的数据结构
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


# 请求模型
class ShipmentQueryRequest(BaseModel):
    """船期查询请求"""
    container_number: str = Field(..., description="集装箱号", min_length=1, max_length=50)
    carrier_code: Optional[str] = Field(None, description="船公司代码")
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="回调URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")

    @validator('container_number')
    def validate_container_number(cls, v):
        if not v or not v.strip():
            raise ValueError('集装箱号不能为空')
        return v.strip().upper()

    @validator('carrier_code')
    def validate_carrier_code(cls, v):
        if v:
            return v.strip().upper()
        return v


class TaskCancelRequest(BaseModel):
    """任务取消请求"""
    reason: Optional[str] = Field(None, description="取消原因")


class TaskRetryRequest(BaseModel):
    """任务重试请求"""
    reason: Optional[str] = Field(None, description="重试原因")
    reset_attempts: bool = Field(False, description="是否重置重试次数")


# 响应模型
class TaskResponse(BaseModel):
    """任务响应"""
    task_id: str = Field(..., description="任务ID")
    container_number: str = Field(..., description="集装箱号")
    carrier_code: Optional[str] = Field(None, description="船公司代码")
    status: TaskStatus = Field(..., description="任务状态")
    priority: TaskPriority = Field(..., description="任务优先级")
    progress: int = Field(0, description="进度百分比", ge=0, le=100)
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    result: Optional[Dict[str, Any]] = Field(None, description="查询结果")
    result_files: Optional[Dict[str, str]] = Field(None, description="结果文件路径，如截图等")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class TaskListResponse(BaseModel):
    """任务列表响应"""
    tasks: List[TaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")


class CarrierInfo(BaseModel):
    """船公司信息"""
    code: str = Field(..., description="船公司代码")
    name: str = Field(..., description="船公司名称")
    website: Optional[str] = Field(None, description="官网地址")
    supported: bool = Field(..., description="是否支持查询")
    last_updated: Optional[datetime] = Field(None, description="最后更新时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class CarrierListResponse(BaseModel):
    """船公司列表响应"""
    carriers: List[CarrierInfo] = Field(..., description="船公司列表")
    total: int = Field(..., description="总数量")


class TaskStats(BaseModel):
    """任务统计信息"""
    total: int = Field(..., description="总任务数")
    completed: int = Field(..., description="已完成任务数")
    running: int = Field(..., description="运行中任务数")
    pending: int = Field(..., description="等待中任务数")
    failed: int = Field(..., description="失败任务数")
    cancelled: int = Field(..., description="已取消任务数")
    success_rate: float = Field(..., description="成功率")
    average_duration: float = Field(..., description="平均处理时长(秒)")


class QueryStats(BaseModel):
    """查询统计信息"""
    total_queries: int = Field(..., description="总查询数")
    successful_queries: int = Field(..., description="成功查询数")
    failed_queries: int = Field(..., description="失败查询数")
    pending_queries: int = Field(..., description="待处理查询数")
    average_duration: float = Field(..., description="平均处理时长(秒)")
    success_rate: float = Field(..., description="成功率")


class StatsResponse(BaseModel):
    """统计响应"""
    today: QueryStats = Field(..., description="今日统计")
    this_week: QueryStats = Field(..., description="本周统计")
    this_month: QueryStats = Field(..., description="本月统计")
    all_time: QueryStats = Field(..., description="总计统计")


class TaskLogEntry(BaseModel):
    """任务日志条目"""
    timestamp: datetime = Field(..., description="时间戳")
    level: str = Field(..., description="日志级别")
    message: str = Field(..., description="日志消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class TaskLogsResponse(BaseModel):
    """任务日志响应"""
    task_id: str = Field(..., description="任务ID")
    logs: List[TaskLogEntry] = Field(..., description="日志列表")
    total: int = Field(..., description="总日志数")


# WebSocket消息模型
class WebSocketMessage(BaseModel):
    """WebSocket消息"""
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class TaskStatusUpdate(BaseModel):
    """任务状态更新"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    progress: int = Field(0, description="进度百分比", ge=0, le=100)
    message: Optional[str] = Field(None, description="状态消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


# 通用响应模型
class APIResponse(BaseModel):
    """通用API响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误消息")
    error_code: str = Field(..., description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }