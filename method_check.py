#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统性方法匹配检查脚本
"""

import re

def check_method_references():
    """检查所有方法引用是否有对应的定义"""
    print("🔍 系统性检查方法引用和定义...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取所有 .connect(self.method_name) 的引用
        connect_pattern = r'\.connect\(self\.([a-zA-Z_][a-zA-Z0-9_]*)\)'
        method_references = re.findall(connect_pattern, content)
        
        # 提取所有 self.method_name() 的调用
        call_pattern = r'self\.([a-zA-Z_][a-zA-Z0-9_]*)\('
        method_calls = re.findall(call_pattern, content)
        
        # 合并所有引用
        all_references = set(method_references + method_calls)
        
        # 提取所有方法定义
        def_pattern = r'def ([a-zA-Z_][a-zA-Z0-9_]*)\(self'
        method_definitions = set(re.findall(def_pattern, content))
        
        print(f"📊 发现 {len(all_references)} 个方法引用")
        print(f"📊 发现 {len(method_definitions)} 个方法定义")
        
        # 检查缺失的方法
        missing_methods = all_references - method_definitions
        
        # 过滤掉一些已知的属性和特殊方法
        ignore_list = {
            'parent', 'accept', 'reject', 'close', 'exec', 'show', 'hide', 'setText', 
            'setStyleSheet', 'clicked', 'textChanged', 'currentTextChanged', 'toggled',
            'timeout', 'doubleClicked', 'cellClicked', 'triggered', 'start', 'stop',
            'get_status', 'running', 'use_two_stage', 'handle_task_completion'
        }
        
        missing_methods = missing_methods - ignore_list
        
        if missing_methods:
            print(f"\n❌ 发现 {len(missing_methods)} 个缺失的方法:")
            for method in sorted(missing_methods):
                print(f"   • {method}")
            
            return False, missing_methods
        else:
            print("\n✅ 所有方法引用都有对应的定义")
            return True, set()
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, set()

if __name__ == "__main__":
    success, missing = check_method_references()
    if not success and missing:
        print(f"\n🔧 需要添加的方法:")
        for method in sorted(missing):
            print(f"""
    def {method}(self):
        \"\"\"TODO: 实现 {method} 方法\"\"\"
        pass
""")