#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速语法检查脚本
"""

def check_app_syntax():
    """检查 app.py 语法"""
    try:
        import ast
        
        print("正在检查 app.py 语法...")
        
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析语法
        ast.parse(content)
        
        print("✅ app.py 语法检查通过！")
        print("\n🎉 修复完成说明：")
        print("1. 修复了 AttributeError 错误 - TaskProcessor 延迟初始化")
        print("2. 添加了两阶段任务状态显示：'网页抓取' 和 'AI分析' 列")
        print("3. 实现了状态点击查看详情和重试功能")
        print("4. 添加了彩色状态显示：绿色(完成)、橙色(进行中)、红色(失败)、灰色(待处理)")
        print("5. 清理了重复的代码片段和语法错误")
        print("\n现在可以运行 app.py 了！")
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_app_syntax()