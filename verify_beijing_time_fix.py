#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple verification that Beijing time fix is working
"""

import sqlite3
import sys
import os
sys.path.append(os.path.dirname(__file__))

from shipment_manager import ShipmentManager, get_beijing_time_str

def verify_beijing_time_fix():
    """Verify that Beijing time fix is working"""
    print("=== Beijing Time Fix Verification ===")
    
    try:
        # Test creating and updating a record
        manager = ShipmentManager()
        
        print("\n1. Creating test record...")
        record_id = manager.create_shipment_record(
            bill_of_lading="BEIJING_TIME_VERIFY_001",
            container_number="VERIFY_CONT_001", 
            carrier_company="TEST",
            estimated_arrival_time=None,
            remarks="Beijing time verification test",
            created_by="verify_test"
        )
        print(f"   Record created with ID: {record_id}")
        
        print("\n2. Updating record status...")
        manager.update_shipment_status(record_id, "verified", "verify_test")
        
        print("\n3. Checking database record...")
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT created_at, updated_at 
            FROM shipment_records 
            WHERE id = ?
        """, (record_id,))
        
        result = cursor.fetchone()
        if result:
            created_at, updated_at = result
            print(f"   Created at: {created_at}")
            print(f"   Updated at: {updated_at}")
            
            # Check if both times have Beijing timezone
            created_ok = '+08:00' in str(created_at)
            updated_ok = '+08:00' in str(updated_at)
            
            print(f"\n4. Verification Results:")
            print(f"   Created time has +08:00: {created_ok}")
            print(f"   Updated time has +08:00: {updated_ok}")
            
            if created_ok and updated_ok:
                print("\n[SUCCESS] Beijing time fix is working correctly!")
                print("The shipment_records.updated_at field now uses Beijing time.")
                return True
            else:
                print("\n[ERROR] Beijing time fix is not working properly.")
                return False
        else:
            print("\n[ERROR] Could not find test record.")
            return False
            
        conn.close()
        
    except Exception as e:
        print(f"\n[ERROR] Verification failed: {e}")
        return False

if __name__ == "__main__":
    success = verify_beijing_time_fix()
    if success:
        print("\n=== CONCLUSION ===")
        print("The database trigger removal was successful.")
        print("All shipment_records.updated_at operations now use Beijing time (+08:00).")
        print("The original issue has been completely resolved.")
    else:
        print("\n=== ISSUE ===") 
        print("There may still be problems with the Beijing time implementation.")