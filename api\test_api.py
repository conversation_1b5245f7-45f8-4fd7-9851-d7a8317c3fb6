#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
用于测试船期查询API的基本功能
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def test_health_check(self) -> bool:
        """测试健康检查接口"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            print(f"健康检查: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"服务状态: {data.get('status')}")
                return True
            return False
        except Exception as e:
            print(f"健康检查失败: {e}")
            return False
    
    async def test_create_query_task(self, bill_of_lading: str = None, 
                                   container_number: str = None,
                                   carrier_code: str = "MSK") -> Dict[str, Any]:
        """测试创建查询任务"""
        try:
            payload = {
                "carrier_code": carrier_code,
                "priority": "normal"
            }
            
            if bill_of_lading:
                payload["bill_of_lading"] = bill_of_lading
            if container_number:
                payload["container_number"] = container_number
            
            print(f"创建查询任务: {json.dumps(payload, ensure_ascii=False)}")
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/shipment/query",
                json=payload
            )
            
            print(f"响应状态: {response.status_code}")
            data = response.json()
            print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if response.status_code == 200 and data.get("success"):
                return data.get("data", {})
            else:
                print(f"创建任务失败: {data.get('message')}")
                return {}
        
        except Exception as e:
            print(f"创建查询任务失败: {e}")
            return {}
    
    async def test_get_task_status(self, task_id: str) -> Dict[str, Any]:
        """测试获取任务状态"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/task/{task_id}"
            )
            
            print(f"任务状态查询: {response.status_code}")
            data = response.json()
            
            if response.status_code == 200:
                task_data = data.get("data", {})
                print(f"任务ID: {task_data.get('task_id')}")
                print(f"状态: {task_data.get('status')}")
                print(f"进度: {task_data.get('progress')}%")
                
                if task_data.get("result"):
                    print("查询结果:")
                    print(json.dumps(task_data["result"], ensure_ascii=False, indent=2))
                
                return task_data
            else:
                print(f"查询任务状态失败: {data.get('message')}")
                return {}
        
        except Exception as e:
            print(f"查询任务状态失败: {e}")
            return {}
    
    async def test_get_carriers(self) -> List[Dict[str, Any]]:
        """测试获取支持的船公司"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/shipment/carriers"
            )
            
            print(f"获取船公司列表: {response.status_code}")
            data = response.json()
            
            if response.status_code == 200:
                carriers = data.get("data", [])
                print(f"支持的船公司数量: {len(carriers)}")
                for carrier in carriers[:5]:  # 显示前5个
                    print(f"  - {carrier.get('code')}: {carrier.get('name')}")
                return carriers
            else:
                print(f"获取船公司列表失败: {data.get('message')}")
                return []
        
        except Exception as e:
            print(f"获取船公司列表失败: {e}")
            return []
    
    async def test_get_tasks(self) -> List[Dict[str, Any]]:
        """测试获取任务列表"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/tasks?limit=5"
            )
            
            print(f"获取任务列表: {response.status_code}")
            data = response.json()
            
            if response.status_code == 200:
                tasks = data.get("data", [])
                print(f"任务数量: {len(tasks)}")
                for task in tasks:
                    print(f"  - {task.get('task_id')}: {task.get('status')}")
                return tasks
            else:
                print(f"获取任务列表失败: {data.get('message')}")
                return []
        
        except Exception as e:
            print(f"获取任务列表失败: {e}")
            return []
    
    async def test_get_stats(self) -> Dict[str, Any]:
        """测试获取统计信息"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/shipment/stats"
            )
            
            print(f"获取统计信息: {response.status_code}")
            data = response.json()
            
            if response.status_code == 200:
                stats = data.get("data", {})
                print(f"统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
                return stats
            else:
                print(f"获取统计信息失败: {data.get('message')}")
                return {}
        
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
    
    async def wait_for_task_completion(self, task_id: str, max_wait: int = 60) -> Dict[str, Any]:
        """等待任务完成"""
        print(f"等待任务 {task_id} 完成...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            task_data = await self.test_get_task_status(task_id)
            
            if not task_data:
                break
            
            status = task_data.get("status")
            if status in ["completed", "failed", "cancelled"]:
                print(f"任务完成，最终状态: {status}")
                return task_data
            
            print(f"任务进行中，状态: {status}, 进度: {task_data.get('progress', 0)}%")
            await asyncio.sleep(2)
        
        print("等待超时")
        return {}
    
    async def run_full_test(self):
        """运行完整测试"""
        print("=" * 50)
        print("开始API功能测试")
        print("=" * 50)
        
        # 1. 健康检查
        print("\n1. 测试健康检查")
        health_ok = await self.test_health_check()
        if not health_ok:
            print("服务不可用，测试终止")
            return
        
        # 2. 获取船公司列表
        print("\n2. 测试获取船公司列表")
        carriers = await self.test_get_carriers()
        
        # 3. 获取统计信息
        print("\n3. 测试获取统计信息")
        await self.test_get_stats()
        
        # 4. 获取任务列表
        print("\n4. 测试获取任务列表")
        await self.test_get_tasks()
        
        # 5. 创建查询任务
        print("\n5. 测试创建查询任务")
        task_data = await self.test_create_query_task(
            bill_of_lading="TEST123456",
            carrier_code="MSK"
        )
        
        if task_data and task_data.get("task_id"):
            task_id = task_data["task_id"]
            
            # 6. 等待任务完成
            print("\n6. 等待任务完成")
            final_task_data = await self.wait_for_task_completion(task_id)
            
            if final_task_data:
                print("\n任务执行完成!")
            else:
                print("\n任务执行超时或失败")
        
        print("\n=" * 50)
        print("API功能测试完成")
        print("=" * 50)
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    tester = APITester()
    
    try:
        await tester.run_full_test()
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main())