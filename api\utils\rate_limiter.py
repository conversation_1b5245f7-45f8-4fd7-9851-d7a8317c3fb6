#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API限流工具
基于滑动窗口的请求频率限制
"""

import time
import asyncio
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, deque
from fastapi import Request, HTTPException
from datetime import datetime, timedelta

from api.utils.logger import api_logger

class RateLimiter:
    """基于滑动窗口的限流器"""
    
    def __init__(self, max_requests: int, window_seconds: int):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: deque = deque()
    
    def is_allowed(self) -> Tuple[bool, Dict[str, any]]:
        """检查是否允许请求"""
        current_time = time.time()
        
        # 清理过期请求
        while self.requests and self.requests[0] <= current_time - self.window_seconds:
            self.requests.popleft()
        
        # 检查是否超出限制
        if len(self.requests) >= self.max_requests:
            # 计算重置时间
            reset_time = self.requests[0] + self.window_seconds
            return False, {
                "allowed": False,
                "limit": self.max_requests,
                "remaining": 0,
                "reset_time": reset_time,
                "retry_after": int(reset_time - current_time)
            }
        
        # 记录请求
        self.requests.append(current_time)
        
        return True, {
            "allowed": True,
            "limit": self.max_requests,
            "remaining": self.max_requests - len(self.requests),
            "reset_time": current_time + self.window_seconds,
            "retry_after": 0
        }

class APIRateLimiter:
    """API限流管理器"""
    
    def __init__(self):
        # 客户端限流器: {client_id: RateLimiter}
        self.client_limiters: Dict[str, RateLimiter] = {}
        # 全局限流器
        self.global_limiter = RateLimiter(max_requests=1000, window_seconds=60)
        # 查询限流器: {client_id: RateLimiter}
        self.query_limiters: Dict[str, RateLimiter] = {}
        # 清理任务
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_limiters())
    
    async def _cleanup_expired_limiters(self):
        """定期清理过期的限流器"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                current_time = time.time()
                
                # 清理客户端限流器
                expired_clients = []
                for client_id, limiter in self.client_limiters.items():
                    if not limiter.requests or limiter.requests[-1] < current_time - 3600:  # 1小时无请求
                        expired_clients.append(client_id)
                
                for client_id in expired_clients:
                    del self.client_limiters[client_id]
                
                # 清理查询限流器
                expired_query_clients = []
                for client_id, limiter in self.query_limiters.items():
                    if not limiter.requests or limiter.requests[-1] < current_time - 3600:  # 1小时无请求
                        expired_query_clients.append(client_id)
                
                for client_id in expired_query_clients:
                    del self.query_limiters[client_id]
                
                if expired_clients or expired_query_clients:
                    api_logger.info(f"清理过期限流器: 客户端={len(expired_clients)}, 查询={len(expired_query_clients)}")
                    
            except Exception as e:
                api_logger.error(f"清理限流器异常: {str(e)}")
    
    def get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用X-Forwarded-For头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # 使用X-Real-IP头
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def check_rate_limit(self, client_id: str, limit_type: str = "general") -> Tuple[bool, Dict[str, any]]:
        """检查限流"""
        # 检查全局限流
        global_allowed, global_info = self.global_limiter.is_allowed()
        if not global_allowed:
            api_logger.warning(f"全局限流触发: {client_id}")
            return False, global_info
        
        # 根据限流类型选择限流器
        if limit_type == "query":
            # 查询限流: 每分钟10次
            if client_id not in self.query_limiters:
                self.query_limiters[client_id] = RateLimiter(max_requests=10, window_seconds=60)
            limiter = self.query_limiters[client_id]
        else:
            # 通用限流: 每分钟100次
            if client_id not in self.client_limiters:
                self.client_limiters[client_id] = RateLimiter(max_requests=100, window_seconds=60)
            limiter = self.client_limiters[client_id]
        
        allowed, info = limiter.is_allowed()
        
        if not allowed:
            api_logger.warning(f"客户端限流触发: {client_id} - {limit_type}")
        
        return allowed, info
    
    def get_rate_limit_info(self, client_id: str, limit_type: str = "general") -> Dict[str, any]:
        """获取限流信息"""
        if limit_type == "query":
            if client_id not in self.query_limiters:
                return {
                    "limit": 10,
                    "remaining": 10,
                    "reset_time": time.time() + 60
                }
            limiter = self.query_limiters[client_id]
        else:
            if client_id not in self.client_limiters:
                return {
                    "limit": 100,
                    "remaining": 100,
                    "reset_time": time.time() + 60
                }
            limiter = self.client_limiters[client_id]
        
        current_time = time.time()
        
        # 清理过期请求
        while limiter.requests and limiter.requests[0] <= current_time - limiter.window_seconds:
            limiter.requests.popleft()
        
        return {
            "limit": limiter.max_requests,
            "remaining": max(0, limiter.max_requests - len(limiter.requests)),
            "reset_time": current_time + limiter.window_seconds
        }

# 全局限流器实例
_rate_limiter = APIRateLimiter()

def get_rate_limiter() -> APIRateLimiter:
    """获取限流器实例"""
    return _rate_limiter

async def rate_limit_dependency(request: Request, limit_type: str = "general"):
    """限流依赖注入函数"""
    rate_limiter = get_rate_limiter()
    client_id = rate_limiter.get_client_id(request)
    
    # 检查限流
    allowed, info = rate_limiter.check_rate_limit(client_id, limit_type)
    
    if not allowed:
        # 添加限流响应头
        headers = {
            "X-RateLimit-Limit": str(info["limit"]),
            "X-RateLimit-Remaining": str(info["remaining"]),
            "X-RateLimit-Reset": str(int(info["reset_time"])),
            "Retry-After": str(info["retry_after"])
        }
        
        raise HTTPException(
            status_code=429,
            detail="请求频率超出限制",
            headers=headers
        )
    
    # 添加限流信息到响应头
    request.state.rate_limit_info = {
        "X-RateLimit-Limit": str(info["limit"]),
        "X-RateLimit-Remaining": str(info["remaining"]),
        "X-RateLimit-Reset": str(int(info["reset_time"]))
    }

async def query_rate_limit_dependency(request: Request):
    """查询限流依赖注入函数"""
    return await rate_limit_dependency(request, "query")