#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接复用器自测脚本（安全、快速）
- 验证 acquire/close 归还行为
- 验证 PRAGMA 是否生效（WAL/busy_timeout）
- 模拟并发读写，观察是否有明显的 database is locked

运行：
    python -m test.test_connection_pool
"""
import os
import time
import threading
import sqlite3
from db.connection_manager import get_connection_manager, get_pool_stats

DB1 = 'db/shipment_records.db'
DB2 = 'db/task_queue.db'


def quick_check(db_path):
    print(f"\n[TEST] Quick check on {db_path}")
    mgr = get_connection_manager(db_path)
    s1 = get_pool_stats(db_path)
    print("[POOL] before:", s1)

    # 借出多个连接
    conns = [mgr.acquire() for _ in range(3)]
    print("[POOL] after acquire:", get_pool_stats(db_path))

    # 简单查询
    for c in conns:
        cur = c.cursor()
        try:
            cur.execute("SELECT 1")
            cur.fetchone()
        finally:
            c.close()  # 归还

    print("[POOL] after release:", get_pool_stats(db_path))


def concurrent_rw(db_path, write_count=10, read_threads=3):
    print(f"\n[TEST] Concurrent RW on {db_path}")
    mgr = get_connection_manager(db_path)

    stop = threading.Event()
    errors = []

    def reader(tid):
        while not stop.is_set():
            conn = mgr.acquire()
            try:
                cur = conn.cursor()
                cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
                cur.fetchall()
            except Exception as e:
                errors.append((f"reader-{tid}", str(e)))
            finally:
                conn.close()
            time.sleep(0.01)

    def writer():
        for i in range(write_count):
            conn = mgr.acquire()
            try:
                cur = conn.cursor()
                # 超短事务：写入测试表（若不存在则创建）
                cur.execute("CREATE TABLE IF NOT EXISTS _pool_test (id INTEGER PRIMARY KEY AUTOINCREMENT, ts TEXT)")
                cur.execute("INSERT INTO _pool_test (ts) VALUES (datetime('now'))")
            except Exception as e:
                errors.append(("writer", str(e)))
            finally:
                conn.close()
            time.sleep(0.01)

    threads = [threading.Thread(target=reader, args=(i,), daemon=True) for i in range(read_threads)]
    for t in threads:
        t.start()

    w = threading.Thread(target=writer, daemon=True)
    w.start()

    w.join()
    stop.set()
    for t in threads:
        t.join(timeout=0.2)

    print("[POOL] stats:", get_pool_stats(db_path))
    if errors:
        print("[WARN] errors:", errors[:5], f"... total={len(errors)}")
    else:
        print("[OK] no errors observed")


def main():
    for db in (DB1, DB2):
        quick_check(db)
        concurrent_rw(db)

if __name__ == '__main__':
    main()

