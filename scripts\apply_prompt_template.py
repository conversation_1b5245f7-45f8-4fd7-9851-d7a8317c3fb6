#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一次性脚本：
1) 将当前 config/prompts.json 保存为历史（带标签）
2) 用新的文本分析模板（严格 YYYY-MM-DD 日期）更新 prompts.json

运行：python scripts/apply_prompt_template.py
"""
import json, os, sys
from ai.prompt_history import snapshot_current
from ai.prompt_config import reload_prompt_config

NEW_TEMPLATE = """
你是一个专业的物流追踪信息抽取专家。任务：从提供的结构化 HTML 片段中抽取与船期/物流相关的日期与上下文信息，并输出严格规范的 JSON。

输入为结构化 HTML（包含表格、行列、标题等上下文标记）：
{simplified_html}

抽取与规范化规则（务必遵守）：
1) 仅输出一个 JSON 对象，不得包含任何多余文本、解释或 Markdown 代码块标记（不要使用 ```）。
2) 所有日期字段必须统一输出为 YYYY-MM-DD（ISO 8601 日期，年-月-日，必须两位补零），例如：2025-01-02。
   - 需要对原文可能出现的各种格式进行解析与标准化（如 24/08/2025、14.08.2025、2025.08.14、Aug 24, 2025、24 Aug 2025 等）。
   - 如果无法确定合法日期（如信息缺失或模糊），顶层字段写 null；列表中的该项请直接跳过，不输出不完整的项。
   - 如果原始文本带有时分秒或时区，请只保留日期（忽略时间部分），并在 original_format 字段中保留原始格式供参考。
3) 顶层字段含义与选择：
   - estimated_arrival_time：最重要的“预计到港时间（ETA）”，优先选择最终目的港的 ETA；如果有多个 ETA，选择时间最新且更接近最终 POD 的那个；若完全缺失则为 null。
   - estimated_arrival_port：与上述 ETA 对应的港口名称，保持原文规范（如 Shanghai CN、Los Angeles CA 等）；若未知则为 null。
4) dates 列表规范：
   - 必须按 date（YYYY-MM-DD）降序排列（最新在前）。
   - 同一事件（date + type + location）重复出现时去重，仅保留一条。
   - 字段说明与取值约定：
     - date：标准化后的 YYYY-MM-DD（必填）
     - original_format：原始日期写法或原文片段（可填原文，如 24/08/2025 或 “Tracking results provided by MSC on 14.08.2025 ...”）
     - type：事件类型，尽量标准化为以下常见集合之一（大小写保持如下）：
       ["POD_ETA","ETA","ATA","ETD","ATD","Latest move","Tracking results time","Full Transshipment Loaded","Full Transshipment Discharged","Full Transshipment Positioned In","Full Transshipment Positioned Out","Export Loaded on Vessel","Export received at CY","Empty to Shipper","Transshipment Loaded","Transshipment Discharged","Price Calculation Date"]
       - 若原文为“Estimated Time of Arrival”，type 可用 "POD_ETA" 或 "ETA"（若明显指向目的港/POD则优先 "POD_ETA"）
     - location：地点（保持原文，如 “Shanghai CN”、“Rodman, PA”、“Moin, CR”）
     - description：人类可读的事件描述（如 “Estimated Time of Arrival”、“Export Loaded on Vessel”）
     - status：尽量归一到 ["estimated","actual","completed"] 三类之一；未知可为空字符串 ""。
     - vessel_info：船名/航次/IMO 等信息（如有；无则 ""）
     - context：可包含该行/相邻单元格的整合说明，帮助理解该事件（如包含表头信息、附加说明）
5) 语言与同义词映射要求：
   - 必须识别中英混合与专业缩写（ETA/ATA/ETD/POD 等），以及中文同义词（如 “预计到港时间”≈ETA，“实际到港时间”≈ATA 等）。
   - 若含 “Estimated Time of Arrival/ETA/到港/抵达” 等词汇，优先作为 ETA 候选；结合地点与页面上下文判定是否为最终 POD。
6) 稳健性要求：
   - 输入中可能包含多个表格或说明段，需结合表头、列名、相邻单元格来判定字段语义。
   - 若同一天有多条相关事件，均可保留为不同的列表项（只要 type/description 不同）。
7) 输出格式（务必严格遵守，键名固定）：
{
  "estimated_arrival_time": "YYYY-MM-DD 或 null",
  "estimated_arrival_port": "字符串 或 null",
  "dates": [
    {
      "date": "YYYY-MM-DD",
      "original_format": "原始日期文本或片段",
      "type": "标准化后的事件类型",
      "location": "地点字符串",
      "description": "事件描述",
      "status": "estimated/actual/completed 或空字符串",
      "vessel_info": "船只信息或空字符串",
      "context": "补充上下文信息或空字符串"
    }
  ]
}

请对输入进行全面、稳健的解析与标准化，仅输出上面的 JSON 对象，不得包含任何额外文本。
"""

CURRENT = 'config/prompts.json'

def main():
    # 1) 保存当前为历史
    snapshot_current(label='pre-apply YYYYMMDD 当前生效模板')

    # 2) 更新 prompts.json
    cfg = {}
    if os.path.exists(CURRENT):
        with open(CURRENT, 'r', encoding='utf-8') as f:
            try:
                cfg = json.load(f)
            except Exception:
                cfg = {}
    cfg.setdefault('text_analyzer', {})['prompt_template'] = NEW_TEMPLATE
    os.makedirs(os.path.dirname(CURRENT), exist_ok=True)
    with open(CURRENT, 'w', encoding='utf-8') as f:
        json.dump(cfg, f, ensure_ascii=False, indent=2)

    # 3) 热加载
    reload_prompt_config()
    print('[SUCCESS] 已保存历史并更新为新模板')

if __name__ == '__main__':
    main()

