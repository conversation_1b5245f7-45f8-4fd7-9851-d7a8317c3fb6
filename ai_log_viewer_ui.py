import sys
import sqlite3
from datetime import datetime
from typing import List, Dict, Any

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QPushButton,
    QComboBox, QDateEdit, QLineEdit, QMessageBox, QFrame, QGroupBox,
    QSplitter, QTextEdit, QProgressBar
)
from PySide6.QtCore import Qt, QDate, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QIcon

from db_logger import AICallLogger


class AILogLoadThread(QThread):
    """AI日志加载线程"""
    logs_loaded = Signal(list)
    error_occurred = Signal(str)
    
    def __init__(self, logger: AICallLogger, filters: Dict[str, Any] = None):
        super().__init__()
        self.logger = logger
        self.filters = filters or {}
    
    def run(self):
        try:
            logs = self.logger.get_all_logs(
                start_date=self.filters.get('start_date'),
                end_date=self.filters.get('end_date'),
                model_name=self.filters.get('model_name'),
                limit=self.filters.get('limit', 1000)
            )
            self.logs_loaded.emit(logs)
        except Exception as e:
            self.error_occurred.emit(str(e))


class AILogViewerWindow(QMainWindow):
    """AI调用日志查看器窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = AICallLogger()
        self.load_thread = None
        self.current_logs = []
        
        self.setWindowTitle("AI调用日志查看器")
        self.setGeometry(100, 100, 1200, 800)
        
        self.setup_ui()
        self.setup_connections()
        self.load_logs()
    
    def setup_ui(self):
        """设置UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 紧凑设计
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(3)
        
        # 工具栏 - 紧凑设计
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(5)
        
        # 日期范围
        toolbar_layout.addWidget(QLabel("从:"))
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setMaximumWidth(100)
        toolbar_layout.addWidget(self.start_date_edit)
        
        toolbar_layout.addWidget(QLabel("到:"))
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setMaximumWidth(100)
        toolbar_layout.addWidget(self.end_date_edit)
        
        # 模型筛选
        toolbar_layout.addWidget(QLabel("模型:"))
        self.model_combo = QComboBox()
        self.load_model_options()
        self.model_combo.setMaximumWidth(120)
        toolbar_layout.addWidget(self.model_combo)
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索...")
        self.search_edit.setMaximumWidth(150)
        toolbar_layout.addWidget(self.search_edit)
        
        # 按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setMaximumWidth(50)
        toolbar_layout.addWidget(self.refresh_btn)
        
        self.export_btn = QPushButton("导出")
        self.export_btn.setMaximumWidth(50)
        toolbar_layout.addWidget(self.export_btn)
        
        toolbar_layout.addStretch()
        main_layout.addLayout(toolbar_layout)
        
        # 统计信息 - 紧凑显示
        self.stats_label = QLabel("记录: 0")
        self.stats_label.setMaximumHeight(20)
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                padding: 2px;
            }
        """)
        main_layout.addWidget(self.stats_label)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 日志表格 - 紧凑设计
        self.logs_table = QTableWidget()
        self.logs_table.setColumnCount(9)
        self.logs_table.setHorizontalHeaderLabels([
            "时间", "模型", "类型", "提示Token", "完成Token", "总Token", "耗时(秒)", "状态", "提示词预览"
        ])
        
        # 设置表格样式 - 紧凑版本
        self.logs_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ecf0f1;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 6px;
                border: none;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        
        self.logs_table.setAlternatingRowColors(True)
        self.logs_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.logs_table.horizontalHeader().setStretchLastSection(True)
        self.logs_table.verticalHeader().setDefaultSectionSize(22)  # 紧凑行高
        
        # 设置列宽 - 更紧凑
        header = self.logs_table.horizontalHeader()
        header.resizeSection(0, 130)  # 时间
        header.resizeSection(1, 100)  # 模型
        header.resizeSection(2, 60)   # 类型
        header.resizeSection(3, 70)   # 提示Token
        header.resizeSection(4, 70)   # 完成Token
        header.resizeSection(5, 70)   # 总Token
        header.resizeSection(6, 60)   # 耗时
        header.resizeSection(7, 60)   # 状态
        
        splitter.addWidget(self.logs_table)
        
        # 详情面板 - 紧凑设计
        detail_widget = QWidget()
        detail_layout = QVBoxLayout(detail_widget)
        detail_layout.setContentsMargins(2, 2, 2, 2)
        detail_layout.setSpacing(2)
        
        detail_title = QLabel("详情")
        detail_title.setMaximumHeight(18)
        detail_title.setStyleSheet("font-size: 11px; font-weight: bold; padding: 2px;")
        detail_layout.addWidget(detail_title)
        
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                font-family: 'Consolas', monospace;
                font-size: 10px;
                padding: 3px;
            }
        """)
        detail_layout.addWidget(self.detail_text)
        
        splitter.addWidget(detail_widget)
        splitter.setSizes([700, 300])
        
        main_layout.addWidget(splitter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def setup_connections(self):
        """设置信号连接"""
        self.refresh_btn.clicked.connect(self.load_logs)
        self.export_btn.clicked.connect(self.export_logs)
        self.logs_table.itemSelectionChanged.connect(self.show_log_detail)
        self.search_edit.textChanged.connect(self.filter_logs)
    
    def load_logs(self):
        """加载日志数据"""
        if self.load_thread and self.load_thread.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        filters = {
            'start_date': self.start_date_edit.date().toPython(),
            'end_date': self.end_date_edit.date().toPython(),
            'model_name': self.model_combo.currentData() or None,
            'limit': 1000
        }
        
        self.load_thread = AILogLoadThread(self.logger, filters)
        self.load_thread.logs_loaded.connect(self.on_logs_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()
    
    def on_logs_loaded(self, logs: List[Dict]):
        """日志加载完成"""
        self.current_logs = logs
        self.populate_table(logs)
        self.update_stats(logs)
        self.progress_bar.setVisible(False)
    
    def on_load_error(self, error: str):
        """日志加载错误"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "错误", f"加载日志失败: {error}")
    
    def populate_table(self, logs: List[Dict]):
        """填充表格数据"""
        self.logs_table.setRowCount(len(logs))
        
        for row, log in enumerate(logs):
            # 时间
            time_item = QTableWidgetItem(log.get('timestamp', ''))
            self.logs_table.setItem(row, 0, time_item)
            
            # 模型
            model_item = QTableWidgetItem(log.get('model_name', ''))
            self.logs_table.setItem(row, 1, model_item)
            
            # 类型
            call_type = "视觉" if log.get('image_count', 0) > 0 else "文本"
            type_item = QTableWidgetItem(call_type)
            self.logs_table.setItem(row, 2, type_item)
            
            # 提示Token
            prompt_tokens = log.get('prompt_tokens', 0)
            prompt_token_item = QTableWidgetItem(str(prompt_tokens))
            self.logs_table.setItem(row, 3, prompt_token_item)
            
            # 完成Token
            completion_tokens = log.get('completion_tokens', 0)
            completion_token_item = QTableWidgetItem(str(completion_tokens))
            self.logs_table.setItem(row, 4, completion_token_item)
            
            # 总Token
            total_tokens = log.get('total_tokens', prompt_tokens + completion_tokens)
            total_token_item = QTableWidgetItem(str(total_tokens))
            self.logs_table.setItem(row, 5, total_token_item)
            
            # 耗时
            duration = log.get('duration_seconds', 0)
            duration_item = QTableWidgetItem(f"{duration:.2f}")
            self.logs_table.setItem(row, 6, duration_item)
            
            # 状态
            status = "成功" if log.get('success') else "失败"
            status_item = QTableWidgetItem(status)
            if not log.get('success'):
                status_item.setBackground(Qt.GlobalColor.red)
                status_item.setForeground(Qt.GlobalColor.white)
            self.logs_table.setItem(row, 7, status_item)
            
            # 提示词预览
            prompt = log.get('prompt', '')
            preview = prompt[:50] + "..." if len(prompt) > 50 else prompt
            prompt_item = QTableWidgetItem(preview)
            self.logs_table.setItem(row, 8, prompt_item)
    
    def update_stats(self, logs: List[Dict]):
        """更新统计信息"""
        total_count = len(logs)
        success_count = sum(1 for log in logs if log.get('success'))
        total_prompt_tokens = sum(log.get('prompt_tokens', 0) for log in logs)
        total_completion_tokens = sum(log.get('completion_tokens', 0) for log in logs)
        total_tokens = total_prompt_tokens + total_completion_tokens
        
        stats_text = f"总计: {total_count} 条记录 | 成功: {success_count} 条 | 提示Token: {total_prompt_tokens:,} | 完成Token: {total_completion_tokens:,} | 总Token: {total_tokens:,}"
        self.stats_label.setText(stats_text)
    
    def show_log_detail(self):
        """显示日志详情"""
        current_row = self.logs_table.currentRow()
        if current_row < 0 or current_row >= len(self.current_logs):
            return
        
        log = self.current_logs[current_row]
        
        detail_text = f"""时间: {log.get('timestamp', 'N/A')}
模型: {log.get('model_name', 'N/A')}
成功: {'是' if log.get('success') else '否'}
提示Token: {log.get('prompt_tokens', 0):,}
完成Token: {log.get('completion_tokens', 0):,}
总Token: {log.get('total_tokens', 0):,}
耗时: {log.get('duration_seconds', 0):.2f} 秒
图片数量: {log.get('image_count', 0)}

提示词:
{log.get('prompt', 'N/A')}

响应:
{log.get('response', 'N/A')}

错误信息:
{log.get('error_message', '无') if not log.get('success') else '无'}"""
        
        self.detail_text.setPlainText(detail_text)
    
    def filter_logs(self):
        """过滤日志"""
        search_text = self.search_edit.text().lower()
        if not search_text:
            self.populate_table(self.current_logs)
            return
        
        filtered_logs = []
        for log in self.current_logs:
            prompt = log.get('prompt', '').lower()
            response = log.get('response', '').lower()
            if search_text in prompt or search_text in response:
                filtered_logs.append(log)
        
        self.populate_table(filtered_logs)
        self.update_stats(filtered_logs)
    
    def load_model_options(self):
        """动态加载模型选项"""
        try:
            # 添加"全部"选项
            self.model_combo.addItem("全部", "")
            
            # 从数据库获取不重复的模型列表
            models = self.logger.get_distinct_models()
            
            # 添加每个模型到下拉框
            for model in models:
                self.model_combo.addItem(model, model)
                
        except Exception as e:
            print(f"加载模型选项失败: {str(e)}")
            # 如果加载失败，添加默认选项
            self.model_combo.addItem("gemini-1.5-flash", "gemini-1.5-flash")
            self.model_combo.addItem("gemini-1.5-pro", "gemini-1.5-pro")
    
    def export_logs(self):
        """导出日志"""
        QMessageBox.information(self, "导出", "导出功能正在开发中...")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("AI Log Viewer")
    app.setApplicationVersion("1.0.0")
    
    # 设置全局字体
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    
    # 创建并显示窗口
    window = AILogViewerWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())