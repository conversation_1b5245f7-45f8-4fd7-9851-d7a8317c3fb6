#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI模块功能
验证从task_executor.py中拆分出来的AI相关功能是否正常工作
"""

import os
from ai import get_ai_client, get_ai_action, analyze_shipment_dates
from ai.text_analyzer import simplify_html_for_logistics_ai

def test_ai_client():
    """测试AI客户端初始化"""
    print("🔧 测试AI客户端初始化...")
    try:
        client = get_ai_client()
        print(f"✅ AI客户端初始化成功: {type(client)}")
        return True
    except Exception as e:
        print(f"❌ AI客户端初始化失败: {e}")
        return False

def test_html_simplification():
    """测试HTML简化功能"""
    print("\n🔧 测试HTML简化功能...")
    try:
        # 创建测试HTML内容
        test_html = """
        <html>
        <head><title>Test</title></head>
        <body>
            <div class="header">Header</div>
            <table class="logistics-table">
                <tr>
                    <th>Date</th>
                    <th>Location</th>
                    <th>Status</th>
                </tr>
                <tr>
                    <td x-text="2024-01-15">2024-01-15</td>
                    <td>Shanghai CN</td>
                    <td>Departed</td>
                </tr>
            </table>
            <script>console.log('test');</script>
        </body>
        </html>
        """
        
        simplified = simplify_html_for_logistics_ai(test_html)
        print(f"✅ HTML简化成功")
        print(f"📊 原长度: {len(test_html)}, 简化后: {len(simplified)}")
        print(f"📝 简化结果预览: {simplified[:200]}...")
        return True
    except Exception as e:
        print(f"❌ HTML简化失败: {e}")
        return False

def test_ai_functions_import():
    """测试AI函数导入"""
    print("\n🔧 测试AI函数导入...")
    try:
        # 检查函数是否可调用
        assert callable(get_ai_action), "get_ai_action不可调用"
        assert callable(analyze_shipment_dates), "analyze_shipment_dates不可调用"
        assert callable(simplify_html_for_logistics_ai), "simplify_html_for_logistics_ai不可调用"
        
        print("✅ 所有AI函数导入成功")
        print(f"📋 可用函数:")
        print(f"   - get_ai_action: {get_ai_action.__doc__[:50] if get_ai_action.__doc__ else 'AI视觉分析'}...")
        print(f"   - analyze_shipment_dates: {analyze_shipment_dates.__doc__[:50] if analyze_shipment_dates.__doc__ else 'AI文本分析'}...")
        print(f"   - simplify_html_for_logistics_ai: {simplify_html_for_logistics_ai.__doc__[:50] if simplify_html_for_logistics_ai.__doc__ else 'HTML简化'}...")
        return True
    except Exception as e:
        print(f"❌ AI函数导入测试失败: {e}")
        return False

def test_ai_module_structure():
    """测试AI模块结构"""
    print("\n🔧 测试AI模块结构...")
    try:
        ai_dir = "d:\\container-helper\\ai"
        expected_files = [
            "__init__.py",
            "client.py", 
            "vision_analyzer.py",
            "text_analyzer.py"
        ]
        
        missing_files = []
        for file in expected_files:
            file_path = os.path.join(ai_dir, file)
            if not os.path.exists(file_path):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ 缺少文件: {missing_files}")
            return False
        
        print("✅ AI模块结构完整")
        print(f"📁 AI模块文件:")
        for file in expected_files:
            file_path = os.path.join(ai_dir, file)
            size = os.path.getsize(file_path)
            print(f"   - {file}: {size} bytes")
        return True
    except Exception as e:
        print(f"❌ AI模块结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试AI模块功能...")
    print("=" * 50)
    
    tests = [
        ("AI模块结构", test_ai_module_structure),
        ("AI客户端初始化", test_ai_client),
        ("AI函数导入", test_ai_functions_import),
        ("HTML简化功能", test_html_simplification),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！AI模块拆分成功！")
    else:
        print("⚠️ 部分测试失败，请检查AI模块配置")

if __name__ == "__main__":
    main()