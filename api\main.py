#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
船期查询API服务主应用
提供RESTful API和WebSocket接口用于船期查询任务管理
"""

import sys
import os
import time
from pathlib import Path
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from api.routers import shipment, task, auth, carriers, user_tasks, system
from api.routers import files as files_router
from api.websocket.manager import WebSocketManager
from api.utils.exceptions import setup_exception_handlers
from api.utils.logger import api_logger

# 全局WebSocket管理器
websocket_manager = WebSocketManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    api_logger.info("启动船期查询API服务")
    
    # 设置WebSocket管理器
    app.state.websocket_manager = websocket_manager
    
    yield
    
    # 关闭时的清理
    api_logger.info("关闭船期查询API服务")
    await websocket_manager.disconnect_all()

# 创建FastAPI应用
app = FastAPI(
    title="船期查询API",
    description="提供船期查询任务管理的RESTful API和WebSocket接口",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 设置异常处理器
setup_exception_handlers(app)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/v1", tags=["用户认证"])
app.include_router(carriers.router, prefix="/api/v1", tags=["承运人校验"])
app.include_router(user_tasks.router, prefix="/api/v1", tags=["用户任务"])
app.include_router(shipment.router, prefix="/api/v1", tags=["船期查询"])
app.include_router(task.router, prefix="/api/v1", tags=["任务管理"])
app.include_router(files_router.router, prefix="/api", tags=["文件服务"])  # 提供 /api/files
app.include_router(files_router.router, prefix="/api/v1", tags=["文件服务"])  # 兼容 /api/v1/files
app.include_router(system.router, prefix="/api/v1", tags=["系统状态"])  # 系统/处理器状态

# 挂载前端静态资源（/web 目录）
web_dir = Path(__file__).parent.parent / "web"
if web_dir.exists():
    app.mount("/", StaticFiles(directory=str(web_dir), html=True), name="web")

# 提供 manifest.json（如果存在）
manifest_path = web_dir / "manifest.json" if web_dir.exists() else None
if manifest_path and manifest_path.exists():
    @app.get("/manifest.json")
    async def get_manifest():
        return FileResponse(str(manifest_path))

# 注册WebSocket路由
websocket_manager.setup_routes(app)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "船期查询API服务",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "shipment-query-api",
        "version": "1.0.0"
    }

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    
    # 记录请求
    api_logger.info(
        f"请求开始: {request.method} {request.url}",
        extra={
            "method": request.method,
            "url": str(request.url),
            "client_ip": request.client.host if request.client else None
        }
    )
    
    response = await call_next(request)
    
    # 记录响应
    process_time = time.time() - start_time
    api_logger.info(
        f"请求完成: {request.method} {request.url} - {response.status_code} ({process_time:.3f}s)",
        extra={
            "method": request.method,
            "url": str(request.url),
            "status_code": response.status_code,
            "process_time": process_time
        }
    )
    
    return response

if __name__ == "__main__":
    import uvicorn
    import time
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )