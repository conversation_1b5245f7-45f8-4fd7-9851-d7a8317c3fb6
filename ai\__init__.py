#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块 - 统一的AI服务接口

这个模块提供了所有AI相关功能的统一接口，包括：
- 视觉AI分析（截图分析和操作决策）
- 文本AI分析（HTML内容分析和物流信息提取）
- AI客户端管理
"""

# 导入主要功能
from .client import get_ai_client, reset_ai_client, AIClient
from .vision_analyzer import get_ai_action
from .text_analyzer import analyze_shipment_dates, simplify_html_for_logistics_ai

# 版本信息
__version__ = "1.0.0"
__author__ = "Container Helper Team"

# 公开的API
__all__ = [
    # 客户端管理
    'get_ai_client',
    'reset_ai_client', 
    'AIClient',
    
    # 视觉分析
    'get_ai_action',
    
    # 文本分析
    'analyze_shipment_dates',
    'simplify_html_for_logistics_ai',
]

# 模块级别的便捷函数
def get_vision_analyzer():
    """获取视觉分析器的便捷函数"""
    from . import vision_analyzer
    return vision_analyzer

def get_text_analyzer():
    """获取文本分析器的便捷函数"""
    from . import text_analyzer
    return text_analyzer

# 模块初始化
print("AI模块已加载 - 版本:", __version__)