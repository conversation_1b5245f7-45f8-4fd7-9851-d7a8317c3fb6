#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除干扰东八区时间的数据库触发器
该触发器会自动覆盖我们设置的东八区时间
"""

import sqlite3
import os

def remove_problematic_trigger():
    """删除会覆盖东八区时间的触发器"""
    db_path = 'db/shipment_records.db'
    
    if not os.path.exists(db_path):
        print(f"[ERROR] 数据库文件不存在: {db_path}")
        return False
    
    print(f"[INFO] 正在连接数据库: {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查触发器是否存在
        print("[INFO] 检查现有触发器...")
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='trigger' AND name='update_shipment_records_timestamp'
        """)
        trigger = cursor.fetchone()
        
        if trigger:
            print(f"[FOUND] 找到问题触发器: {trigger[0]}")
            print("[INFO] 该触发器会自动覆盖我们设置的东八区时间")
            
            # 删除触发器
            print("[ACTION] 删除触发器...")
            cursor.execute("DROP TRIGGER IF EXISTS update_shipment_records_timestamp")
            conn.commit()
            
            print("[SUCCESS] 触发器已成功删除！")
            print("[INFO] 现在 updated_at 字段将完全由代码控制，使用东八区时间")
            
        else:
            print("[INFO] 未找到问题触发器，可能已经被删除")
        
        # 验证删除结果
        print("\\n[VERIFY] 验证删除结果...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
        remaining_triggers = cursor.fetchall()
        
        if remaining_triggers:
            print(f"[INFO] 剩余触发器: {[t[0] for t in remaining_triggers]}")
        else:
            print("[INFO] 当前没有任何触发器")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 删除触发器失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
        print(f"[INFO] 数据库连接已关闭")

def verify_current_trigger_behavior():
    """验证当前触发器行为"""
    db_path = 'db/shipment_records.db'
    
    print(f"\\n[TEST] 测试当前数据库行为...")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 创建测试记录
        test_time = "2025-08-05T22:00:00.000000+08:00"
        cursor.execute("""
            INSERT INTO shipment_records 
            (bill_of_lading, status, created_by, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """, ("TRIGGER_TEST_001", "测试", "trigger_test", test_time, test_time))
        
        record_id = cursor.lastrowid
        print(f"[TEST] 创建测试记录 ID: {record_id}")
        
        # 更新记录，显式设置东八区时间
        new_update_time = "2025-08-05T22:05:00.000000+08:00"
        cursor.execute("""
            UPDATE shipment_records 
            SET status = ?, updated_at = ?
            WHERE id = ?
        """, ("更新测试", new_update_time, record_id))
        
        # 检查 updated_at 是否被覆盖
        cursor.execute("SELECT updated_at FROM shipment_records WHERE id = ?", (record_id,))
        actual_updated_at = cursor.fetchone()[0]
        
        print(f"[TEST] 设置的更新时间: {new_update_time}")
        print(f"[TEST] 实际存储时间: {actual_updated_at}")
        
        if actual_updated_at == new_update_time:
            print("[SUCCESS] 东八区时间设置成功，没有被触发器覆盖！")
            result = True
        else:
            print("[WARNING] 东八区时间被覆盖，触发器仍在工作")
            result = False
        
        # 清理测试数据
        cursor.execute("DELETE FROM shipment_records WHERE id = ?", (record_id,))
        conn.commit()
        print(f"[CLEANUP] 测试记录已删除")
        
        return result
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("删除干扰东八区时间的数据库触发器")
    print("=" * 60)
    
    # 删除触发器
    success = remove_problematic_trigger()
    
    if success:
        # 测试验证
        if verify_current_trigger_behavior():
            print("\\n" + "=" * 60)
            print("[EXCELLENT] 触发器删除成功！")
            print("[INFO] 现在 shipment_records.updated_at 将完全使用东八区时间")
            print("[INFO] 不会再被系统时间覆盖")
            print("=" * 60)
        else:
            print("\\n" + "=" * 60)
            print("[WARNING] 触发器可能仍在工作，请检查")
            print("=" * 60)
    else:
        print("\\n" + "=" * 60)
        print("[ERROR] 触发器删除失败")
        print("=" * 60)

if __name__ == "__main__":
    main()