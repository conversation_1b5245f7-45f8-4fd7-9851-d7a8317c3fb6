# 前端设计规范

> 本文档记录项目的前端视觉设计标准，确保界面一致性和开发效率。  
> 最后更新：2025-01-06

## 🎯 基础设置

### 根字号与比例
```css
html { font-size: 12px; }
```
- **基准字号**：12px（相当于浏览器100%缩放下的75%视觉效果）
- **优势**：适配不同屏幕密度，保持舒适的信息密度
- **使用建议**：优先使用 Tailwind 的 rem 类（text-sm、p-4 等），会自动随基线同比例缩放

### 字体族
```css
font-family: 'Inter', 'Helvetica Neue', 'Arial', sans-serif;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
```
- **主字体**：Inter（需在 HTML head 中引入 Google Fonts）
- **字体渲染**：启用抗锯齿平滑

### 图标系统
```css
.lucide {
    width: 1.125rem;  /* 18px at 16px base; scales to 13.5px at 12px base */
    height: 1.125rem;
}
```
- **图标库**：Lucide Icons
- **基准大小**：1.125rem（与根字号联动）
- **CDN引入**：`<script src="https://unpkg.com/lucide@latest"></script>`

## 🌈 主题系统

### 色彩方案
- **浅色模式**：`bg-slate-100 text-slate-800`
- **深色模式**：`bg-slate-900 text-slate-300`
- **切换方式**：在 html 标签添加/移除 `dark` 类

### 主题设置CSS
```css
html.dark {
    color-scheme: dark;
}
```

### 通用文本颜色
- **主要文本**：`text-slate-900 dark:text-slate-100`
- **次要文本**：`text-slate-700 dark:text-slate-200`
- **辅助文本**：`text-slate-500 dark:text-slate-400`
- **占位符**：`placeholder-slate-400`

## 📦 布局容器

### 页面布局
```html
<div class="flex h-screen">
    <!-- 侧边栏：w-64 -->
    <!-- 主内容区：flex-1 -->
</div>
```

### 侧边栏规范
- **宽度**：`w-64` (256px)
- **背景样式**：`bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm`
- **边框**：`border-r border-slate-200/80 dark:border-slate-700/60`

### 主内容区间距
- **推荐内边距**：`p-6 md:p-8 lg:p-10`
- **响应式调整**：移动端适当减小间距

## 🎴 卡片组件

### 标准卡片
```html
<div class="bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm border border-slate-200/80 dark:border-slate-700/60 rounded-2xl shadow-md card-glow">
    <!-- 卡片内容 -->
</div>
```

### 卡片光晕效果
```css
.card-glow {
    position: relative;
    transition: all 0.3s ease;
}
.card-glow:before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    border-radius: 1rem;
    border: 1px solid transparent;
    background: linear-gradient(120deg, #818cf8, #3b82f6) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.card-glow:hover:before {
    opacity: 0.6;
}
```

### 卡片内边距
- **标准**：`p-4 md:p-6`
- **紧凑**：`p-3 md:p-4`
- **宽松**：`p-6 md:p-8`

## 🔘 按钮组件

### 按钮基础类
```html
<!-- 通用基础结构 -->
<button class="inline-flex items-center justify-center h-10 px-4 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed">
    按钮文本
</button>
```

### 按钮变体

#### 主要按钮
```html
<button class="[基础类] bg-blue-600 hover:bg-blue-700 text-white shadow-sm">
    主要操作
</button>
```

#### 次要按钮
```html
<button class="[基础类] bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200 border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/70">
    次要操作
</button>
```

#### 幽灵按钮
```html
<button class="[基础类] bg-transparent text-slate-600 dark:text-slate-300 hover:bg-slate-100/60 dark:hover:bg-slate-800/60">
    幽灵按钮
</button>
```

#### 危险按钮
```html
<button class="[基础类] bg-rose-600 hover:bg-rose-700 text-white">
    删除操作
</button>
```

#### 图标按钮
```html
<button class="h-10 w-10 inline-flex items-center justify-center rounded-lg bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/70">
    <i data-lucide="settings"></i>
</button>
```

### 按钮尺寸变体
- **小号**：`h-8 px-3 text-xs`
- **标准**：`h-10 px-4 text-sm`（默认）
- **大号**：`h-12 px-6 text-base`

## 📝 表单控件

### 输入框
```html
<input class="h-10 px-3 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg placeholder-slate-400 text-slate-700 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50" 
       placeholder="请输入内容">
```

### 选择器
```html
<select class="h-10 px-3 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg text-slate-700 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50">
    <option>选项1</option>
</select>
```

### 日期输入
```html
<input type="date" class="h-10 px-3 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg text-slate-700 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50">
```

### 表单状态
- **禁用**：添加 `disabled:opacity-50 disabled:cursor-not-allowed`
- **错误**：`border-rose-300 dark:border-rose-700 focus:ring-rose-500/50`
- **成功**：`border-emerald-300 dark:border-emerald-700 focus:ring-emerald-500/50`

## 🏷️ 状态徽章

### 基础徽章
```html
<span class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium">
    <span class="w-1.5 h-1.5 rounded-full bg-current"></span>
    状态文本
</span>
```

### 状态颜色规范

#### 进行中（蓝色）
```html
<span class="[基础类] bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300">
    进行中
</span>
```

#### 已完成（绿色）
```html
<span class="[基础类] bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-300">
    已完成
</span>
```

#### 警告（琥珀色）
```html
<span class="[基础类] bg-amber-50 dark:bg-amber-900/30 text-amber-600 dark:text-amber-300">
    警告
</span>
```

#### 错误（玫红色）
```html
<span class="[基础类] bg-rose-50 dark:bg-rose-900/30 text-rose-600 dark:text-rose-300">
    失败
</span>
```

#### 中性（灰色）
```html
<span class="[基础类] bg-slate-50 dark:bg-slate-900/30 text-slate-600 dark:text-slate-300">
    待处理
</span>
```

## 📊 表格组件

### 表格容器
```html
<div class="bg-white/80 dark:bg-slate-800/50 border border-slate-200/80 dark:border-slate-700/60 rounded-2xl overflow-hidden shadow-md">
    <table class="w-full">
        <!-- 表格内容 -->
    </table>
</div>
```

### 表头样式
```html
<thead class="bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-400 text-xs uppercase tracking-wider">
    <tr>
        <th class="px-4 py-3 md:px-6 md:py-4 text-left">列标题</th>
    </tr>
</thead>
```

### 表格行
```html
<tbody class="divide-y divide-slate-100 dark:divide-slate-800">
    <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/60">
        <td class="px-4 py-3 md:px-6 md:py-4 text-slate-700 dark:text-slate-200">
            单元格内容
        </td>
    </tr>
</tbody>
```

### 表格对齐
- **左对齐**：`text-left`（默认）
- **居中对齐**：`text-center`
- **右对齐**：`text-right`（常用于数值列）

## 🧭 导航组件

### 侧边栏菜单项
```html
<a href="#" class="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700/60 text-slate-700 dark:text-slate-200 transition-colors">
    <i data-lucide="home"></i>
    <span>菜单项</span>
</a>
```

### 选中状态
```html
<a href="#" class="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300">
    <i data-lucide="home"></i>
    <span>当前页面</span>
</a>
```

### 菜单分组
```html
<div class="space-y-1">
    <div class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider px-3 py-2">
        分组标题
    </div>
    <!-- 菜单项 -->
</div>
```

## ✨ 动效与交互

### 过渡动画
- **颜色变化**：`transition-colors duration-200`
- **综合过渡**：`transition-all duration-300`
- **透明度**：`transition-opacity duration-200`

### 阴影层级
- **基础阴影**：`shadow-md`
- **悬浮阴影**：`hover:shadow-lg`
- **轻微阴影**：`shadow-sm`
- **重阴影**：`shadow-xl`（用于弹窗等）

### 焦点样式
```css
/* 统一焦点样式 */
focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500/50
```

## 🎨 特殊效果

### 毛玻璃效果
```css
backdrop-blur-sm  /* 轻微模糊 */
backdrop-blur-md  /* 中等模糊 */
```

### 粒子背景
```css
#particle-canvas {
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    z-index: 0;
    pointer-events: none;
}

/* 确保内容在粒子背景之上 */
main > * {
    position: relative;
    z-index: 1;
}
```

## 📱 响应式设计

### 断点规范
- **sm**：640px+ (小平板)
- **md**：768px+ (平板)
- **lg**：1024px+ (小屏幕电脑)
- **xl**：1280px+ (大屏幕电脑)

### 响应式建议
```html
<!-- 移动端优先，逐步增强 -->
<div class="p-4 md:p-6 lg:p-8">
<div class="text-sm md:text-base lg:text-lg">
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
```

## 🎯 快速复用类名

### 卡片容器
```
bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm border border-slate-200/80 dark:border-slate-700/60 rounded-2xl shadow-md card-glow
```

### 输入框
```
h-10 px-3 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg placeholder-slate-400 text-slate-700 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50
```

### 主要按钮
```
inline-flex items-center justify-center h-10 px-4 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500/50 bg-blue-600 hover:bg-blue-700 text-white shadow-sm
```

### 次要按钮
```
inline-flex items-center justify-center h-10 px-4 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500/50 bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200 border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/70
```

### 进行中徽章
```
inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300
```

### 表格行悬浮
```
hover:bg-slate-50 dark:hover:bg-slate-800/60
```

## 📚 最佳实践

### 1. 类名组织
- 按功能分组：`layout positioning sizing colors typography effects transitions`
- 响应式修饰符放在最后：`p-4 md:p-6 lg:p-8`

### 2. 主题适配
- 所有有背景色的元素都要考虑深色模式
- 使用语义化的slate色彩系统
- 透明度使用 `/80`、`/50`、`/30` 等标准值

### 3. 一致性原则
- 圆角：优先使用 `rounded-lg`、`rounded-2xl`
- 间距：遵循 Tailwind 的4px基准（1、2、3、4、6、8...）
- 字号：text-xs, text-sm, text-base, text-lg
- 阴影：shadow-sm, shadow-md, shadow-lg

### 4. 性能优化
- 优先使用 Tailwind 原子类
- 减少自定义CSS，除非必要（如卡片光晕效果）
- 使用 CSS 变量处理主题切换

---

## 🔄 更新日志

- **2025-01-06**：初始版本，基于 E船期查询 项目实践总结
- 后续更新将记录新组件规范和设计改进

---

*本文档将随项目发展持续更新，确保设计规范的时效性和实用性。*