# 集装箱船期查询工作台系统产品需求文档

## 1. Product Overview
现代化的集装箱船期查询工作台系统，采用类似Grok或Manus的深色主题设计风格，为用户提供统一的工作台界面完成船期查询、任务管理、数据分析等核心功能。
- 解决传统船期查询系统界面分散、操作复杂的问题，为物流从业者提供高效的一站式查询平台。
- 目标打造业界领先的现代化船期查询工作台，提升用户工作效率和体验。

## 2. Core Features

### 2.1 User Roles
| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| Default User | 直接访问 | 可使用所有查询功能、任务管理、数据查看等核心功能 |

### 2.2 Feature Module
我们的集装箱船期查询工作台包含以下核心页面：
1. **统一工作台页面**: 主控制面板、快速查询区、实时数据概览、任务状态监控
2. **船期查询模块**: 智能查询表单、查询历史、收藏管理
3. **任务管理中心**: 任务列表、状态跟踪、批量操作
4. **数据分析面板**: 查询统计、趋势图表、性能指标
5. **船公司管理**: 船公司列表、配置管理、状态监控
6. **系统设置**: 个人偏好、主题切换、通知设置

### 2.3 Page Details
| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 统一工作台页面 | 主控制面板 | 显示系统概览、快捷操作按钮、实时状态指示器 |
| 统一工作台页面 | 快速查询区 | 集成查询表单、智能输入提示、一键查询功能 |
| 统一工作台页面 | 实时数据概览 | 今日查询数、成功率、活跃任务数等关键指标卡片 |
| 统一工作台页面 | 任务状态监控 | 实时任务列表、状态更新、进度条显示 |
| 船期查询模块 | 智能查询表单 | 容器号输入、船公司选择、优先级设置、高级筛选 |
| 船期查询模块 | 查询历史 | 历史记录列表、快速重查、结果导出 |
| 船期查询模块 | 收藏管理 | 常用查询收藏、快速访问、分类管理 |
| 任务管理中心 | 任务列表 | 分页显示、状态筛选、排序功能、批量选择 |
| 任务管理中心 | 状态跟踪 | 实时状态更新、进度显示、错误信息展示 |
| 任务管理中心 | 批量操作 | 批量重试、取消、删除、导出功能 |
| 数据分析面板 | 查询统计 | 查询量统计、成功率分析、时间分布图 |
| 数据分析面板 | 趋势图表 | 交互式图表、时间范围选择、数据钻取 |
| 数据分析面板 | 性能指标 | 响应时间、系统负载、错误率监控 |
| 船公司管理 | 船公司列表 | 支持的船公司展示、状态指示、配置入口 |
| 船公司管理 | 配置管理 | 船公司参数设置、API配置、测试连接 |
| 船公司管理 | 状态监控 | 连接状态、响应时间、可用性监控 |
| 系统设置 | 个人偏好 | 语言设置、时区配置、默认查询参数 |
| 系统设置 | 主题切换 | 深色/浅色主题、色彩方案、布局密度 |
| 系统设置 | 通知设置 | 任务完成通知、错误提醒、邮件推送配置 |

## 3. Core Process
用户主要操作流程：
1. 用户访问统一工作台页面，查看系统概览和实时数据
2. 在快速查询区输入容器号和选择船公司，发起查询任务
3. 系统创建查询任务，用户可在任务管理中心监控进度
4. 查询完成后，用户查看结果并可选择收藏或导出
5. 用户可在数据分析面板查看查询统计和趋势分析
6. 通过船公司管理模块配置和监控各船公司连接状态
7. 在系统设置中个性化配置工作台偏好

```mermaid
graph TD
  A[统一工作台页面] --> B[船期查询模块]
  A --> C[任务管理中心]
  A --> D[数据分析面板]
  B --> C
  C --> B
  A --> E[船公司管理]
  A --> F[系统设置]
  B --> G[查询结果]
  G --> C
```

## 4. User Interface Design
### 4.1 Design Style
- **主色调**: 深色主题 (#1a1d29 背景, #2f3349 侧边栏, #5865f2 主要按钮)
- **辅助色**: #7289da (次要按钮), #43a047 (成功), #f44336 (错误), #ff9800 (警告)
- **按钮风格**: 圆角按钮 (8px), 渐变效果, 悬停动画
- **字体**: 系统默认字体, 主标题 16-18px, 正文 14px, 辅助文字 12px
- **布局风格**: 卡片式设计, 左侧固定导航, 顶部状态栏, 主内容区域
- **图标风格**: Ant Design Icons, 线性图标, 统一16px尺寸

### 4.2 Page Design Overview
| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 统一工作台页面 | 主控制面板 | 深色背景卡片, 渐变色数据指标, 圆形进度环, 状态指示灯 |
| 统一工作台页面 | 快速查询区 | 圆角输入框, 下拉选择器, 主色调查询按钮, 输入提示气泡 |
| 统一工作台页面 | 实时数据概览 | 数据卡片网格布局, 图标+数字组合, 颜色编码状态 |
| 统一工作台页面 | 任务状态监控 | 紧凑型表格, 状态标签, 进度条, 操作按钮组 |
| 船期查询模块 | 智能查询表单 | 分步表单, 智能提示, 验证反馈, 重置按钮 |
| 任务管理中心 | 任务列表 | 虚拟滚动表格, 筛选器, 排序指示器, 批量选择框 |
| 数据分析面板 | 趋势图表 | 交互式图表, 时间选择器, 图例开关, 数据标签 |
| 船公司管理 | 状态监控 | 状态卡片, 连接指示器, 响应时间图表, 配置按钮 |

### 4.3 Responsiveness
桌面优先设计，支持移动端自适应，包含触摸交互优化。断点设置：桌面 ≥1200px, 平板 768-1199px, 手机 <768px。移动端采用底部导航栏，侧边栏改为抽屉式设计。