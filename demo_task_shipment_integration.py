#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务与货运记录联动演示脚本
演示创建货运记录时自动创建任务，以及任务完成后的状态同步
"""

import time
from datetime import datetime
from shipment_manager import ShipmentManager
from task_manager import TaskManager


def demo_task_shipment_integration():
    """
    演示任务与货运记录的联动功能
    """
    print("🔗 任务与货运记录联动演示")
    print("=" * 60)
    
    # 初始化管理器
    shipment_manager = ShipmentManager()
    task_manager = TaskManager()
    
    try:
        # 1. 创建货运记录（会自动创建任务）
        print("\n📦 步骤1: 创建货运记录（自动创建任务）")
        print("-" * 40)
        
        record_id = shipment_manager.create_shipment_record(
            bill_of_lading="DEMO123456",
            container_number="DEMO789012",
            carrier_company="MSC",
            remarks="演示联动功能",
            created_by="demo_user"
        )
        
        if record_id:
            print(f"✅ 货运记录创建成功，ID: {record_id}")
            
            # 查看创建的货运记录
            record = shipment_manager.get_shipment_record(record_id)
            if record:
                print(f"   状态: {record.get('status', '未知')}")
                print(f"   备注: {record.get('remarks', '')}")
        else:
            print("❌ 货运记录创建失败")
            return
        
        # 2. 查看自动创建的任务
        print("\n🎯 步骤2: 查看自动创建的任务")
        print("-" * 40)
        
        # 获取待处理任务
        pending_tasks = task_manager.get_pending_tasks()
        demo_task = None
        
        for task in pending_tasks:
            if 'DEMO123456' in task.get('tracking_number', ''):
                demo_task = task
                break
        
        if demo_task:
            task_id = demo_task['id']
            print(f"✅ 找到关联任务，ID: {task_id}")
            print(f"   任务名称: {demo_task.get('task_name', '')}")
            print(f"   任务状态: {demo_task.get('status', '')}")
            print(f"   跟踪号: {demo_task.get('tracking_number', '')}")
        else:
            print("❌ 未找到关联任务")
            return
        
        # 3. 模拟任务状态变化
        print("\n⚡ 步骤3: 模拟任务执行过程")
        print("-" * 40)
        
        # 更新任务为处理中
        print("   设置任务状态为 'processing'...")
        task_manager.update_task_status(task_id, 'processing')
        
        # 同步货运记录状态
        shipment_manager.sync_status_with_task(task_id)
        
        # 查看更新后的货运记录状态
        record = shipment_manager.get_shipment_record(record_id)
        if record:
            print(f"   ✅ 货运记录状态已更新为: {record.get('status', '未知')}")
        
        # 模拟处理时间
        print("   模拟任务处理中...")
        time.sleep(2)
        
        # 4. 模拟任务完成
        print("\n🎉 步骤4: 模拟任务完成")
        print("-" * 40)
        
        # 更新任务为已完成
        print("   设置任务状态为 'completed'...")
        task_manager.update_task_status(task_id, 'completed')
        
        # 模拟任务结果数据
        result_data = {
            'estimated_arrival_time': '2025-09-15',
            'dates_data': [
                {
                    'date': '2025-09-10',
                    'type': 'departure',
                    'location': 'Shanghai CN',
                    'description': '离港'
                },
                {
                    'date': '2025-09-15',
                    'type': 'arrival',
                    'location': 'Los Angeles US',
                    'description': '预计到港'
                }
            ]
        }
        
        # 处理任务完成
        success = shipment_manager.handle_task_completion(task_id, result_data)
        
        if success:
            print("   ✅ 任务完成处理成功")
            
            # 查看最终的货运记录状态
            record = shipment_manager.get_shipment_record(record_id)
            if record:
                print(f"   最终状态: {record.get('status', '未知')}")
                print(f"   预计到港时间: {record.get('estimated_arrival_time', '未设置')}")
                
                # 查看时间节点
                dates = record.get('dates', [])
                if dates:
                    print(f"   时间节点数量: {len(dates)}")
                    for date_info in dates:
                        print(f"     - {date_info.get('date', '')}: {date_info.get('description', '')}")
        else:
            print("   ❌ 任务完成处理失败")
        
        print("\n🎯 演示完成！")
        print("=" * 60)
        print("功能总结:")
        print("✅ 创建货运记录时自动创建查询任务")
        print("✅ 货运记录状态根据任务状态实时同步")
        print("✅ 任务完成后自动更新货运记录数据")
        print("✅ 支持时间节点数据的批量添加")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    demo_task_shipment_integration()