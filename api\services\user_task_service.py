#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户任务服务
基于现有任务管理系统，增加用户权限和信息管理
"""

import asyncio
import json
import sys
import uuid
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from api.models.user_schemas import (
    TaskCreateRequest,
    BatchTaskCreateRequest,
    TaskResponse,
    BatchTaskCreateResponse,
    UserInfo
)
from api.services.carrier_service import CarrierValidationService
from api.services.task_service import get_task_service

# 导入现有的任务管理器
try:
    from task_manager import TaskManager
    from utils.carrier_lookup import get_company_info
except ImportError as e:
    print(f"导入任务管理模块失败: {e}")

class UserTaskService:
    """用户任务服务类"""

    def __init__(self):
        self.task_manager = TaskManager()
        self.carrier_service = CarrierValidationService()
        self.task_service = get_task_service()


    async def create_single_task(
        self,
        request: TaskCreateRequest,
        user_info: UserInfo
    ) -> TaskResponse:
        """
        创建单个任务

        Args:
            request: 任务创建请求
            user_info: 用户信息

        Returns:
            任务响应
        """
        try:
            # 校验提单号
            validation_result = self.carrier_service.validate_tracking_number(request.container_number)

            # 确定承运人代码
            carrier_code = request.carrier_code
            if not carrier_code and validation_result.is_valid and validation_result.carrier_info:
                carrier_code = validation_result.carrier_info.code

            # 生成任务ID
            task_id = f"task_{uuid.uuid4().hex[:12]}"

            # 准备任务数据
            task_data = {
                "task_id": task_id,
                "container_number": request.container_number,
                "carrier_code": carrier_code or "UNKNOWN",
                "priority": request.priority.value,
                "user_id": user_info.user_id,
                "user_name": user_info.name,
                "callback_url": request.callback_url,
                "metadata": request.metadata or {},
                "created_at": datetime.now(),
                "status": "pending"
            }

            # 调用现有的任务管理器创建任务
            print(f"[DEBUG] create_single_task 准备调用 _create_task_in_manager")
            success = await self._create_task_in_manager(task_data)
            print(f"[DEBUG] _create_task_in_manager 返回结果: {success}")

            if not success:
                print(f"[DEBUG] 任务创建失败，抛出异常")
                raise Exception("任务创建失败")

            return TaskResponse(
                task_id=task_id,
                container_number=request.container_number,
                carrier_code=carrier_code,
                status="pending",
                priority=request.priority.value,
                user_id=user_info.user_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                result=None
            )

        except Exception as e:
            raise Exception(f"创建任务失败: {str(e)}")

    async def create_batch_tasks(
        self,
        request: BatchTaskCreateRequest,
        user_info: UserInfo
    ) -> BatchTaskCreateResponse:
        """
        批量创建任务

        Args:
            request: 批量任务创建请求
            user_info: 用户信息

        Returns:
            批量任务创建响应
        """
        created_tasks = []
        failed_tasks = []

        for i, task_request in enumerate(request.tasks):
            try:
                task_response = await self.create_single_task(task_request, user_info)
                created_tasks.append(task_response)
            except Exception as e:
                failed_tasks.append({
                    "index": i,
                    "container_number": task_request.container_number,
                    "error": str(e)
                })

        summary = {
            "total": len(request.tasks),
            "success": len(created_tasks),
            "failed": len(failed_tasks)
        }

        return BatchTaskCreateResponse(
            success=len(created_tasks) > 0,
            created_tasks=created_tasks,
            failed_tasks=failed_tasks,
            summary=summary
        )

    async def _create_task_in_manager(self, task_data: Dict[str, Any]) -> bool:
        """
        在任务管理器中创建任务

        Args:
            task_data: 任务数据

        Returns:
            是否创建成功
        """
        try:
            print(f"[DEBUG] _create_task_in_manager 开始创建任务: {task_data['container_number']} for user {task_data['user_id']}")
            print(f"[DEBUG] TaskManager实例: {self.task_manager}")
            
            # 调用现有的任务管理器创建任务
            task_id = self.task_manager.create_task(
                tracking_number=task_data["container_number"],
                task_type="shipment_query",  # 添加必需的task_type参数
                carrier=task_data["carrier_code"],
                priority=5,  # 正常优先级
                creator_id=task_data["user_id"],
                creator_name=task_data["user_name"]
            )
            
            print(f"[DEBUG] TaskManager返回任务ID: {task_id}")
            print(f"[DEBUG] _create_task_in_manager 任务创建成功")
            return True

        except Exception as e:
            print(f"[DEBUG] 任务管理器创建任务失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    async def get_user_tasks(
        self,
        user_info: UserInfo,
        status_filter: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[TaskResponse]:
        """
        获取用户任务列表

        Args:
            user_info: 用户信息
            status_filter: 状态过滤
            limit: 限制数量
            offset: 偏移量

        Returns:
            任务响应列表
        """
        try:
            # 直接从task_queue数据库获取用户任务
            conn = self.task_manager._get_connection()
            cursor = conn.cursor()
            
            # 构建查询SQL
            sql_parts = ["SELECT * FROM task_queue WHERE creator_id = ?"]
            params = [user_info.user_id]
            
            if status_filter:
                # 支持逗号分隔的多状态过滤，例如 "pending,running"
                if isinstance(status_filter, str) and "," in status_filter:
                    statuses = [s.strip() for s in status_filter.split(",") if s.strip()]
                    if statuses:
                        placeholders = ",".join(["?"] * len(statuses))
                        sql_parts.append(f"AND status IN ({placeholders})")
                        params.extend(statuses)
                else:
                    sql_parts.append("AND status = ?")
                    params.append(status_filter)
                
            sql_parts.append("ORDER BY updated_at DESC")
            
            if limit:
                sql_parts.append(f"LIMIT {limit}")
                if offset:
                    sql_parts.append(f"OFFSET {offset}")
            
            sql = " ".join(sql_parts)
            cursor.execute(sql, params)
            
            tasks = []
            rows = cursor.fetchall()
            
            for row in rows:
                columns = [col[0] for col in cursor.description]
                task_dict = dict(zip(columns, row))
                
                # 解析result_summary
                result = {}
                if task_dict.get('result_summary'):
                    try:
                        result = json.loads(task_dict['result_summary'])
                    except json.JSONDecodeError:
                        result = {}
                
                tasks.append(TaskResponse(
                    task_id=task_dict['id'],
                    container_number=task_dict['tracking_number'],
                    carrier_code=task_dict['carrier'],
                    status=task_dict['status'],
                    priority=str(task_dict.get('priority', 'normal')),  # 转换为字符串
                    user_id=task_dict['creator_id'],
                    created_at=task_dict['created_at'],
                    updated_at=task_dict['updated_at'],
                    result=result
                ))
            
            conn.close()
            return tasks
        except Exception as e:
            # 失败则退回空列表并记录日志
            print(f"get_user_tasks 失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def validate_and_create_from_text(
        self,
        text_input: str,
        user_info: UserInfo,
        priority: str = "normal",
        deduplicate: bool = True
    ) -> BatchTaskCreateResponse:
        """
        从文本解析并创建任务（一步完成）

        Args:
            text_input: 输入文本
            user_info: 用户信息
            priority: 任务优先级
            deduplicate: 是否去重

        Returns:
            批量任务创建响应
        """
        try:
            # 解析文本
            tracking_numbers = self.carrier_service.parse_batch_input(text_input)

            if not tracking_numbers:
                return BatchTaskCreateResponse(
                    success=False,
                    created_tasks=[],
                    failed_tasks=[{"error": "未解析出有效的提单号"}],
                    summary={"total": 0, "success": 0, "failed": 1}
                )

            # 去重处理
            if deduplicate:
                tracking_numbers = list(dict.fromkeys(tracking_numbers))

            # 限制数量
            if len(tracking_numbers) > 100:
                tracking_numbers = tracking_numbers[:100]

            # 创建任务请求列表
            task_requests = []
            for tracking_number in tracking_numbers:
                task_requests.append(TaskCreateRequest(
                    container_number=tracking_number,
                    priority=priority
                ))

            # 批量创建任务
            batch_request = BatchTaskCreateRequest(tasks=task_requests)
            return await self.create_batch_tasks(batch_request, user_info)

        except Exception as e:
            return BatchTaskCreateResponse(
                success=False,
                created_tasks=[],
                failed_tasks=[{"error": str(e)}],
                summary={"total": 0, "success": 0, "failed": 1}
            )

def get_user_task_service() -> UserTaskService:
    """获取用户任务服务实例"""
    return UserTaskService()