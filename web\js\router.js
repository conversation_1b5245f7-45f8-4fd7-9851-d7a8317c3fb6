// Simple SPA Router
class Router {
    constructor() {
        this.routes = {};
        this.currentRoute = null;
        
        // Listen for browser back/forward
        window.addEventListener('popstate', (e) => {
            this.handleRoute(e.state?.route || 'dashboard');
        });
        
        // Initialize router
        this.init();
    }
    
    // Register route
    register(path, handler) {
        this.routes[path] = handler;
    }
    
    // Navigate to route
    navigate(path, pushState = true) {
        if (this.currentRoute === path) return;
        
        this.currentRoute = path;
        
        if (pushState) {
            history.pushState({ route: path }, '', `#${path}`);
        }
        
        this.handleRoute(path);
    }
    
    // Handle route
    handleRoute(path) {
        const handler = this.routes[path];
        if (handler && typeof handler === 'function') {
            handler();
        } else {
            // Default to dashboard
            this.navigate('dashboard', false);
        }
    }
    
    // Initialize router
    init() {
        // Get initial route from URL hash
        const hash = window.location.hash.slice(1);
        const initialRoute = hash || 'dashboard';
        this.navigate(initialRoute, false);
    }
    
    // Get current route
    getCurrentRoute() {
        return this.currentRoute;
    }
}

// Export router class
window.Router = Router;