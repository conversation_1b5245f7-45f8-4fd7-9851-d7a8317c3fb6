#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试 - 状态更新机制完全工作验证
"""

import time
import sqlite3
from shipment_manager import ShipmentManager
from task_manager import TaskManager
from scheduled_task_processor import ScheduledTaskProcessor

def final_status_update_verification():
    """最终验证状态更新机制完全工作"""
    print("=== 状态更新机制最终验证测试 ===\n")
    
    # 初始化管理器
    shipment_manager = ShipmentManager()
    task_manager = TaskManager()
    
    # 清理旧测试数据
    print("1. 清理旧测试数据...")
    try:
        # 清理货运记录
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute("DELETE FROM shipment_records WHERE bill_of_lading LIKE 'VERIFY%'")
        conn.commit()
        conn.close()
        
        # 清理任务队列
        conn = sqlite3.connect('db/task_queue.db') 
        cursor = conn.cursor()
        cursor.execute("DELETE FROM task_queue WHERE tracking_number LIKE 'VERIFY%'")
        conn.commit()
        conn.close()
        print("   测试数据清理完成")
    except Exception as e:
        print(f"   清理数据时出错: {e}")
    
    # 收集状态更新历史
    status_updates = []
    
    def verification_callback(task_id, task_stage, result_data):
        """验证回调函数 - 记录所有状态更新"""
        print(f"\n[CALLBACK] 任务完成回调触发:")
        print(f"   任务ID: {task_id}")
        print(f"   阶段: {task_stage}")
        print(f"   数据: {result_data}")
        
        if task_stage == 'status_update' and result_data:
            if 'shipment_status_update' in result_data:
                update_info = result_data['shipment_status_update']
                record_id = update_info.get('record_id')
                new_status = update_info.get('new_status')
                
                print(f"   状态更新: 记录ID {record_id} -> {new_status}")
                
                # 记录状态更新
                status_updates.append({
                    'record_id': record_id,
                    'new_status': new_status,
                    'timestamp': time.time()
                })
                
                # 执行实际状态更新
                success = shipment_manager.update_shipment_status(record_id, new_status, 'verification_test')
                print(f"   更新结果: {'成功' if success else '失败'}")
                return success
        
        return True
    
    # 创建测试记录
    print("\n2. 创建验证测试记录...")
    test_bill = "VERIFY001"
    record_id = shipment_manager.create_shipment_record(
        bill_of_lading=test_bill,
        carrier_company="验证测试船公司",
        created_by="状态更新验证测试"
    )
    print(f"   验证记录已创建，ID: {record_id}")
    
    # 检查初始状态
    print("\n3. 验证初始状态...")
    records = shipment_manager.search_shipment_records(bill_of_lading=test_bill)
    if records:
        record = records[0]
        initial_status = record['status']
        print(f"   初始状态: {initial_status}")
        assert initial_status == "排队中", f"初始状态应为'排队中'，实际为'{initial_status}'"
    else:
        print("   ERROR: 未找到刚创建的记录")
        return False
    
    # 创建处理器并测试状态更新
    print("\n4. 创建处理器并测试状态更新回调...")
    processor = ScheduledTaskProcessor(
        scraping_interval=60,  # 设置长间隔避免实际抓取
        ai_interval=60,
        status_update_interval=60,
        max_scraping_tasks=1,
        max_ai_tasks=1,
        completion_callback=verification_callback
    )
    
    # 模拟获取任务
    time.sleep(1)
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute("""
        SELECT id, task_name, tracking_number, remarks 
        FROM task_queue 
        WHERE tracking_number = ? 
        ORDER BY created_at DESC LIMIT 1
    """, (test_bill,))
    task_data = cursor.fetchone()
    conn.close()
    
    if task_data:
        task_dict = {
            'id': task_data[0],
            'task_name': task_data[1],
            'tracking_number': task_data[2],
            'remarks': task_data[3] or ''
        }
        
        print(f"   找到任务: {task_dict['task_name']}")
        print(f"   任务备注: {task_dict['remarks']}")
        
        # 验证任务备注包含货运记录ID
        if f"货运记录ID: {record_id}" in task_dict['remarks']:
            print("   [OK] 任务备注包含正确的货运记录ID")
        else:
            print(f"   [ERROR] 任务备注格式错误: {task_dict['remarks']}")
            return False
        
        # 测试状态更新
        print("\n5. 测试状态更新机制...")
        test_statuses = ["处理中", "已完成"]

        for i, test_status in enumerate(test_statuses):
            print(f"\n   测试 {i+1}: 更新为 '{test_status}'")

            # 调用状态更新方法
            processor._update_shipment_status_from_task(task_dict, test_status)
            
            # 等待回调执行
            time.sleep(0.5)
            
            # 验证状态是否更新
            records = shipment_manager.search_shipment_records(bill_of_lading=test_bill)
            if records:
                record = records[0]
                current_status = record['status']
                print(f"   当前状态: {current_status}")
                
                if current_status == test_status:
                    print(f"   [OK] 状态成功更新为 '{test_status}'")
                else:
                    print(f"   [ERROR] 状态更新失败，期望'{test_status}'，实际'{current_status}'")
                    return False
            else:
                print("   ✗ 无法查询记录状态")
                return False
    
    else:
        print("   ERROR: 未找到相关任务")
        return False
    
    # 验证状态更新历史
    print("\n6. 验证状态更新历史...")
    print(f"   记录的状态更新次数: {len(status_updates)}")
    
    if len(status_updates) >= 3:
        print("   ✓ 状态更新回调正常触发")
        for i, update in enumerate(status_updates):
            print(f"   更新{i+1}: 记录{update['record_id']} -> {update['new_status']}")
    else:
        print(f"   ✗ 状态更新回调次数不足，期望3次，实际{len(status_updates)}次")
        return False
    
    print("\n=== 验证结果 ===")
    print("✓ 货运记录创建正常")
    print("✓ 任务自动创建正常") 
    print("✓ 任务备注包含货运记录ID")
    print("✓ 状态更新回调机制正常")
    print("✓ 数据库状态更新正常")
    print("\n🎉 状态更新机制验证完全通过！")
    
    return True

if __name__ == "__main__":
    success = final_status_update_verification()
    print(f"\n测试结果: {'通过' if success else '失败'}")