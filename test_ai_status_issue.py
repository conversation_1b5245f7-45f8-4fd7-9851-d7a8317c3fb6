#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析状态显示问题 - 修复后的逻辑
"""

import sqlite3
import os

def test_new_logic(tracking_number):
    """测试新的状态查询逻辑"""
    if not os.path.exists('db/task_queue.db'):
        print("❌ 数据库不存在")
        return
    
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    
    print(f"🔍 测试跟踪号: {tracking_number}")
    print("="*50)
    
    status_mapping = {
        'pending': '待处理',
        'processing': '进行中', 
        'completed': '已完成',
        'failed': '失败'
    }
    
    print("🔄 新逻辑：优先查找进行中的任务")
    
    # 优先查找进行中或待处理的网页抓取任务
    cursor.execute("""
        SELECT id, status, created_at, error_message
        FROM task_queue 
        WHERE tracking_number = ? AND task_stage = 'scraping'
        AND status IN ('pending', 'processing')
        ORDER BY created_at DESC
        LIMIT 1
    """, (tracking_number,))
    
    scraping_task = cursor.fetchone()
    print(f"进行中的抓取任务: {scraping_task is not None}")
    
    # 如果没有进行中的抓取任务，查找最新的已完成抓取任务
    if not scraping_task:
        cursor.execute("""
            SELECT id, status, created_at, error_message
            FROM task_queue 
            WHERE tracking_number = ? AND task_stage = 'scraping'
            ORDER BY created_at DESC
            LIMIT 1
        """, (tracking_number,))
        scraping_task = cursor.fetchone()
        print(f"使用最新的已完成抓取任务")
    
    if scraping_task:
        scraping_id, scraping_db_status, scraping_created, scraping_error = scraping_task
        scraping_display = status_mapping.get(scraping_db_status, scraping_db_status)
        
        print(f"网页抓取: {scraping_db_status} -> {scraping_display}")
        print(f"抓取任务时间: {scraping_created}")
        
        # 优先查找进行中或待处理的AI分析任务
        cursor.execute("""
            SELECT id, status, created_at, error_message
            FROM task_queue 
            WHERE tracking_number = ? 
            AND task_stage = 'ai_analysis'
            AND status IN ('pending', 'processing')
            AND (parent_task_id = ? OR created_at >= ?)
            ORDER BY created_at DESC
            LIMIT 1
        """, (tracking_number, scraping_id, scraping_created))
        
        ai_task = cursor.fetchone()
        print(f"进行中的AI任务: {ai_task is not None}")
        
        # 如果没有进行中的AI任务，查找最新的已完成AI任务
        if not ai_task:
            cursor.execute("""
                SELECT id, status, created_at, error_message
                FROM task_queue 
                WHERE tracking_number = ? 
                AND task_stage = 'ai_analysis'
                AND (parent_task_id = ? OR created_at >= ?)
                ORDER BY created_at DESC
                LIMIT 1
            """, (tracking_number, scraping_id, scraping_created))
            ai_task = cursor.fetchone()
            print(f"使用最新的已完成AI任务")
        
        if ai_task:
            ai_id, ai_db_status, ai_created, ai_error = ai_task
            ai_display = status_mapping.get(ai_db_status, ai_db_status)
            print(f"AI分析: {ai_db_status} -> {ai_display}")
            print(f"AI任务时间: {ai_created}")
        else:
            # 没有AI分析任务，根据抓取状态推断
            if scraping_db_status == 'completed':
                ai_display = "准备中"
            elif scraping_db_status in ['pending', 'processing']:
                ai_display = "等待中"
            else:
                ai_display = "未开始"
            print(f"AI分析: (推断) -> {ai_display}")
    else:
        print("没有找到任何抓取任务")
    
    conn.close()

def suggest_test_cases():
    """建议测试用例"""
    print("\n" + "="*50)
    print("💡 测试建议:")
    print("1. 请添加一个新的测试跟踪号 (比如 TEST_NEW_2025)")
    print("2. 观察新任务的状态显示是否为 '待处理' 和 '等待中'")
    print("3. 让任务处理器运行，观察状态变化")
    print("4. 验证历史已完成的任务不会影响新任务的状态显示")

if __name__ == "__main__":
    # 测试最新的跟踪号
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT tracking_number FROM task_queue ORDER BY rowid DESC LIMIT 3")
    results = cursor.fetchall()
    
    if results:
        print("最近的3个跟踪号:")
        for i, (tracking,) in enumerate(results, 1):
            print(f"{i}. {tracking}")
        
        print(f"\n详细测试修复后的逻辑:")
        for tracking, in results:
            test_new_logic(tracking)
            print()
    else:
        print("数据库中没有任务")
    
    conn.close()
    suggest_test_cases()