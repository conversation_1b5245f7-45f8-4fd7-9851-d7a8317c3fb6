# 🚢 Container Helper - 智能集装箱货运跟踪系统

## 🚨 前端开发重要提醒

**每次修改前端代码后必须刷新缓存！**

- ✅ 正确访问地址: `http://127.0.0.1:8080/?v=版本号`
- ❌ 错误地址: `http://127.0.0.1:8080/web/`
- 🔄 强制刷新: `Ctrl + F5`

详细说明请查看: [FRONTEND_DEVELOPMENT_REMINDERS.md](./FRONTEND_DEVELOPMENT_REMINDERS.md)

## 📊 项目概述

**Container Helper** 是一个基于AI驱动的智能集装箱货运跟踪系统，集成了现代化的桌面界面、自动化网页抓取、智能数据分析和任务管理功能。系统能够自动从各大船公司网站获取货运信息，并通过AI技术进行智能分析和处理。

## 🏗️ 系统架构

### 核心模块
- **🖥️ 用户界面层** - 基于PySide6的现代化桌面应用
- **🤖 AI分析引擎** - 集成多种AI模型的视觉和文本分析
- **🕷️ 自动化抓取** - 基于Playwright的智能网页操作
- **📊 数据管理** - SQLite数据库集群管理
- **⚙️ 任务调度** - 异步任务处理和队列管理

## 🚀 主要功能特性

### ✅ 核心功能模块

#### 🖥️ 现代化桌面界面
- **商业级UI设计** - 基于PySide6构建的现代化桌面应用界面
- **实时数据展示** - 动态更新的货运信息表格和状态监控
- **交互式操作** - 直观的货运记录管理和查询功能
- **响应式布局** - 自适应窗口大小的界面设计

#### 🤖 AI智能分析
- **视觉AI分析** - 基于多种AI模型的网页截图智能识别（默认豆包AI）
- **文本内容解析** - HTML内容的智能提取和物流信息分析
- **自动化决策** - AI驱动的网页操作路径规划
- **数据结构化** - 非结构化数据的智能整理和标准化

#### 🕷️ 自动化网页抓取
- **多船公司支持** - 支持MSC、Maersk、COSCO、阳明等主流船公司网站的自动化查询
- **智能承运人识别** - 基于数据库的动态承运人检测和网站适配
- **智能元素定位** - 动态识别和操作网页元素，支持不同船公司的页面结构
- **容错机制** - 网络异常和页面变化的自动处理
- **数据持久化** - 自动保存截图、HTML和分析结果

#### 📊 数据管理系统
- **多数据库架构** - 分离式SQLite数据库集群管理
- **承运人数据库** - 完整的船公司信息管理，包括SCAC代码、网站配置和页面元素
- **货运记录管理** - 完整的提单和箱号跟踪记录
- **AI调用日志** - 详细的AI服务调用历史和性能监控
- **任务队列管理** - 异步任务的调度和状态跟踪

## 🛠️ 技术栈

### 前端界面
- **PySide6** - 现代化Qt桌面应用框架
- **自定义UI组件** - 商业级界面设计和交互体验

### 后端核心
- **Python 3.8+** - 主要开发语言
- **Playwright** - 自动化浏览器操作
- **SQLite** - 轻量级数据库解决方案
- **异步任务处理** - 多线程任务调度

### AI服务
- **多AI模型支持** - 支持豆包AI、Gemini等多种AI服务
- **默认豆包AI** - 高性价比的国产AI服务
- **自定义AI客户端** - 统一的AI服务接口，支持灵活切换

### 数据存储
- **承运人数据库** - 船公司信息、SCAC代码、网站配置和页面元素管理
- **货运数据库** - 提单和箱号跟踪记录
- **AI日志数据库** - 服务调用历史和性能监控
- **任务队列数据库** - 异步任务状态管理

## 📁 项目结构

```
container-helper/
├── 📱 app.py                    # 主应用程序入口
├── 🤖 ai/                       # AI分析模块
│   ├── client.py               # AI客户端管理
│   ├── vision_analyzer.py      # 视觉AI分析
│   └── text_analyzer.py        # 文本AI分析
├── 💾 db/                       # 数据库模块
│   ├── *.db                    # SQLite数据库文件（包含carriers.db承运人数据库）
│   └── init_*.py               # 数据库初始化脚本
├── 🔧 carrier_management_ui.py  # 承运人管理界面
├── ⚙️ task_*.py                 # 任务管理模块
├── 📊 shipment_manager.py       # 货运记录管理
├── 🔧 utils/                    # 工具模块
└── 📁 files/                    # 数据文件存储
```

## 🚀 快速开始

### 📋 系统要求
- **Python 3.8+**
- **Windows 10/11** (推荐)
- **8GB+ RAM**
- **稳定的网络连接**

### 🔧 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd container-helper
```

#### 2. 安装Python依赖
```bash
# 安装核心依赖
pip install -U PySide6 playwright
pip install -U langchain-google-vertexai google-cloud-aiplatform

# 安装Playwright浏览器
playwright install chromium
```

#### 3. 配置AI服务
```bash
# 配置豆包AI（推荐）
# 在AI模型管理界面中配置豆包AI的API密钥

# 或配置其他AI服务（可选）
# 支持Gemini、Claude等多种AI模型
```

#### 4. 初始化数据库
```bash
# 进入数据库目录
cd db

# 初始化所有数据库
python init_database.py
python init_shipment_database.py
python init_task_queue.py
python init_carrier_database.py

cd ..
```

#### 5. 启动应用
```bash
# 启动主应用程序
python app.py
```

### 🎯 使用指南

#### 基本操作
1. **添加货运记录** - 在界面中输入提单号或箱号
2. **自动查询** - 系统自动访问船公司网站获取信息
3. **AI分析** - 智能解析网页内容提取关键信息
4. **数据管理** - 查看和管理所有货运跟踪记录

#### 高级功能
- **批量处理** - 支持多个提单号的批量查询
- **任务调度** - 自动化的后台任务处理
- **数据导出** - 支持多种格式的数据导出
- **AI统计** - 详细的AI服务使用统计和性能监控

## 🔧 开发说明

### 📝 更新日志

#### v2.1.0 - 承运人数据库系统 (2025-08)
- 🚢 **承运人数据库** - 实现完整的船公司信息数据库管理系统
- 🔍 **智能承运人识别** - 基于SCAC代码和提单号规则的自动承运人检测
- 🌐 **多船公司支持** - 支持MSC、Maersk、COSCO、阳明等主流船公司
- 🎯 **动态网站适配** - 根据承运人自动选择对应的查询网站和页面元素
- 📊 **承运人管理界面** - 提供完整的承运人信息管理和配置功能
- 🔧 **数据流优化** - 从用户输入到网页抓取的完整数据流链路打通

#### v2.0.0 - 重大架构升级 (2025-01)
- 🎯 **完整系统重构** - 从简单网页抓取工具升级为完整的集装箱货运跟踪系统
- 🖥️ **现代化界面** - 采用PySide6构建商业级桌面应用
- 🤖 **AI智能分析** - 集成多种AI模型的视觉和文本分析能力，默认使用豆包AI
- 📊 **数据库架构** - 实现分离式SQLite数据库集群管理
- ⚙️ **任务系统** - 构建完整的异步任务处理和调度机制
- 🕷️ **自动化抓取** - 支持多船公司网站的智能化数据获取
- 🔧 **模块化设计** - 采用清晰的模块分离和统一的接口设计

### 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork** 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 **Pull Request**

### 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

### 📞 支持与反馈

如果您遇到问题或有改进建议，请：
- 提交 [Issue](../../issues)
- 发送邮件至项目维护者
- 查看 [Wiki](../../wiki) 获取更多文档

---

**Container Helper** - 让集装箱货运跟踪变得智能化 🚢✨