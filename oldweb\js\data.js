// 数据管理器
class DataManager {
    constructor() {
        this.storageKey = 'shipment_query_data';
        this.mockData = this.generateMockData();
    }
    
    // 生成模拟数据
    generateMockData() {
        const ports = {
            'CNSHA': '上海港',
            'CNNGB': '宁波港',
            'CNSZX': '深圳港',
            'CNQIN': '青岛港',
            'CNTAO': '天津港',
            'USLAX': '洛杉矶港',
            'USNYC': '纽约港',
            'DEHAM': '汉堡港',
            'NLRTM': '鹿特丹港',
            'SGSIN': '新加坡港'
        };
        
        const shippingLines = ['COSCO', 'MSC', 'MAERSK', 'CMA-CGM', 'EVERGREEN'];
        
        const mockTasks = [
            {
                id: '1',
                name: '上海到洛杉矶航线查询',
                shippingLine: 'COSCO',
                originPort: 'CNSHA',
                destinationPort: 'USLAX',
                startDate: '2024-01-15',
                endDate: '2024-02-15',
                status: 'completed',
                progress: 100,
                createdAt: '2024-01-10',
                updatedAt: '2024-01-12T10:30:00Z',
                departureDate: '2024-01-18',
                arrivalDate: '2024-02-02',
                transitTime: '15天',
                notes: '春节前最后一班船期'
            },
            {
                id: '2',
                name: '深圳到鹿特丹船期查询',
                shippingLine: 'MSC',
                originPort: 'CNSZX',
                destinationPort: 'NLRTM',
                startDate: '2024-01-20',
                endDate: '2024-02-20',
                status: 'running',
                progress: 65,
                createdAt: '2024-01-15',
                updatedAt: '2024-01-15T14:20:00Z',
                notes: '欧洲航线常规查询'
            },
            {
                id: '3',
                name: '宁波到汉堡港查询',
                shippingLine: 'MAERSK',
                originPort: 'CNNGB',
                destinationPort: 'DEHAM',
                startDate: '2024-01-25',
                endDate: '2024-02-25',
                status: 'pending',
                progress: 0,
                createdAt: '2024-01-20',
                updatedAt: '2024-01-20T09:15:00Z',
                notes: '德国港口专线'
            }
        ];
        
        return {
            tasks: mockTasks,
            ports,
            shippingLines,
            schedules: this.generateMockSchedules()
        };
    }
    
    // 生成模拟船期数据
    generateMockSchedules() {
        const schedules = [];
        const routes = [
            { origin: 'CNSHA', destination: 'USLAX', line: 'COSCO', vessel: 'COSCO SHIPPING UNIVERSE' },
            { origin: 'CNSZX', destination: 'NLRTM', line: 'MSC', vessel: 'MSC GULSUN' },
            { origin: 'CNNGB', destination: 'DEHAM', line: 'MAERSK', vessel: 'MADRID MAERSK' },
            { origin: 'CNQIN', destination: 'USNYC', line: 'CMA-CGM', vessel: 'CMA CGM MARCO POLO' },
            { origin: 'CNTAO', destination: 'SGSIN', line: 'EVERGREEN', vessel: 'EVER GIVEN' }
        ];
        
        routes.forEach((route, index) => {
            const baseDate = new Date();
            baseDate.setDate(baseDate.getDate() + (index * 7));
            
            for (let i = 0; i < 3; i++) {
                const departureDate = new Date(baseDate);
                departureDate.setDate(departureDate.getDate() + (i * 14));
                
                const arrivalDate = new Date(departureDate);
                arrivalDate.setDate(arrivalDate.getDate() + (15 + Math.floor(Math.random() * 10)));
                
                schedules.push({
                    id: `schedule_${index}_${i}`,
                    ...route,
                    departureDate: departureDate.toISOString().split('T')[0],
                    arrivalDate: arrivalDate.toISOString().split('T')[0],
                    transitTime: `${Math.floor((arrivalDate - departureDate) / (1000 * 60 * 60 * 24))}天`,
                    voyage: `${route.line}${String(Math.floor(Math.random() * 999) + 100).padStart(3, '0')}E`,
                    status: Math.random() > 0.1 ? 'confirmed' : 'tentative'
                });
            }
        });
        
        return schedules;
    }
    
    // 从本地存储加载数据
    loadFromStorage() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const data = JSON.parse(stored);
                return {
                    tasks: data.tasks || [],
                    settings: data.settings || {}
                };
            }
        } catch (error) {
            console.warn('Failed to load data from storage:', error);
        }
        
        return {
            tasks: [],
            settings: {}
        };
    }
    
    // 保存数据到本地存储
    saveToStorage(data) {
        try {
            const existing = this.loadFromStorage();
            const merged = {
                ...existing,
                ...data,
                lastUpdated: new Date().toISOString()
            };
            
            localStorage.setItem(this.storageKey, JSON.stringify(merged));
            return true;
        } catch (error) {
            console.error('Failed to save data to storage:', error);
            return false;
        }
    }
    
    // 获取模拟任务数据
    getMockTasks() {
        return this.mockData.tasks;
    }
    
    // 获取港口列表
    getPorts() {
        return this.mockData.ports;
    }
    
    // 获取船公司列表
    getShippingLines() {
        return this.mockData.shippingLines;
    }
    
    // 获取船期数据
    getSchedules(filters = {}) {
        let schedules = [...this.mockData.schedules];
        
        // 应用筛选条件
        if (filters.origin) {
            schedules = schedules.filter(s => s.origin === filters.origin);
        }
        
        if (filters.destination) {
            schedules = schedules.filter(s => s.destination === filters.destination);
        }
        
        if (filters.shippingLine) {
            schedules = schedules.filter(s => s.line === filters.shippingLine);
        }
        
        if (filters.startDate) {
            schedules = schedules.filter(s => s.departureDate >= filters.startDate);
        }
        
        if (filters.endDate) {
            schedules = schedules.filter(s => s.departureDate <= filters.endDate);
        }
        
        return schedules;
    }
    
    // 模拟任务执行
    simulateTaskExecution(taskId, onProgress, onComplete) {
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                
                // 生成模拟结果
                const mockResult = this.generateTaskResult(taskId);
                if (onComplete) onComplete(mockResult);
            } else {
                if (onProgress) onProgress(progress);
            }
        }, 1000 + Math.random() * 2000);
        
        return interval;
    }
    
    // 生成任务结果
    generateTaskResult(taskId) {
        const schedules = this.getSchedules();
        const randomSchedules = schedules
            .sort(() => Math.random() - 0.5)
            .slice(0, Math.floor(Math.random() * 5) + 2);
        
        return {
            taskId,
            schedules: randomSchedules,
            totalFound: randomSchedules.length,
            searchTime: Math.floor(Math.random() * 5000) + 1000,
            timestamp: new Date().toISOString()
        };
    }
    
    // 清除所有数据
    clearAllData() {
        try {
            localStorage.removeItem(this.storageKey);
            return true;
        } catch (error) {
            console.error('Failed to clear data:', error);
            return false;
        }
    }
    
    // 导出数据
    exportData() {
        const data = this.loadFromStorage();
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `shipment_query_backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 导出数据管理器
window.DataManager = DataManager;