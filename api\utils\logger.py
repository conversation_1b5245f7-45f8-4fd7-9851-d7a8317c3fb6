#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API日志工具
提供结构化日志记录功能
"""

import os
import sys
import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path
from logging.handlers import RotatingFileHandler

class APILogger:
    """API日志记录器"""
    
    def __init__(self, name: str = "api", log_level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        file_handler = RotatingFileHandler(
            log_dir / "api.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # 错误文件处理器
        error_handler = RotatingFileHandler(
            log_dir / "api_error.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        self.logger.addHandler(error_handler)
    
    def _format_extra(self, extra: Optional[Dict[str, Any]]) -> str:
        """格式化额外信息"""
        if not extra:
            return ""
        try:
            return f" | {json.dumps(extra, ensure_ascii=False, default=str)}"
        except Exception:
            return f" | {str(extra)}"
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """调试日志"""
        self.logger.debug(message + self._format_extra(extra))
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """信息日志"""
        self.logger.info(message + self._format_extra(extra))
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """警告日志"""
        self.logger.warning(message + self._format_extra(extra))
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """错误日志"""
        self.logger.error(message + self._format_extra(extra), exc_info=exc_info)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """严重错误日志"""
        self.logger.critical(message + self._format_extra(extra), exc_info=exc_info)

class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self, logger: APILogger):
        self.logger = logger
    
    def log_request(self, method: str, url: str, client_ip: str, user_agent: str = None):
        """记录请求日志"""
        self.logger.info(
            f"请求开始: {method} {url}",
            extra={
                "type": "request",
                "method": method,
                "url": url,
                "client_ip": client_ip,
                "user_agent": user_agent
            }
        )
    
    def log_response(self, method: str, url: str, status_code: int, process_time: float):
        """记录响应日志"""
        level = "info" if status_code < 400 else "error"
        getattr(self.logger, level)(
            f"请求完成: {method} {url} - {status_code} ({process_time:.3f}s)",
            extra={
                "type": "response",
                "method": method,
                "url": url,
                "status_code": status_code,
                "process_time": process_time
            }
        )
    
    def log_error(self, method: str, url: str, error: Exception, client_ip: str):
        """记录错误日志"""
        self.logger.error(
            f"请求异常: {method} {url} - {type(error).__name__}: {str(error)}",
            extra={
                "type": "request_error",
                "method": method,
                "url": url,
                "client_ip": client_ip,
                "error_type": type(error).__name__,
                "error_message": str(error)
            },
            exc_info=True
        )

class TaskLogger:
    """任务日志记录器"""
    
    def __init__(self, logger: APILogger):
        self.logger = logger
    
    def log_task_created(self, task_id: str, container_number: str, carrier_code: str = None):
        """记录任务创建日志"""
        self.logger.info(
            f"任务创建: {task_id} - {container_number}",
            extra={
                "type": "task_created",
                "task_id": task_id,
                "container_number": container_number,
                "carrier_code": carrier_code
            }
        )
    
    def log_task_started(self, task_id: str):
        """记录任务开始日志"""
        self.logger.info(
            f"任务开始: {task_id}",
            extra={
                "type": "task_started",
                "task_id": task_id
            }
        )
    
    def log_task_progress(self, task_id: str, progress: int, message: str = None):
        """记录任务进度日志"""
        self.logger.info(
            f"任务进度: {task_id} - {progress}%" + (f" - {message}" if message else ""),
            extra={
                "type": "task_progress",
                "task_id": task_id,
                "progress": progress,
                "message": message
            }
        )
    
    def log_task_completed(self, task_id: str, duration: float, result_count: int = None):
        """记录任务完成日志"""
        self.logger.info(
            f"任务完成: {task_id} - 耗时{duration:.2f}秒" + (f" - {result_count}条结果" if result_count else ""),
            extra={
                "type": "task_completed",
                "task_id": task_id,
                "duration": duration,
                "result_count": result_count
            }
        )
    
    def log_task_failed(self, task_id: str, error: str, duration: float = None):
        """记录任务失败日志"""
        self.logger.error(
            f"任务失败: {task_id} - {error}" + (f" - 耗时{duration:.2f}秒" if duration else ""),
            extra={
                "type": "task_failed",
                "task_id": task_id,
                "error": error,
                "duration": duration
            }
        )
    
    def log_task_cancelled(self, task_id: str, reason: str = None):
        """记录任务取消日志"""
        self.logger.info(
            f"任务取消: {task_id}" + (f" - {reason}" if reason else ""),
            extra={
                "type": "task_cancelled",
                "task_id": task_id,
                "reason": reason
            }
        )

class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger: APILogger):
        self.logger = logger
    
    def log_slow_request(self, method: str, url: str, duration: float, threshold: float = 5.0):
        """记录慢请求日志"""
        if duration > threshold:
            self.logger.warning(
                f"慢请求: {method} {url} - {duration:.3f}秒",
                extra={
                    "type": "slow_request",
                    "method": method,
                    "url": url,
                    "duration": duration,
                    "threshold": threshold
                }
            )
    
    def log_database_query(self, query: str, duration: float, result_count: int = None):
        """记录数据库查询日志"""
        self.logger.debug(
            f"数据库查询: {duration:.3f}秒" + (f" - {result_count}条结果" if result_count else ""),
            extra={
                "type": "database_query",
                "query": query[:200],  # 限制查询语句长度
                "duration": duration,
                "result_count": result_count
            }
        )
    
    def log_external_api_call(self, url: str, method: str, duration: float, status_code: int = None):
        """记录外部API调用日志"""
        level = "info" if not status_code or status_code < 400 else "warning"
        getattr(self.logger, level)(
            f"外部API调用: {method} {url} - {duration:.3f}秒" + (f" - {status_code}" if status_code else ""),
            extra={
                "type": "external_api_call",
                "url": url,
                "method": method,
                "duration": duration,
                "status_code": status_code
            }
        )

# 全局日志记录器实例
api_logger = APILogger("shipment_api")
request_logger = RequestLogger(api_logger)
task_logger = TaskLogger(api_logger)
performance_logger = PerformanceLogger(api_logger)

def get_logger(name: str = "api") -> APILogger:
    """获取日志记录器实例"""
    return APILogger(name)

def get_request_logger() -> RequestLogger:
    """获取请求日志记录器"""
    return request_logger

def get_task_logger() -> TaskLogger:
    """获取任务日志记录器"""
    return task_logger

def get_performance_logger() -> PerformanceLogger:
    """获取性能日志记录器"""
    return performance_logger