#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
船期查询API路由
处理船期查询相关的HTTP请求
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse

from api.models.schemas import (
    ShipmentQueryRequest,
    TaskResponse,
    CarrierListResponse,
    StatsResponse,
    APIResponse
)
from api.services.task_service import TaskService, get_task_service
from api.utils.rate_limiter import rate_limit_dependency
from api.utils.logger import api_logger

router = APIRouter()

@router.post("/shipment/query", response_model=TaskResponse)
async def create_shipment_query(
    request: ShipmentQueryRequest,
    task_service: TaskService = Depends(get_task_service),
    _: None = Depends(rate_limit_dependency)
):
    """
    创建船期查询任务
    
    - **container_number**: 集装箱号（必填）
    - **carrier_code**: 船公司代码（可选）
    - **priority**: 任务优先级（可选，默认normal）
    - **callback_url**: 回调URL（可选）
    - **metadata**: 附加元数据（可选）
    """
    try:
        api_logger.info(f"创建船期查询任务: {request.container_number}")
        
        task = await task_service.create_shipment_query(
            container_number=request.container_number,
            carrier_code=request.carrier_code,
            priority=request.priority.value,
            callback_url=request.callback_url,
            metadata=request.metadata
        )
        
        api_logger.info(f"船期查询任务创建成功: {task.task_id}")
        return task
        
    except ValueError as e:
        api_logger.error(f"创建船期查询任务失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"创建船期查询任务异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.get("/shipment/carriers", response_model=CarrierListResponse)
async def get_supported_carriers(
    task_service: TaskService = Depends(get_task_service),
    supported_only: bool = Query(True, description="是否只返回支持的船公司")
):
    """
    获取支持的船公司列表
    
    - **supported_only**: 是否只返回支持查询的船公司
    """
    try:
        api_logger.info("获取支持的船公司列表")
        
        carriers = await task_service.get_supported_carriers(supported_only)
        
        api_logger.info(f"获取到 {len(carriers.carriers)} 个船公司")
        return carriers
        
    except Exception as e:
        api_logger.error(f"获取船公司列表异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@router.get("/shipment/stats", response_model=StatsResponse)
async def get_query_stats(
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取查询统计信息
    
    返回今日、本周、本月和总计的查询统计数据
    """
    try:
        api_logger.info("获取查询统计信息")
        
        stats = await task_service.get_query_stats()
        
        api_logger.info("查询统计信息获取成功")
        return stats
        
    except Exception as e:
        api_logger.error(f"获取查询统计信息异常: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")