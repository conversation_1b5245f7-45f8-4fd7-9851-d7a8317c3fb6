import os
import shutil
from datetime import datetime
from typing import Optional

class FileManager:
    """
    文件管理器类
    负责按照年月和箱号/单号_时间戳的结构组织文件存储
    """
    
    def __init__(self, base_dir: str = "files"):
        """
        初始化文件管理器
        
        Args:
            base_dir: 基础目录名称，默认为"files"
        """
        self.base_dir = base_dir
        self._ensure_base_dir()
    
    def _ensure_base_dir(self):
        """确保基础目录存在"""
        if not os.path.exists(self.base_dir):
            os.makedirs(self.base_dir)
            print(f"📁 创建基础目录: {self.base_dir}")
    
    def get_storage_path(self, tracking_number: str, timestamp: Optional[datetime] = None) -> str:
        """
        获取存储路径
        
        Args:
            tracking_number: 箱号或单号
            timestamp: 时间戳，如果不提供则使用当前时间
            
        Returns:
            str: 完整的存储路径
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        # 年月文件夹格式：YYYY-MM
        year_month = timestamp.strftime("%Y-%m")
        
        # 时间戳格式：YYYYMMDD_HHMMSS
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
        
        # 箱号/单号_时间戳文件夹
        folder_name = f"{tracking_number}_{timestamp_str}"
        
        # 完整路径：files/YYYY-MM/箱号_时间戳/
        full_path = os.path.join(self.base_dir, year_month, folder_name)
        
        # 确保目录存在
        if not os.path.exists(full_path):
            os.makedirs(full_path)
            print(f"📁 创建存储目录: {full_path}")
        
        return full_path
    
    def save_file(self, tracking_number: str, file_path: str, new_filename: Optional[str] = None, timestamp: Optional[datetime] = None) -> str:
        """
        保存文件到指定的存储结构中
        
        Args:
            tracking_number: 箱号或单号
            file_path: 源文件路径
            new_filename: 新文件名，如果不提供则使用原文件名
            timestamp: 时间戳，如果不提供则使用当前时间
            
        Returns:
            str: 保存后的文件完整路径
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"源文件不存在: {file_path}")
        
        # 获取存储目录
        storage_dir = self.get_storage_path(tracking_number, timestamp)
        
        # 确定目标文件名
        if new_filename is None:
            new_filename = os.path.basename(file_path)
        
        # 目标文件路径
        target_path = os.path.join(storage_dir, new_filename)
        
        # 复制文件
        shutil.copy2(file_path, target_path)
        print(f"📄 文件已保存: {target_path}")
        
        return target_path
    
    def save_content(self, tracking_number: str, content: str, filename: str, timestamp: Optional[datetime] = None) -> str:
        """
        保存文本内容到文件
        
        Args:
            tracking_number: 箱号或单号
            content: 文本内容
            filename: 文件名
            timestamp: 时间戳，如果不提供则使用当前时间
            
        Returns:
            str: 保存后的文件完整路径
        """
        # 获取存储目录
        storage_dir = self.get_storage_path(tracking_number, timestamp)
        
        # 目标文件路径
        target_path = os.path.join(storage_dir, filename)
        
        # 写入文件
        with open(target_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📄 内容已保存: {target_path}")
        return target_path
    
    def get_files_for_tracking(self, tracking_number: str) -> list:
        """
        获取指定箱号/单号的所有文件记录
        
        Args:
            tracking_number: 箱号或单号
            
        Returns:
            list: 包含该箱号/单号所有文件夹的列表
        """
        result = []
        
        if not os.path.exists(self.base_dir):
            return result
        
        # 遍历所有年月文件夹
        for year_month in os.listdir(self.base_dir):
            year_month_path = os.path.join(self.base_dir, year_month)
            if not os.path.isdir(year_month_path):
                continue
            
            # 遍历该年月下的所有文件夹
            for folder_name in os.listdir(year_month_path):
                if folder_name.startswith(f"{tracking_number}_"):
                    folder_path = os.path.join(year_month_path, folder_name)
                    if os.path.isdir(folder_path):
                        # 获取文件夹中的所有文件
                        files = []
                        for file_name in os.listdir(folder_path):
                            file_path = os.path.join(folder_path, file_name)
                            if os.path.isfile(file_path):
                                files.append({
                                    'name': file_name,
                                    'path': file_path,
                                    'size': os.path.getsize(file_path),
                                    'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                                })
                        
                        result.append({
                            'folder_name': folder_name,
                            'folder_path': folder_path,
                            'year_month': year_month,
                            'files': files,
                            'created': datetime.fromtimestamp(os.path.getctime(folder_path))
                        })
        
        # 按文件夹名称中的时间戳排序（最新的在前）
        def extract_timestamp_from_folder_name(folder_record):
            folder_name = folder_record['folder_name']
            # 提取时间戳部分，格式：箱号_YYYYMMDD_HHMMSS
            try:
                parts = folder_name.split('_')
                if len(parts) >= 3:
                    date_part = parts[-2]  # YYYYMMDD
                    time_part = parts[-1]  # HHMMSS
                    timestamp_str = f"{date_part}_{time_part}"
                    return datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            except (ValueError, IndexError):
                # 如果解析失败，使用文件夹创建时间作为备选
                return folder_record['created']
            return folder_record['created']
        
        result.sort(key=extract_timestamp_from_folder_name, reverse=True)
        return result
    
    def cleanup_old_files(self, days_to_keep: int = 30):
        """
        清理旧文件（可选功能）
        
        Args:
            days_to_keep: 保留天数，默认30天
        """
        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
        deleted_count = 0
        
        if not os.path.exists(self.base_dir):
            return deleted_count
        
        for year_month in os.listdir(self.base_dir):
            year_month_path = os.path.join(self.base_dir, year_month)
            if not os.path.isdir(year_month_path):
                continue
            
            for folder_name in os.listdir(year_month_path):
                folder_path = os.path.join(year_month_path, folder_name)
                if os.path.isdir(folder_path):
                    # 检查文件夹创建时间
                    if os.path.getctime(folder_path) < cutoff_time:
                        shutil.rmtree(folder_path)
                        deleted_count += 1
                        print(f"🗑️ 删除过期文件夹: {folder_path}")
            
            # 如果年月文件夹为空，也删除它
            if not os.listdir(year_month_path):
                os.rmdir(year_month_path)
                print(f"🗑️ 删除空的年月文件夹: {year_month_path}")
        
        print(f"🧹 清理完成，删除了 {deleted_count} 个过期文件夹")
        return deleted_count


# 全局文件管理器实例
file_manager = FileManager()


def get_file_manager() -> FileManager:
    """获取全局文件管理器实例"""
    return file_manager