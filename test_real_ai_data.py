#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实的AI数据测试修复后的 handle_task_completion
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from shipment_manager import ShipmentManager
from task_manager import TaskManager
import json

def test_with_real_ai_data():
    """使用真实AI数据测试"""
    print("=== 使用真实AI数据测试修复 ===")
    
    try:
        shipment_manager = ShipmentManager()
        task_manager = TaskManager()
        
        # 1. 获取一个有关联货运记录的pending任务
        pending_tasks = task_manager.get_pending_tasks(limit=10)
        
        test_task = None
        for task in pending_tasks:
            if '货运记录ID:' in task.get('remarks', ''):
                test_task = task
                break
        
        if not test_task:
            # 创建一个测试任务和记录
            print("[INFO] 创建测试货运记录...")
            record_id = shipment_manager.create_shipment_record(
                bill_of_lading="REAL_AI_TEST_001",
                container_number="REAL_CONT_001",
                carrier_company="MSC",
                estimated_arrival_time=None,
                remarks="真实AI数据测试",
                created_by="real_ai_test"
            )
            task_id = "test_task_id_001"
        else:
            task_id = test_task['id']
            record_id = test_task['remarks'].split('货运记录ID:')[1].split(',')[0].strip()
        
        print(f"[INFO] 测试任务ID: {task_id}")
        print(f"[INFO] 关联货运记录ID: {record_id}")
        
        # 2. 使用真实的AI分析结果数据（来自文件检查结果）
        real_ai_data = {
            "estimated_arrival_time": "2025-08-31",
            "estimated_arrival_port": "Shanghai CN",
            "dates": [  # 注意：这里是 "dates" 不是 "dates_data"
                {
                    "date": "2025-08-31",
                    "original_format": "31/08/2025",
                    "type": "POD_ETA",
                    "location": "Shanghai CN",
                    "description": "Estimated Time of Arrival",
                    "status": "estimated",
                    "vessel_info": "MSC UBERTY VIII GO535N",
                    "context": "31/08/2025 Shanghai, CN Estimated Time of Arrival MSC UBERTY VIII GO535N"
                },
                {
                    "date": "2025-08-05",
                    "original_format": "05.08.2025",
                    "type": "Tracking results",
                    "location": "",
                    "description": "Tracking results provided by MSC on 05.08.2025 at 13:19 Central Europe Standard Time",
                    "status": "",
                    "vessel_info": "",
                    "context": "Tracking results provided by MSC on 05.08.2025 at 13:19"
                }
            ]
        }
        
        print(f"[INFO] 使用真实AI数据格式: {len(real_ai_data['dates'])} 条时间节点")
        for i, date_item in enumerate(real_ai_data['dates']):
            print(f"  {i+1}. {date_item['date']} @ {date_item['location']}: {date_item['description']}")
        
        # 3. 检查处理前状态
        print(f"\\n[BEFORE] 检查处理前状态...")
        import sqlite3
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (record_id,))
        before_count = cursor.fetchone()[0]
        print(f"[BEFORE] dates记录数: {before_count}")
        conn.close()
        
        # 4. 调用修复后的 handle_task_completion
        print(f"\\n[PROCESS] 调用修复后的 handle_task_completion...")
        success = shipment_manager.handle_task_completion(task_id, real_ai_data)
        
        if success:
            print(f"[SUCCESS] 处理成功!")
            
            # 5. 检查结果
            print(f"\\n[AFTER] 检查处理结果...")
            conn = sqlite3.connect('db/shipment_records.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (record_id,))
            after_count = cursor.fetchone()[0]
            print(f"[AFTER] dates记录数: {after_count}")
            
            if after_count > before_count:
                print(f"[EXCELLENT] 成功插入了 {after_count - before_count} 条新的时间节点记录!")
                
                # 显示新记录详情
                cursor.execute("""
                    SELECT date, location, description, type, created_at 
                    FROM shipment_dates 
                    WHERE shipment_id = ? 
                    ORDER BY id DESC 
                    LIMIT ?
                """, (record_id, after_count - before_count))
                
                new_records = cursor.fetchall()
                print(f"\\n[DETAILS] 新插入的记录:")
                for record in new_records:
                    print(f"  - {record[0]} @ {record[1]}: {record[2]} ({record[3]})")
                    print(f"    创建时间: {record[4]}")
                    
                    if '+08:00' in str(record[4]):
                        print(f"    [OK] 正确使用东八区时间")
                    else:
                        print(f"    [WARNING] 时间格式问题: {record[4]}")
                
                # 检查主记录更新
                cursor.execute("SELECT estimated_arrival_time, updated_at FROM shipment_records WHERE id = ?", (record_id,))
                main_record = cursor.fetchone()
                if main_record:
                    print(f"\\n[MAIN_RECORD] 主记录更新:")
                    print(f"  预计到港时间: {main_record[0]}")
                    print(f"  记录更新时间: {main_record[1]}")
                
                return True
            else:
                print(f"[ERROR] 没有插入新的时间节点记录")
                return False
            
            conn.close()
        else:
            print(f"[ERROR] 处理失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("使用真实AI数据测试修复结果")
    print("=" * 70)
    
    success = test_with_real_ai_data()
    
    if success:
        print("\\n" + "=" * 70)
        print("[PERFECT] 真实AI数据处理成功!")
        print("修复完成:")
        print("1. 支持 'dates' 和 'dates_data' 两种格式")
        print("2. shipment_records 和 shipment_dates 都正确插入数据")
        print("3. 所有时间字段使用东八区时间")
        print("4. 真实业务流程现在应该正常工作")
    else:
        print("\\n" + "=" * 70)
        print("[ERROR] 还有问题需要解决")
    
    print("=" * 70)