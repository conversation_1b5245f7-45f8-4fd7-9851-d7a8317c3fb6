// 船期查询应用主类
class ShipmentApp {
    constructor() {
        this.tasks = [];
        this.currentPage = 'dashboard';
        this.dataManager = null;
        this.components = null;
        this.router = null;
    }

    // 初始化应用
    init() {
        this.initManagers();
        this.initFeatherIcons();
        this.initNavigation();
        this.loadData();
        
        // 默认加载工作台页面
        this.loadDashboard();
    }

    // 初始化Feather图标
    initFeatherIcons() {
        if (window.feather) {
            feather.replace();
        }
    }

    // 初始化导航
    initNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.router.navigate(page);
                this.updateNavigation(page);
            });
        });
    }

    // 更新导航状态
    updateNavigation(activePage) {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            const link = item.querySelector('.nav-link');
            const page = link.getAttribute('data-page');
            
            if (page === activePage) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
        
        this.currentPage = activePage;
    }

    // 加载数据
    loadData() {
        // 从本地存储加载数据
        const stored = this.dataManager.loadFromStorage();
        if (stored.tasks && stored.tasks.length > 0) {
            this.tasks = stored.tasks;
        } else {
            // 使用模拟数据
            this.tasks = this.dataManager.getMockTasks();
        }
    }

    // 保存任务数据
    saveTasks() {
        this.dataManager.saveToStorage({ tasks: this.tasks });
    }

    // 渲染任务表格行
    renderTaskTableRows() {
        if (!this.tasks || this.tasks.length === 0) {
            return `
                <tr>
                    <td colspan="7" class="no-data">
                        <div class="no-data-content">
                            <i data-feather="inbox"></i>
                            <p>暂无查询任务</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        return this.tasks.map(task => {
            const statusClass = this.getStatusClass(task.status);
            const statusText = this.getStatusText(task.status);
            
            return `
                <tr class="task-row" onclick="app.viewTaskDetail('${task.id}')">
                    <td class="task-id">${task.bl_number || task.id}</td>
                    <td>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </td>
                    <td class="vessel-info">
                        <div class="vessel-name">${task.vessel_name || 'MSC OSCAR'}</div>
                        <div class="voyage">${task.voyage || '240EA'}</div>
                    </td>
                    <td class="port">${task.origin_port || '上海港'}</td>
                    <td class="port">${task.destination_port || '洛杉矶港'}</td>
                    <td class="eta">${task.estimated_arrival || '2024-01-15'}</td>
                    <td class="update-time">${this.formatDateTime(task.updated_at || new Date())}</td>
                </tr>
            `;
        }).join('');
    }

    // 获取状态样式类
    getStatusClass(status) {
        const statusMap = {
            'completed': 'status-completed',
            'processing': 'status-processing', 
            'pending': 'status-pending',
            'error': 'status-error'
        };
        return statusMap[status] || 'status-pending';
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'completed': '已完成',
            'processing': '处理中',
            'pending': '等待中',
            'error': '查询失败'
        };
        return statusMap[status] || '等待中';
    }

    // 格式化日期时间
    formatDateTime(date) {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 执行搜索
    performSearch() {
        const searchInput = document.getElementById('search-input');
        const query = searchInput.value.trim();
        
        if (!query) {
            this.showNotification('warning', '提示', '请输入提单号或集装箱号');
            return;
        }

        // 创建新任务
        const newTask = {
            id: 'TASK_' + Date.now(),
            bl_number: query,
            status: 'processing',
            vessel_name: 'MSC OSCAR',
            voyage: '240EA',
            origin_port: '上海港',
            destination_port: '洛杉矶港',
            estimated_arrival: '2024-01-15',
            created_at: new Date(),
            updated_at: new Date()
        };

        this.tasks.unshift(newTask);
        this.saveTasks();
        this.refreshTaskTable();
        
        // 清空搜索框
        searchInput.value = '';
        
        this.showNotification('success', '查询已提交', `正在查询 ${query} 的船期信息...`);
        
        // 模拟查询过程
        setTimeout(() => {
            newTask.status = 'completed';
            newTask.updated_at = new Date();
            this.refreshTaskTable();
            this.showNotification('success', '查询完成', `${query} 的船期信息已更新`);
        }, 3000);
    }

    // 刷新任务表格
    refreshTaskTable() {
        const tbody = document.getElementById('task-table-body');
        if (tbody) {
            tbody.innerHTML = this.renderTaskTableRows();
            this.initFeatherIcons();
        }
    }

    // 显示加载动画
    showLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'flex';
        }
    }

    // 隐藏加载动画
    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'none';
        }
    }

    // 加载工作台页面
    loadDashboard() {
        const content = document.getElementById('page-content');
        // 兼容：如果当前页面没有 SPA 容器，直接跳过旧版工作台渲染，避免报错
        if (!content) {
            return;
        }
        content.innerHTML = `
            <div class="page-header">
                <div class="page-header-top">
                    <h1 class="page-title">
                        <i data-feather="anchor"></i>
                        船期查询工作台
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="app.router.navigate('create-task')">
                            <i data-feather="plus"></i>
                            新建任务
                        </button>
                    </div>
                </div>
                <div class="search-section">
                    <div class="search-input-group">
                        <label>提单号/集装箱号查询：如：MSKU1234567</label>
                        <div class="search-controls">
                            <input type="text" class="form-control" placeholder="请输入提单号或集装箱号查询" id="search-input">
                            <button class="btn btn-primary" onclick="app.performSearch()">
                                查询跟踪
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i data-feather="list"></i>
                        查询任务
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-secondary" onclick="app.refreshTasks()">
                            <i data-feather="refresh-cw"></i>
                            刷新
                        </button>
                        <button class="btn btn-secondary" onclick="app.filterTasks('all')">
                            全部
                        </button>
                    </div>
                </div>
                <div class="task-table-container">
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>编号</th>
                                <th>状态</th>
                                <th>船名/航次</th>
                                <th>起运港</th>
                                <th>目的港</th>
                                <th>预计到港</th>
                                <th>更新时间</th>
                            </tr>
                        </thead>
                        <tbody id="task-table-body">
                            ${this.renderTaskTableRows()}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i data-feather="list"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${this.tasks.length}</div>
                        <div class="stat-label">总任务数</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i data-feather="clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${this.tasks.filter(t => t.status === 'running').length}</div>
                        <div class="stat-label">执行中</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i data-feather="check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${this.tasks.filter(t => t.status === 'completed').length}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i data-feather="pause-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${this.tasks.filter(t => t.status === 'pending').length}</div>
                        <div class="stat-label">待执行</div>
                    </div>
                </div>
            </div>
            
            <!-- 新建任务区域 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i data-feather="plus-circle"></i>
                        快速新建任务
                    </h3>
                </div>
                
                <div class="quick-actions">
                    <button class="btn btn-primary btn-large" onclick="app.router.navigate('create-task')">
                        <i data-feather="plus"></i>
                        新建查询任务
                    </button>
                    <button class="btn btn-secondary" onclick="app.router.navigate('task-management')">
                        <i data-feather="list"></i>
                        管理任务
                    </button>
                    <button class="btn btn-secondary" onclick="app.router.navigate('results')">
                        <i data-feather="bar-chart-2"></i>
                        查看结果
                    </button>
                </div>
            </div>
            
            <!-- 快速查询模板 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i data-feather="zap"></i>
                        快速查询模板
                    </h3>
                </div>
                
                <div class="template-grid">
                    <div class="template-card" onclick="app.useTemplate('china-us')">
                        <div class="template-icon">
                            <i data-feather="anchor"></i>
                        </div>
                        <div class="template-title">中美航线</div>
                        <div class="template-desc">上海/深圳 → 洛杉矶/纽约</div>
                    </div>
                    
                    <div class="template-card" onclick="app.useTemplate('china-europe')">
                        <div class="template-icon">
                            <i data-feather="globe"></i>
                        </div>
                        <div class="template-title">中欧航线</div>
                        <div class="template-desc">宁波/青岛 → 汉堡/鹿特丹</div>
                    </div>
                    
                    <div class="template-card" onclick="app.useTemplate('intra-asia')">
                        <div class="template-icon">
                            <i data-feather="map"></i>
                        </div>
                        <div class="template-title">亚洲区域</div>
                        <div class="template-desc">中国港口 → 新加坡/日本</div>
                    </div>
                </div>
            </div>
        `;
        
        this.initFeatherIcons();
    }

    // 使用模板
    useTemplate(templateType) {
        this.router.navigate('create-task');
        
        // 延迟填充模板数据，确保页面已加载
        setTimeout(() => {
            this.fillTemplate(templateType);
        }, 100);
    }

    // 填充模板数据
    fillTemplate(templateType) {
        const templates = {
            'china-us': {
                name: '中美航线查询',
                originPort: 'CNSHA',
                destinationPort: 'USLAX',
                shippingLine: 'COSCO'
            },
            'china-europe': {
                name: '中欧航线查询',
                originPort: 'CNNGB',
                destinationPort: 'DEHAM',
                shippingLine: 'MAERSK'
            },
            'intra-asia': {
                name: '亚洲区域查询',
                originPort: 'CNSHA',
                destinationPort: 'SGSIN',
                shippingLine: ''
            }
        };
        
        const template = templates[templateType];
        if (template) {
            const form = document.getElementById('create-task-form');
            if (form) {
                form.querySelector('[name="taskName"]').value = template.name;
                form.querySelector('[name="originPort"]').value = template.originPort;
                form.querySelector('[name="destinationPort"]').value = template.destinationPort;
                form.querySelector('[name="shippingLine"]').value = template.shippingLine;
            }
        }
    }

    // 加载新建任务页面
    loadCreateTask() {
        const ports = this.dataManager.getPorts();
        const shippingLines = this.dataManager.getShippingLines();
        
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h1 class="page-title">
                    <i data-feather="plus-circle"></i>
                    新建查询任务
                </h1>
            </div>
            
            <div class="card">
                <form id="create-task-form">
                    <div class="form-group">
                        <label class="form-label">任务名称</label>
                        <input type="text" name="taskName" class="form-control" placeholder="请输入任务名称" required>
                    </div>
                    
                    <div class="grid grid-2">
                        <div class="form-group">
                            <label class="form-label">起始港</label>
                            <select name="originPort" class="form-control" required>
                                <option value="">请选择起始港</option>
                                ${Object.entries(ports).map(([code, name]) => 
                                    `<option value="${code}">${name} (${code})</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">目的港</label>
                            <select name="destinationPort" class="form-control" required>
                                <option value="">请选择目的港</option>
                                ${Object.entries(ports).map(([code, name]) => 
                                    `<option value="${code}">${name} (${code})</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-2">
                        <div class="form-group">
                            <label class="form-label">船公司 (可选)</label>
                            <select name="shippingLine" class="form-control">
                                <option value="">不限</option>
                                ${shippingLines.map(line => 
                                    `<option value="${line}">${line}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">查询日期范围</label>
                            <input type="date" name="startDate" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">备注 (可选)</label>
                        <textarea name="notes" class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i data-feather="play"></i>
                            创建并执行任务
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.router.navigate('dashboard')">
                            <i data-feather="arrow-left"></i>
                            返回工作台
                        </button>
                    </div>
                </form>
            </div>
        `;
        
        // 绑定表单提交事件
        const form = document.getElementById('create-task-form');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.createTask(new FormData(form));
        });
        
        this.initFeatherIcons();
    }

    // 创建任务
    createTask(formData) {
        const task = {
            id: this.components.generateId(),
            name: formData.get('taskName'),
            originPort: formData.get('originPort'),
            destinationPort: formData.get('destinationPort'),
            shippingLine: formData.get('shippingLine'),
            startDate: formData.get('startDate'),
            notes: formData.get('notes'),
            status: 'pending',
            progress: 0,
            createdAt: this.components.formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
            updatedAt: new Date().toISOString()
        };
        
        this.tasks.unshift(task);
        this.saveTasks();
        
        this.showNotification('success', '任务创建成功', `任务 "${task.name}" 已创建并开始执行`);
        
        // 开始执行任务
        this.simulateTaskExecution(task.id);
        
        // 跳转到任务管理页面
        this.router.navigate('task-management');
    }

    // 模拟任务执行
    simulateTaskExecution(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        task.status = 'running';
        task.progress = 0;
        
        const interval = setInterval(() => {
            task.progress += Math.random() * 15 + 5;
            
            if (task.progress >= 100) {
                task.progress = 100;
                task.status = 'completed';
                task.updatedAt = new Date().toISOString();
                
                // 生成模拟结果
                task.departureDate = '2024-02-15';
                task.arrivalDate = '2024-03-01';
                task.transitTime = '15天';
                
                clearInterval(interval);
                this.saveTasks();
                
                this.showNotification('success', '任务完成', `任务 "${task.name}" 执行完成`);
            } else {
                task.updatedAt = new Date().toISOString();
            }
            
            this.saveTasks();
            
            // 如果当前在任务管理页面，刷新显示
            if (this.currentPage === 'task-management') {
                this.loadTaskManagement();
            }
        }, 2000 + Math.random() * 3000);
    }

    // 加载任务管理页面
    loadTaskManagement() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h1 class="page-title">
                    <i data-feather="list"></i>
                    任务管理
                </h1>
                <button class="btn btn-primary" onclick="app.router.navigate('create-task')">
                    <i data-feather="plus"></i>
                    新建任务
                </button>
            </div>
            
            <div class="task-filters">
                <div class="filter-group">
                    <button class="filter-btn active" data-status="all">全部 (${this.tasks.length})</button>
                    <button class="filter-btn" data-status="running">执行中 (${this.tasks.filter(t => t.status === 'running').length})</button>
                    <button class="filter-btn" data-status="completed">已完成 (${this.tasks.filter(t => t.status === 'completed').length})</button>
                    <button class="filter-btn" data-status="pending">待执行 (${this.tasks.filter(t => t.status === 'pending').length})</button>
                </div>
                
                <button class="btn btn-secondary" onclick="app.refreshTasks()">
                    <i data-feather="refresh-cw"></i>
                    刷新
                </button>
            </div>
            
            <div class="task-list">
                ${this.tasks.length === 0 ? `
                    <div class="empty-state">
                        <i data-feather="inbox"></i>
                        <h3>暂无任务</h3>
                        <p>点击上方按钮创建您的第一个查询任务</p>
                        <button class="btn btn-primary" onclick="app.router.navigate('create-task')">
                            <i data-feather="plus"></i>
                            新建任务
                        </button>
                    </div>
                ` : this.tasks.map(task => this.renderTaskCard(task)).join('')}
            </div>
        `;
        
        // 绑定筛选按钮事件
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.filterTasks(btn.getAttribute('data-status'));
            });
        });
        
        this.initFeatherIcons();
    }

    // 渲染任务卡片
    renderTaskCard(task) {
        const statusMap = {
            pending: { text: '待执行', class: 'status-pending', icon: 'clock' },
            running: { text: '执行中', class: 'status-running', icon: 'play-circle' },
            completed: { text: '已完成', class: 'status-completed', icon: 'check-circle' },
            failed: { text: '执行失败', class: 'status-failed', icon: 'x-circle' }
        };
        
        const status = statusMap[task.status] || statusMap.pending;
        
        return `
            <div class="task-card" data-task-id="${task.id}">
                <div class="task-header">
                    <div class="task-title">
                        <h3>${task.name}</h3>
                        <span class="task-status ${status.class}">
                            <i data-feather="${status.icon}"></i>
                            ${status.text}
                        </span>
                    </div>
                    
                    <div class="task-actions">
                        <button class="btn-icon" onclick="app.viewTaskDetail('${task.id}')" title="查看详情">
                            <i data-feather="eye"></i>
                        </button>
                        <button class="btn-icon" onclick="app.deleteTask('${task.id}')" title="删除任务">
                            <i data-feather="trash-2"></i>
                        </button>
                    </div>
                </div>
                
                <div class="task-info">
                    <div class="task-route">
                        <span class="port">${task.originPort}</span>
                        <i data-feather="arrow-right"></i>
                        <span class="port">${task.destinationPort}</span>
                    </div>
                    
                    <div class="task-meta">
                        <span><i data-feather="ship"></i> ${task.shippingLine || '不限'}</span>
                        <span><i data-feather="calendar"></i> ${task.createdAt}</span>
                    </div>
                </div>
                
                ${task.status === 'running' ? `
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${task.progress}%"></div>
                        </div>
                        <span class="progress-text">${Math.round(task.progress)}%</span>
                    </div>
                ` : ''}
                
                ${task.status === 'completed' ? `
                    <div class="task-result">
                        <div class="result-item">
                            <span class="label">开船日期:</span>
                            <span class="value">${task.departureDate}</span>
                        </div>
                        <div class="result-item">
                            <span class="label">到港日期:</span>
                            <span class="value">${task.arrivalDate}</span>
                        </div>
                        <div class="result-item">
                            <span class="label">运输时间:</span>
                            <span class="value">${task.transitTime}</span>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // 筛选任务
    filterTasks(status) {
        const taskCards = document.querySelectorAll('.task-card');
        
        taskCards.forEach(card => {
            const taskId = card.getAttribute('data-task-id');
            const task = this.tasks.find(t => t.id === taskId);
            
            if (status === 'all' || task.status === status) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    // 查看任务详情
    viewTaskDetail(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h1 class="page-title">
                    <i data-feather="eye"></i>
                    任务详情
                </h1>
                <button class="btn btn-secondary" onclick="app.router.navigate('task-management')">
                    <i data-feather="arrow-left"></i>
                    返回列表
                </button>
            </div>
            
            <div class="task-detail">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i data-feather="info"></i>
                            基本信息
                        </h3>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">任务名称</label>
                        <div class="form-control-static">${task.name}</div>
                    </div>
                    
                    <div class="grid grid-2">
                        <div class="form-group">
                            <label class="form-label">起始港</label>
                            <div class="form-control-static">${task.originPort}</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目的港</label>
                            <div class="form-control-static">${task.destinationPort}</div>
                        </div>
                    </div>
                    
                    <div class="grid grid-2">
                        <div class="form-group">
                            <label class="form-label">船公司</label>
                            <div class="form-control-static">${task.shippingLine || '不限'}</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创建时间</label>
                            <div class="form-control-static">${task.createdAt}</div>
                        </div>
                    </div>
                    
                    ${task.notes ? `
                        <div class="form-group">
                            <label class="form-label">备注</label>
                            <div class="form-control-static">${task.notes}</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        this.initFeatherIcons();
    }

    // 删除任务
    deleteTask(taskId) {
        this.components.showConfirm(
            '确认删除',
            '确定要删除这个任务吗？此操作不可撤销。',
            () => {
                this.tasks = this.tasks.filter(t => t.id !== taskId);
                this.saveTasks();
                this.showNotification('success', '删除成功', '任务已删除');
                
                if (this.currentPage === 'task-management') {
                    this.loadTaskManagement();
                } else {
                    this.router.navigate('task-management');
                }
            }
        );
    }

    // 加载查询结果页面
    loadResults() {
        const completedTasks = this.tasks.filter(t => t.status === 'completed');
        
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h1 class="page-title">
                    <i data-feather="bar-chart-2"></i>
                    查询结果
                </h1>
            </div>
            
            ${completedTasks.length === 0 ? `
                <div class="empty-state">
                    <i data-feather="search"></i>
                    <h3>暂无查询结果</h3>
                    <p>完成查询任务后，结果将在这里显示</p>
                    <button class="btn btn-primary" onclick="app.router.navigate('create-task')">
                        <i data-feather="plus"></i>
                        创建查询任务
                    </button>
                </div>
            ` : `
                <div class="results-grid">
                    ${completedTasks.map(task => `
                        <div class="result-card">
                            <div class="result-header">
                                <h3>${task.name}</h3>
                                <span class="result-date">${task.updatedAt.split('T')[0]}</span>
                            </div>
                            
                            <div class="result-route">
                                <span class="port">${task.originPort}</span>
                                <i data-feather="arrow-right"></i>
                                <span class="port">${task.destinationPort}</span>
                            </div>
                            
                            <div class="result-details">
                                <div class="detail-item">
                                    <span class="label">开船日期</span>
                                    <span class="value">${task.departureDate}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">到港日期</span>
                                    <span class="value">${task.arrivalDate}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">运输时间</span>
                                    <span class="value">${task.transitTime}</span>
                                </div>
                            </div>
                            
                            <div class="result-actions">
                                <button class="btn btn-primary btn-sm" onclick="app.exportTaskResult('${task.id}')">
                                    <i data-feather="download"></i>
                                    导出
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="app.viewTaskDetail('${task.id}')">
                                    <i data-feather="eye"></i>
                                    详情
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `}
        `;
        
        this.initFeatherIcons();
    }

    // 导出任务结果
    exportTaskResult(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task || task.status !== 'completed') {
            this.showNotification('error', '导出失败', '任务未完成或不存在');
            return;
        }
        
        const result = {
            taskInfo: {
                name: task.name,
                originPort: task.originPort,
                destinationPort: task.destinationPort,
                shippingLine: task.shippingLine,
                createdAt: task.createdAt,
                completedAt: task.updatedAt
            },
            result: {
                departureDate: task.departureDate,
                arrivalDate: task.arrivalDate,
                transitTime: task.transitTime
            },
            exportTime: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(result, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${task.name}_result_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('success', '导出成功', '任务结果已导出');
    }

    // 加载设置页面
    loadSettings() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h1 class="page-title">
                    <i data-feather="settings"></i>
                    设置
                </h1>
            </div>
            
            <div class="settings-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i data-feather="database"></i>
                            数据管理
                        </h3>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>导出数据</h4>
                            <p>导出所有任务数据到本地文件</p>
                        </div>
                        <button class="btn btn-secondary" onclick="app.exportAllData()">
                            <i data-feather="download"></i>
                            导出
                        </button>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>清除数据</h4>
                            <p>清除所有本地存储的任务数据</p>
                        </div>
                        <button class="btn btn-danger" onclick="app.clearAllData()">
                            <i data-feather="trash-2"></i>
                            清除
                        </button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i data-feather="info"></i>
                            应用信息
                        </h3>
                    </div>
                    
                    <div class="app-info">
                        <div class="info-item">
                            <span class="label">应用名称</span>
                            <span class="value">船期查询工作台</span>
                        </div>
                        <div class="info-item">
                            <span class="label">版本</span>
                            <span class="value">1.0.0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">总任务数</span>
                            <span class="value">${this.tasks.length}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">已完成任务</span>
                            <span class="value">${this.tasks.filter(t => t.status === 'completed').length}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.initFeatherIcons();
    }

    // 导出所有数据
    exportAllData() {
        this.dataManager.exportData();
        this.showNotification('success', '导出成功', '数据已导出到本地文件');
    }

    // 清除所有数据
    clearAllData() {
        this.components.showConfirm(
            '确认清除',
            '确定要清除所有数据吗？此操作不可撤销，建议先导出数据备份。',
            () => {
                this.dataManager.clearAllData();
                this.tasks = [];
                this.showNotification('success', '清除成功', '所有数据已清除');
                this.router.navigate('dashboard');
            }
        );
    }

    // 刷新任务列表
    refreshTasks() {
        this.showNotification('info', '刷新中', '正在刷新任务列表...');
        
        setTimeout(() => {
            this.loadTaskManagement();
            this.showNotification('success', '刷新完成', '任务列表已更新');
        }, 1000);
    }

    // 显示通知
    showNotification(type, title, message) {
        this.components.showNotification(type, title, message);
    }

    // 初始化数据管理器和组件管理器
    initManagers() {
        this.dataManager = new DataManager();
        this.components = new ComponentManager();
        this.router = new Router();
        
        // 注册路由
        this.router.register('dashboard', () => this.loadDashboard());
        this.router.register('create-task', () => this.loadCreateTask());
        this.router.register('task-management', () => this.loadTaskManagement());
        this.router.register('results', () => this.loadResults());
        this.router.register('settings', () => this.loadSettings());
    }
}

// 全局变量
let app;

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app = new ShipmentApp();
    window.app = app;
    app.init();
});