#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理器测试脚本
测试新的文件管理系统是否正常工作
"""

import os
import sys
from datetime import datetime
from utils.file_manager import get_file_manager

def test_file_manager():
    """
    测试文件管理器的各项功能
    """
    print("🧪 开始测试文件管理器...")
    
    # 获取文件管理器实例
    file_mgr = get_file_manager()
    
    # 测试数据
    test_tracking_number = "TEST123456"
    test_content = "这是一个测试文件内容\n测试时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    try:
        # 1. 测试保存文本内容
        print("\n📝 测试保存文本内容...")
        txt_path = file_mgr.save_content(
            tracking_number=test_tracking_number,
            content=test_content,
            filename="test_content.txt"
        )
        print(f"✅ 文本文件保存成功: {txt_path}")
        
        # 2. 测试创建临时文件并保存
        print("\n📸 测试保存文件...")
        temp_file = "temp_test_image.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write("这是一个临时测试文件")
        
        saved_file_path = file_mgr.save_file(
            tracking_number=test_tracking_number,
            file_path=temp_file,
            new_filename="test_image.txt"
        )
        os.remove(temp_file)  # 删除临时文件
        print(f"✅ 文件保存成功: {saved_file_path}")
        
        # 3. 测试获取文件列表
        print("\n📋 测试获取文件列表...")
        files_list = file_mgr.get_files_for_tracking(test_tracking_number)
        print(f"✅ 找到 {len(files_list)} 个文件记录")
        
        for i, record in enumerate(files_list):
            print(f"  记录 {i+1}:")
            print(f"    文件夹: {record['folder_name']}")
            print(f"    路径: {record['folder_path']}")
            print(f"    年月: {record['year_month']}")
            print(f"    创建时间: {record['created']}")
            print(f"    文件数量: {len(record['files'])}")
            for file_info in record['files']:
                print(f"      - {file_info['name']} ({file_info['size']} bytes)")
        
        # 4. 测试文件夹结构
        print("\n📁 检查文件夹结构...")
        if os.path.exists("files"):
            print("✅ files 基础目录存在")
            
            # 列出年月文件夹
            year_months = [d for d in os.listdir("files") if os.path.isdir(os.path.join("files", d))]
            print(f"📅 年月文件夹: {year_months}")
            
            for ym in year_months:
                ym_path = os.path.join("files", ym)
                tracking_folders = [d for d in os.listdir(ym_path) if os.path.isdir(os.path.join(ym_path, d))]
                print(f"  {ym}: {len(tracking_folders)} 个跟踪号文件夹")
                for tf in tracking_folders[:3]:  # 只显示前3个
                    tf_path = os.path.join(ym_path, tf)
                    files_count = len([f for f in os.listdir(tf_path) if os.path.isfile(os.path.join(tf_path, f))])
                    print(f"    - {tf} ({files_count} 个文件)")
        else:
            print("❌ files 基础目录不存在")
        
        print("\n🎉 文件管理器测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_existing_files():
    """
    测试与现有文件的集成
    """
    print("\n🔗 测试与现有文件的集成...")
    
    # 检查当前目录中的现有文件
    existing_files = [
        "ai_analysis_result_MEDUJ0618622.txt",
        "ai_analysis_result_TEST123456.txt",
        "page_content_original_MEDUJ0618622.txt",
        "page_content_original_TEST123456.txt",
        "final_result.png"
    ]
    
    file_mgr = get_file_manager()
    
    for file_name in existing_files:
        if os.path.exists(file_name):
            print(f"📄 发现现有文件: {file_name}")
            
            # 尝试从文件名中提取跟踪号
            if "MEDUJ0618622" in file_name:
                tracking_number = "MEDUJ0618622"
            elif "TEST123456" in file_name:
                tracking_number = "TEST123456"
            else:
                continue
            
            try:
                # 将现有文件迁移到新的文件管理结构中
                new_path = file_mgr.save_file(
                    tracking_number=tracking_number,
                    file_path=file_name,
                    new_filename=file_name
                )
                print(f"  ✅ 已迁移到: {new_path}")
                
                # 可选：删除原文件（注释掉以保持安全）
                # os.remove(file_name)
                # print(f"  🗑️ 已删除原文件: {file_name}")
                
            except Exception as e:
                print(f"  ❌ 迁移失败: {e}")

def show_file_structure():
    """
    显示当前的文件结构
    """
    print("\n📊 当前文件结构:")
    
    if not os.path.exists("files"):
        print("❌ files 目录不存在")
        return
    
    def print_tree(path, prefix=""):
        items = sorted(os.listdir(path))
        for i, item in enumerate(items):
            item_path = os.path.join(path, item)
            is_last = i == len(items) - 1
            current_prefix = "└── " if is_last else "├── "
            
            if os.path.isdir(item_path):
                print(f"{prefix}{current_prefix}{item}/")
                next_prefix = prefix + ("    " if is_last else "│   ")
                print_tree(item_path, next_prefix)
            else:
                size = os.path.getsize(item_path)
                print(f"{prefix}{current_prefix}{item} ({size} bytes)")
    
    print("files/")
    print_tree("files", "")

if __name__ == "__main__":
    print("🚀 文件管理器测试程序")
    print("=" * 50)
    
    # 运行基本测试
    success = test_file_manager()
    
    if success:
        # 显示文件结构
        show_file_structure()
        
        # 测试与现有文件的集成
        test_integration_with_existing_files()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        print("\n💡 提示:")
        print("  - 文件现在按照 files/年月/箱号_时间戳/ 的结构组织")
        print("  - 可以使用 file_mgr.get_files_for_tracking('箱号') 查询特定箱号的所有文件")
        print("  - 可以使用 file_mgr.cleanup_old_files(30) 清理30天前的文件")
    else:
        print("\n❌ 测试失败，请检查错误信息")