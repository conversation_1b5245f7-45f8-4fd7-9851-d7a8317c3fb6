#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试 shipment_records.updated_at 字段的东八区时间
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from shipment_manager import ShipmentManager, get_beijing_time, get_beijing_time_str

def test_updated_at_field():
    """专门测试 shipment_records 表的 updated_at 字段"""
    print("=" * 70)
    print("测试 shipment_records.updated_at 字段的东八区时间")
    print("=" * 70)
    
    try:
        manager = ShipmentManager()
        
        # 1. 创建记录
        print("\\n[TEST 1] 创建货运记录...")
        before_create = get_beijing_time_str()
        
        record_id = manager.create_shipment_record(
            bill_of_lading="UPDATED_AT_TEST_001",
            container_number="UPDATED_AT_CONT_001",
            carrier_company="MSC",
            estimated_arrival_time=get_beijing_time(),
            remarks="updated_at字段测试记录",
            created_by="updated_at_test_user"
        )
        
        after_create = get_beijing_time_str()
        print(f"[TIME] 创建前: {before_create}")
        print(f"[TIME] 创建后: {after_create}")
        print(f"[SUCCESS] 记录创建成功，ID: {record_id}")
        
        # 2. 测试更新预计到港时间
        print("\\n[TEST 2] 更新预计到港时间...")
        before_update_eta = get_beijing_time_str()
        
        new_eta = get_beijing_time_str()
        manager._update_estimated_arrival_time(record_id, new_eta)
        
        after_update_eta = get_beijing_time_str()
        print(f"[TIME] 更新ETA前: {before_update_eta}")
        print(f"[TIME] 更新ETA后: {after_update_eta}")
        
        # 3. 测试更新状态
        print("\\n[TEST 3] 更新记录状态...")
        before_update_status = get_beijing_time_str()
        
        manager.update_shipment_status(record_id, "处理中", "updated_at_test_user")
        
        after_update_status = get_beijing_time_str()
        print(f"[TIME] 更新状态前: {before_update_status}")
        print(f"[TIME] 更新状态后: {after_update_status}")
        
        # 4. 直接查询数据库验证时间
        print("\\n[TEST 4] 直接查询数据库验证时间...")
        import sqlite3
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, bill_of_lading, created_at, updated_at, status
            FROM shipment_records 
            WHERE id = ?
        """, (record_id,))
        
        record = cursor.fetchone()
        if record:
            print(f"[DB] 记录ID: {record[0]}")
            print(f"[DB] 提单号: {record[1]}")
            print(f"[DB] 创建时间: {record[2]}")
            print(f"[DB] 更新时间: {record[3]}")
            print(f"[DB] 状态: {record[4]}")
            
            # 检查时间格式
            created_at = record[2]
            updated_at = record[3]
            
            print(f"\\n[VERIFY] 时间格式验证:")
            print(f"  - 创建时间包含+08:00: {'+08:00' in str(created_at)}")
            print(f"  - 更新时间包含+08:00: {'+08:00' in str(updated_at)}")
            
            if '+08:00' in str(created_at) and '+08:00' in str(updated_at):
                print(f"[SUCCESS] 所有时间都正确使用东八区时间！")
                result = True
            else:
                print(f"[ERROR] 时间格式不正确，可能不是东八区时间")
                result = False
        else:
            print(f"[ERROR] 未找到测试记录")
            result = False
        
        conn.close()
        
        # 5. 测试多次更新
        print("\\n[TEST 5] 测试多次更新是否都使用东八区时间...")
        for i in range(3):
            print(f"  第 {i+1} 次更新...")
            before_update = get_beijing_time_str()
            
            manager.update_shipment_status(record_id, f"测试状态{i+1}", "multi_update_test")
            
            after_update = get_beijing_time_str()
            print(f"    更新前: {before_update}")
            print(f"    更新后: {after_update}")
            
            # 验证数据库中的时间
            conn = sqlite3.connect('db/shipment_records.db')
            cursor = conn.cursor()
            cursor.execute("SELECT updated_at FROM shipment_records WHERE id = ?", (record_id,))
            db_updated_at = cursor.fetchone()[0]
            conn.close()
            
            print(f"    数据库时间: {db_updated_at}")
            print(f"    包含+08:00: {'+08:00' in str(db_updated_at)}")
        
        return result
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_database_updated_at():
    """验证数据库中所有 updated_at 字段"""
    print("\\n" + "=" * 70)
    print("验证数据库中所有 updated_at 字段")
    print("=" * 70)
    
    try:
        import sqlite3
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 查询最近的记录
        cursor.execute("""
            SELECT id, bill_of_lading, created_at, updated_at, status
            FROM shipment_records 
            WHERE bill_of_lading LIKE '%TEST%' OR bill_of_lading LIKE '%WORKFLOW%'
            ORDER BY id DESC LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        correct_count = 0
        total_count = len(records)
        
        for record in records:
            record_id, bl_number, created_at, updated_at, status = record
            
            print(f"\\n[RECORD] ID: {record_id}, 提单号: {bl_number}")
            print(f"  创建时间: {created_at}")
            print(f"  更新时间: {updated_at}")
            print(f"  状态: {status}")
            
            created_ok = '+08:00' in str(created_at) if created_at else False
            updated_ok = '+08:00' in str(updated_at) if updated_at else False
            
            print(f"  创建时间格式: {'正确' if created_ok else '错误'}")
            print(f"  更新时间格式: {'正确' if updated_ok else '错误'}")
            
            if created_ok and updated_ok:
                correct_count += 1
        
        conn.close()
        
        print(f"\\n[SUMMARY] 检查结果: {correct_count}/{total_count} 条记录时间格式正确")
        
        if correct_count == total_count:
            print("[EXCELLENT] 所有记录的时间字段都正确使用东八区时间！")
            return True
        else:
            print(f"[WARNING] 还有 {total_count - correct_count} 条记录的时间格式不正确")
            return False
        
    except Exception as e:
        print(f"[ERROR] 验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("[TEST] 开始 updated_at 字段专项测试...")
    print(f"[TIME] 测试开始时间: {get_beijing_time_str()}")
    
    # 运行测试
    test_result = test_updated_at_field()
    verify_result = verify_database_updated_at()
    
    print("\\n" + "=" * 70)
    print("updated_at 字段测试结果汇总")
    print("=" * 70)
    
    print(f"[RESULT] 功能测试: {'✅ 通过' if test_result else '❌ 失败'}")
    print(f"[RESULT] 数据验证: {'✅ 通过' if verify_result else '❌ 失败'}")
    
    if test_result and verify_result:
        print("\\n[PERFECT] 🎉 updated_at 字段完美修复！")
        print("[INFO] 现在 shipment_records.updated_at 完全使用东八区时间")
        print("[INFO] 不再被任何触发器或系统时间覆盖")
    else:
        print("\\n[WARNING] ⚠️  还有问题需要解决")
    
    print("=" * 70)

if __name__ == "__main__":
    main()