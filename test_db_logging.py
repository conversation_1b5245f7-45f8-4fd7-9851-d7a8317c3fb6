#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库日志测试脚本
用于验证数据库操作日志是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from task_manager import TaskManager, create_bill_of_lading_task
from shipment_manager import ShipmentManager

def test_task_manager_logging():
    """测试TaskManager的数据库日志"""
    print("=" * 60)
    print("测试 TaskManager 数据库操作日志")
    print("=" * 60)
    
    try:
        manager = TaskManager()
        
        # 测试创建任务
        print("\n[TEST] 1. 测试创建任务...")
        task_id = create_bill_of_lading_task(
            bl_number="TEST_BL_123456",
            creator_id="test_user",
            creator_name="测试用户",
            carrier="MSC",
            priority=1,
            remarks="数据库日志测试任务"
        )
        print(f"[TEST] 任务创建成功，ID: {task_id}")
        
        # 测试获取任务详情
        print("\n[TEST] 2. 测试获取任务详情...")
        task = manager.get_task_by_id(task_id)
        if task:
            print(f"[TEST] 获取任务成功: {task['task_name']}")
        else:
            print("[TEST] 获取任务失败")
        
        # 测试更新任务状态
        print("\n[TEST] 3. 测试更新任务状态...")
        success = manager.update_task_status(task_id, "processing")
        print(f"[TEST] 状态更新结果: {success}")
        
        # 测试获取待处理任务
        print("\n[TEST] 4. 测试获取待处理任务...")
        pending_tasks = manager.get_pending_tasks(limit=5)
        print(f"[TEST] 获取到 {len(pending_tasks)} 个待处理任务")
        
        # 测试获取统计信息
        print("\n[TEST] 5. 测试获取统计信息...")
        stats = manager.get_task_statistics()
        print(f"[TEST] 统计信息获取成功: {stats}")
        
        # 测试完成任务
        print("\n[TEST] 6. 测试完成任务...")
        success = manager.update_task_status(
            task_id, 
            "completed", 
            result_summary="测试完成"
        )
        print(f"[TEST] 任务完成状态更新结果: {success}")
        
    except Exception as e:
        print(f"[ERROR] TaskManager测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_shipment_manager_logging():
    """测试ShipmentManager的数据库日志"""
    print("\n" + "=" * 60)
    print("测试 ShipmentManager 数据库操作日志")
    print("=" * 60)
    
    try:
        manager = ShipmentManager()
        
        # 测试创建货运记录
        print("\n[TEST] 1. 测试创建货运记录...")
        record_id = manager.create_shipment_record(
            bill_of_lading="TEST_BL_789012",
            container_number="TEST_CONT_345678",
            carrier_company="MSC",
            estimated_arrival_time=datetime.now(),
            remarks="数据库日志测试货运记录",
            created_by="test_user"
        )
        print(f"[TEST] 货运记录创建成功，ID: {record_id}")
        
        # 测试添加时间节点
        print("\n[TEST] 2. 测试添加时间节点...")
        test_dates = [
            {
                'date': '2025-01-15 10:00:00',
                'location': '上海港',
                'event': '装船',
                'status': '已完成',
                'event_type': 'departure',
                'vessel_info': {'name': 'MSC OSCAR', 'voyage': 'MSC001'}
            },
            {
                'date': '2025-01-25 14:30:00',
                'location': '洛杉矶港',
                'event': '到港',
                'status': '预计',
                'event_type': 'arrival',
                'vessel_info': {'name': 'MSC OSCAR', 'voyage': 'MSC001'}
            }
        ]
        
        success = manager.add_shipment_dates(record_id, test_dates)
        print(f"[TEST] 时间节点添加结果: {success}")
        
    except Exception as e:
        print(f"[ERROR] ShipmentManager测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("[TEST] 开始数据库日志测试...")
    print(f"[TEST] 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test_task_manager_logging()
    test_shipment_manager_logging()
    
    print("\n" + "=" * 60)
    print("[SUCCESS] 数据库日志测试完成")
    print("[INFO] 请检查上述日志输出，确认包含以下信息：")
    print("   - [DB_LOG] 正在连接数据库: xxx.db")
    print("   - [DB_LOG] SQL语句: INSERT/UPDATE/SELECT...")
    print("   - [DB_LOG] 参数: (param1, param2, ...)")  
    print("   - [DB_LOG] 执行结果: 影响行数/返回行数")
    print("   - [DB_LOG] 事务提交成功/回滚完成")
    print("   - [DB_LOG] 数据库连接已关闭")
    print("=" * 60)

if __name__ == "__main__":
    main()