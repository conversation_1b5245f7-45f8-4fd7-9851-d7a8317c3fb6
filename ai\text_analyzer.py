#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本AI分析模块
处理HTML内容分析和物流信息提取
"""

import os
import json
from datetime import datetime
from typing import Dict, Any
from bs4 import BeautifulSoup, Comment
import re

# 修复导入问题，使用绝对导入
try:
    from ai.client import get_ai_client
    from ai.prompt_config import get_text_analyzer_prompt
except ImportError:
    # 相对导入作为备选
    from .client import get_ai_client
    from .prompt_config import get_text_analyzer_prompt

from db_logger import log_ai_call_simple
from utils.file_manager import get_file_manager


class TextAnalyzer:
    """文本AI分析器类"""
    
    def __init__(self):
        """初始化文本分析器"""
        print("[INIT] TextAnalyzer初始化")
    
    def analyze_tracking_data(self, analysis_data: Dict) -> Dict:
        """
        分析货运跟踪数据
        
        Args:
            analysis_data: 分析数据，包含:
                - tracking_number: 跟踪号
                - screenshot_path: 截图路径
                - html_files: HTML文件列表
                - data_folder: 数据文件夹路径
        
        Returns:
            Dict: 分析结果 {'success': bool, 'data': str, 'error': str}
        """
        try:
            tracking_number = analysis_data.get('tracking_number', '')
            screenshot_path = analysis_data.get('screenshot_path', '')
            html_files = analysis_data.get('html_files', [])
            data_folder = analysis_data.get('data_folder', '')
            
            print(f"[AI] 分析货运数据: {tracking_number}")
            print(f"[AI] 截图路径: {screenshot_path}")
            print(f"[AI] HTML文件数量: {len(html_files)}")
            
            # 优先使用简化后的HTML内容进行分析
            html_content = ""
            selected_file = None
            
            if html_files:
                # 优先查找简化后的HTML文件
                simplified_files = [f for f in html_files if 'simplified' in f]
                original_files = [f for f in html_files if 'original' in f and 'simplified' not in f]
                
                # 优先使用简化文件，如果没有则使用原始文件
                target_files = simplified_files if simplified_files else original_files
                
                if target_files:
                    selected_file = target_files[0]
                    if os.path.exists(selected_file):
                        with open(selected_file, 'r', encoding='utf-8') as f:
                            html_content = f.read()
                        file_type = "简化" if 'simplified' in selected_file else "原始"
                        print(f"[AI] 使用{file_type}HTML文件: {os.path.basename(selected_file)} (长度: {len(html_content)})")
                    else:
                        print(f"[WARNING] HTML文件不存在: {selected_file}")
            
            if not html_content:
                print("[WARNING] 没有可用的HTML内容，无法进行AI分析")
                return {
                    'success': False,
                    'error': '没有可用的HTML内容进行分析'
                }
            
            # 调用现有的AI分析函数
            ai_result = analyze_shipment_dates(html_content, tracking_number)
            
            if ai_result:
                return {
                    'success': True,
                    'data': ai_result
                }
            else:
                return {
                    'success': False,
                    'error': 'AI分析返回空结果'
                }
                
        except Exception as e:
            print(f"[ERROR] AI分析异常: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': f'AI分析异常: {str(e)}'
            }

def simplify_html_for_logistics_ai(html_content: str) -> str:
    """
    简化HTML内容，专门为物流AI分析优化
    确保简化后的内容不会比原始内容更多
    
    Args:
        html_content: 原始HTML内容
        
    Returns:
        简化后的HTML内容
    """
    try:
        original_length = len(html_content)
        
        # 如果原始内容很短，直接返回
        if original_length < 1000:
            return html_content
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 移除不必要的标签
        for tag in soup.find_all(['script', 'style', 'meta', 'link', 'noscript', 'head']):
            tag.decompose()
        
        # 移除HTML注释
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # 查找包含物流信息的表格
        logistics_tables = []
        
        # 查找所有表格
        tables = soup.find_all('table')
        for table in tables:
            table_text = table.get_text().lower()
            # 检查表格是否包含物流相关关键词
            logistics_keywords = [
                'eta', 'etd', 'ata', 'atd', 'departure', 'arrival', 'port', 'vessel',
                'container', 'shipment', 'cargo', 'loading', 'discharge', 'transshipment',
                '到港', '离港', '装船', '卸货', '转运', '预计', '实际', '港口', '船期',
                'pod', 'pol', 'date', 'time', 'schedule', 'status'
            ]
            
            if any(keyword in table_text for keyword in logistics_keywords):
                cleaned_table = clean_table_content(table)
                if cleaned_table and len(cleaned_table.strip()) > 0:
                    logistics_tables.append(cleaned_table)
                    if len(logistics_tables) >= 2:  # 最多2个表格
                        break
        
        # 如果找到物流表格，只保留这些表格
        if logistics_tables:
            result = '\n\n'.join(logistics_tables)
            # 确保结果不超过原始长度
            if len(result) <= original_length:
                return result
        
        # 如果没有找到明确的物流表格，查找包含日期的div或其他容器
        date_containers = []
        
        # 查找包含日期模式的元素
        date_pattern = r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}[/-]\d{1,2}[/-]\d{1,2}'
        
        for element in soup.find_all(['div', 'span', 'p', 'td', 'th']):
            element_text = element.get_text()
            if re.search(date_pattern, element_text):
                # 找到包含日期的元素，保留其父容器
                parent = element.find_parent(['table', 'div', 'section'])
                if parent and parent not in date_containers:
                    cleaned_container = clean_container_content(parent)
                    if cleaned_container and len(cleaned_container.strip()) > 0:
                        date_containers.append(cleaned_container)
                        if len(date_containers) >= 1:  # 最多1个容器
                            break
        
        if date_containers:
            result = '\n\n'.join(date_containers)
            # 确保结果不超过原始长度
            if len(result) <= original_length:
                return result
        
        # 如果都没有找到，返回压缩的文本内容
        text_content = soup.get_text()
        # 压缩空白字符
        text_content = re.sub(r'\s+', ' ', text_content).strip()
        
        # 确保不超过原始长度的一半
        max_length = min(len(text_content), original_length // 2)
        return text_content[:max_length]
        
    except Exception as e:
        print(f"HTML简化过程中出错: {e}")
        # 出错时返回原始内容的前1/3
        return html_content[:len(html_content)//3]

def clean_table_content(table):
    """清理表格内容，只保留文本信息"""
    try:
        # 提取表格的纯文本内容
        rows = []
        for row in table.find_all('tr'):
            cells = []
            for cell in row.find_all(['td', 'th']):
                cell_text = cell.get_text(strip=True)
                if cell_text:
                    cells.append(cell_text)
            if cells:
                rows.append(' | '.join(cells))
        
        return '\n'.join(rows)
    except:
        return table.get_text(strip=True)

def clean_container_content(container):
    """清理容器内容，只保留文本信息"""
    try:
        return container.get_text(strip=True)
    except:
        return str(container)[:200]  # 最多200字符

def analyze_shipment_dates(html_content: str, bl_number: str) -> str:
    """
    使用文字AI分析页面HTML内容，提取提单号的各个日期信息
    
    Args:
        html_content: 页面HTML内容（可能已经简化过）
        bl_number: 提单号
        
    Returns:
        AI分析结果的JSON字符串
    """
    try:
        # 直接使用传入的HTML内容，不再重复简化
        print(f"[INFO] 使用HTML内容进行AI分析，长度: {len(html_content)}")
        
        # 获取AI客户端
        ai_client = get_ai_client()
        
        prompt = get_text_analyzer_prompt(html_content)

        # 记录AI调用开始时间
        request_time = datetime.now()
        
        response = ai_client.client.chat.completions.create(
            model=ai_client.get_text_model(),
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            max_tokens=4000,
            temperature=0.1
        )
        
        # 记录AI调用结束时间
        response_time = datetime.now()
        
        # 提取AI的回复内容
        ai_response = response.choices[0].message.content.strip()
        print(f"🤖 AI分析结果: {ai_response[:200]}...")
        
        # 记录AI调用日志
        log_ai_call_simple(
            model=ai_client.get_text_model(),
            request_time=request_time,
            response_time=response_time,
            prompt_tokens=response.usage.prompt_tokens if response.usage else 0,
            completion_tokens=response.usage.completion_tokens if response.usage else 0,
            total_tokens=response.usage.total_tokens if response.usage else 0,
            business_id=bl_number,
            caller_id='text_analyzer',
            status='success'
        )
        
        # 保存AI分析结果到文件
        file_manager = get_file_manager()
        timestamp = datetime.now()
        
        try:
            file_manager.save_content(
                bl_number,
                ai_response,
                f"ai_analysis_result_{bl_number}.txt",
                timestamp
            )
            print(f"[SUCCESS] AI分析结果已保存")
        except Exception as e:
            print(f"[WARNING] 保存AI分析结果失败: {e}")
        
        return ai_response
        
    except Exception as e:
        print(f"[ERROR] AI分析失败: {str(e)}")
        
        # 记录失败的AI调用
        log_ai_call_simple(
            model=ai_client.get_text_model(),
            request_time=request_time if 'request_time' in locals() else datetime.now(),
            response_time=datetime.now(),
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            business_id=bl_number,
            caller_id='text_analyzer',
            status='error',
            error_message=str(e)
        )
        
        return json.dumps({
            "error": f"AI分析失败: {str(e)}",
            "estimated_arrival_time": None,
            "estimated_arrival_port": None,
            "dates": []
        }, ensure_ascii=False, indent=2)