#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货运记录演示脚本
展示如何使用ShipmentManager创建和管理货运记录
"""

from shipment_manager import ShipmentManager, create_shipment_with_dates
from datetime import datetime
import json

def demo_create_shipment():
    """
    演示创建货运记录
    """
    print("🚢 演示创建货运记录")
    print("=" * 50)
    
    # 示例时间节点数据（基于用户提供的JSON格式）
    sample_dates = [
        {
            "date": "2025-08-31",
            "original_format": "31/08/2025",
            "type": "POD_ETA",
            "location": "Shanghai CN",
            "description": "POD ETA",
            "status": "",
            "vessel_info": "MSC UBERTY VIII GO535N",
            "context": "表格行中关于POD ETA的信息"
        },
        {
            "date": "2025-08-31",
            "original_format": "31/08/2025",
            "type": "Estimated Time of Arrival",
            "location": "Shanghai CN",
            "description": "Estimated Time of Arrival",
            "status": "estimated",
            "vessel_info": "MSC UBERTY VIII GO535N",
            "context": "表格行中Estimated Time of Arrival的信息"
        },
        {
            "date": "2025-07-30",
            "original_format": "30/07/2025",
            "type": "Full Transshipment Loaded",
            "location": "Rodman PA",
            "description": "Full Transshipment Loaded",
            "status": "",
            "vessel_info": "MSC UBERTY VIII XA530A",
            "context": "表格行中Full Transshipment Loaded的信息"
        },
        {
            "date": "2025-07-28",
            "original_format": "28/07/2025",
            "type": "Full Transshipment Positioned In",
            "location": "Rodman PA",
            "description": "Full Transshipment Positioned In",
            "status": "",
            "vessel_info": "LADEN",
            "context": "表格行中Full Transshipment Positioned In的信息"
        },
        {
            "date": "2025-07-28",
            "original_format": "28/07/2025",
            "type": "Full Transshipment Positioned Out",
            "location": "Colon PA",
            "description": "Full Transshipment Positioned Out",
            "status": "",
            "vessel_info": "LADEN",
            "context": "表格行中Full Transshipment Positioned Out的信息"
        },
        {
            "date": "2025-07-25",
            "original_format": "25/07/2025",
            "type": "Full Transshipment Discharged",
            "location": "Colon PA",
            "description": "Full Transshipment Discharged",
            "status": "",
            "vessel_info": "MSC PASSION III PH528R",
            "context": "表格行中Full Transshipment Discharged的信息"
        },
        {
            "date": "2025-07-20",
            "original_format": "20/07/2025",
            "type": "Price Calculation Date",
            "location": "",
            "description": "Price Calculation Date*",
            "status": "",
            "vessel_info": "",
            "context": "表格行中Price Calculation Date的信息"
        },
        {
            "date": "2025-07-20",
            "original_format": "20/07/2025",
            "type": "Export Loaded on Vessel",
            "location": "Moin CR",
            "description": "Export Loaded on Vessel",
            "status": "",
            "vessel_info": "MSC PASSION III PH528R",
            "context": "表格行中Export Loaded on Vessel的信息"
        },
        {
            "date": "2025-07-19",
            "original_format": "19/07/2025",
            "type": "Export received at CY",
            "location": "Moin CR",
            "description": "Export received at CY",
            "status": "",
            "vessel_info": "LADEN",
            "context": "表格行中Export received at CY的信息"
        },
        {
            "date": "2025-07-15",
            "original_format": "15/07/2025",
            "type": "Empty to Shipper",
            "location": "Puerto limon CR",
            "description": "Empty to Shipper",
            "status": "",
            "vessel_info": "EMPTY",
            "context": "表格行中Empty to Shipper的信息"
        }
    ]
    
    # 创建货运记录
    record_id = create_shipment_with_dates(
        bill_of_lading="MEDUJ0618622",
        container_number="MSCU1234567",
        carrier_company="MSC",
        dates_data=sample_dates,
        created_by="demo_user"
    )
    
    print(f"\n📋 创建的货运记录ID: {record_id}")
    return record_id

def demo_query_shipment(record_id: str):
    """
    演示查询货运记录
    
    Args:
        record_id: 记录ID
    """
    print("\n🔍 演示查询货运记录")
    print("=" * 50)
    
    manager = ShipmentManager()
    
    # 获取记录详情
    record = manager.get_shipment_record(record_id)
    
    if record:
        print(f"📦 提单号: {record.get('bill_of_lading')}")
        print(f"📦 箱号: {record.get('container_number')}")
        print(f"🚢 船公司: {record.get('carrier_company')}")
        print(f"📊 状态: {record.get('status')}")
        print(f"👤 创建人: {record.get('created_by')}")
        print(f"⏰ 创建时间: {record.get('created_at')}")
        
        print(f"\n📅 时间节点 ({len(record.get('dates', []))} 个):")
        for i, date_info in enumerate(record.get('dates', []), 1):
            print(f"  {i}. {date_info['date']} - {date_info['type']}")
            print(f"     📍 {date_info['location']} | 🚢 {date_info['vessel_info']}")
            print(f"     📝 {date_info['description']}")
            print()
    else:
        print("❌ 未找到记录")

def demo_search_shipments():
    """
    演示搜索货运记录
    """
    print("\n🔎 演示搜索货运记录")
    print("=" * 50)
    
    manager = ShipmentManager()
    
    # 按提单号搜索
    print("🔍 按提单号搜索 (MEDUJ):")
    records = manager.search_shipment_records(bill_of_lading="MEDUJ")
    for record in records:
        print(f"  - ID: {record['id']} | 提单号: {record['bill_of_lading']} | 状态: {record['status']}")
    
    # 按船公司搜索
    print("\n🔍 按船公司搜索 (MSC):")
    records = manager.search_shipment_records(carrier_company="MSC")
    for record in records:
        print(f"  - ID: {record['id']} | 船公司: {record['carrier_company']} | 提单号: {record['bill_of_lading']}")

def demo_statistics():
    """
    演示统计信息
    """
    print("\n📊 演示统计信息")
    print("=" * 50)
    
    manager = ShipmentManager()
    stats = manager.get_statistics()
    
    print(f"📈 总记录数: {stats.get('total_records', 0)}")
    print(f"📊 状态分布: {stats.get('status_distribution', {})}")
    print(f"🚢 主要船公司: {stats.get('top_carriers', {})}")
    print(f"📅 最近7天新增: {stats.get('recent_records', 0)}")

def demo_update_status(record_id: str):
    """
    演示更新状态
    
    Args:
        record_id: 记录ID
    """
    print("\n✏️ 演示更新状态")
    print("=" * 50)
    
    manager = ShipmentManager()
    
    # 更新状态为处理中
    success = manager.update_shipment_status(record_id, "处理中", "demo_user")
    if success:
        print("✅ 状态更新成功")
    
    # 再次查询确认
    record = manager.get_shipment_record(record_id)
    if record:
        print(f"📊 当前状态: {record['status']}")
        print(f"👤 更新人: {record['updated_by']}")
        print(f"⏰ 更新时间: {record['updated_at']}")

def main():
    """
    主演示函数
    """
    print("🎯 货运记录管理系统演示")
    print("=" * 60)
    
    try:
        # 1. 创建货运记录
        record_id = demo_create_shipment()
        
        # 2. 查询货运记录
        demo_query_shipment(record_id)
        
        # 3. 搜索货运记录
        demo_search_shipments()
        
        # 4. 更新状态
        demo_update_status(record_id)
        
        # 5. 显示统计信息
        demo_statistics()
        
        print("\n🎉 演示完成！")
        print(f"\n💡 提示: 货运记录ID {record_id} 已创建，您可以在数据库中查看")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()