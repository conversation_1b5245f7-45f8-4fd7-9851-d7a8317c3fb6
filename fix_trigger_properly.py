#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复触发器方案设计和实施
重新创建使用东八区时间的触发器
"""

import sqlite3
import os
from datetime import datetime, timezone, timedelta

# 东八区时区
BEIJING_TZ = timezone(timedelta(hours=8))

def create_beijing_time_trigger():
    """创建使用东八区时间的触发器"""
    print("=== 创建东八区时间触发器 ===")
    
    db_path = 'db/shipment_records.db'
    if not os.path.exists(db_path):
        print(f"[ERROR] 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n1. 检查现有触发器...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger' AND name='update_shipment_records_timestamp'")
        existing_trigger = cursor.fetchone()
        
        if existing_trigger:
            print("   [INFO] 发现现有触发器，先删除")
            cursor.execute("DROP TRIGGER update_shipment_records_timestamp")
        
        print("\n2. 创建新的东八区时间触发器...")
        
        # 注意：SQLite触发器中不能直接使用Python的datetime函数
        # 但我们可以使用 datetime('now', '+8 hours') 来获取东八区时间
        # 或者使用 strftime('%Y-%m-%dT%H:%M:%S.%f+08:00', 'now', '+8 hours')
        
        trigger_sql = '''
        CREATE TRIGGER update_shipment_records_timestamp 
        AFTER UPDATE ON shipment_records
        FOR EACH ROW
        WHEN NEW.updated_at = OLD.updated_at OR NEW.updated_at IS NULL
        BEGIN
            UPDATE shipment_records 
            SET updated_at = strftime('%Y-%m-%dT%H:%M:%S.', 'now', '+8 hours') || 
                            substr(strftime('%f', 'now', '+8 hours'), 4) || '+08:00'
            WHERE id = NEW.id;
        END
        '''
        
        print("   触发器SQL:")
        print(f"   {trigger_sql}")
        
        cursor.execute(trigger_sql)
        conn.commit()
        
        print("   [SUCCESS] 东八区时间触发器创建成功!")
        
        # 验证触发器
        print("\n3. 验证触发器...")
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='trigger' AND name='update_shipment_records_timestamp'")
        trigger_info = cursor.fetchone()
        
        if trigger_info:
            print(f"   [OK] 触发器已创建: {trigger_info[0]}")
        else:
            print("   [ERROR] 触发器创建失败")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[ERROR] 创建触发器失败: {e}")
        return False

def test_trigger_behavior():
    """测试触发器行为"""
    print("\n=== 测试触发器行为 ===")
    
    db_path = 'db/shipment_records.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建测试记录
        print("\n1. 创建测试记录...")
        beijing_time = datetime.now(BEIJING_TZ).isoformat()
        
        cursor.execute('''
            INSERT INTO shipment_records 
            (bill_of_lading, status, created_by, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
        ''', ("TRIGGER_TEST_002", "测试", "trigger_test", beijing_time, beijing_time))
        
        record_id = cursor.lastrowid
        print(f"   创建记录 ID: {record_id}")
        
        # 测试场景1: 显式设置 updated_at (不应触发触发器)
        print("\n2. 测试显式设置 updated_at...")
        explicit_time = datetime.now(BEIJING_TZ).isoformat()
        
        cursor.execute('''
            UPDATE shipment_records 
            SET status = ?, updated_at = ?
            WHERE id = ?
        ''', ("显式更新", explicit_time, record_id))
        
        cursor.execute("SELECT updated_at FROM shipment_records WHERE id = ?", (record_id,))
        result_time = cursor.fetchone()[0]
        
        print(f"   设置时间: {explicit_time}")
        print(f"   结果时间: {result_time}")
        print(f"   是否匹配: {explicit_time == result_time}")
        
        # 测试场景2: 不设置 updated_at (应触发触发器)
        print("\n3. 测试不设置 updated_at (触发器自动设置)...")
        before_update = datetime.now(BEIJING_TZ).isoformat()
        
        cursor.execute('''
            UPDATE shipment_records 
            SET status = ?
            WHERE id = ?
        ''', ("触发器更新", record_id))
        
        cursor.execute("SELECT updated_at FROM shipment_records WHERE id = ?", (record_id,))
        trigger_time = cursor.fetchone()[0]
        
        print(f"   更新前时间: {before_update}")
        print(f"   触发器时间: {trigger_time}")
        print(f"   包含+08:00: {'+08:00' in str(trigger_time)}")
        
        # 清理测试数据
        cursor.execute("DELETE FROM shipment_records WHERE id = ?", (record_id,))
        
        conn.commit()
        conn.close()
        
        # 判断测试结果
        if '+08:00' in str(trigger_time):
            print("\n[SUCCESS] 触发器正确使用东八区时间!")
            return True
        else:
            print(f"\n[ERROR] 触发器时间格式不正确: {trigger_time}")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("触发器修复方案实施")
    print("=" * 50)
    
    # 创建触发器
    if create_beijing_time_trigger():
        # 测试触发器
        if test_trigger_behavior():
            print("\n" + "=" * 50)
            print("[EXCELLENT] 触发器修复成功!")
            print("现在系统具有以下能力:")
            print("1. 代码显式设置东八区时间时 -> 使用代码设置的时间")
            print("2. 代码未设置updated_at时 -> 触发器自动使用东八区时间") 
            print("3. 完全兼容现有代码，提供自动备份机制")
            print("=" * 50)
        else:
            print("\n" + "=" * 50)
            print("[WARNING] 触发器创建成功但测试失败")
            print("请检查触发器逻辑")
            print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("[ERROR] 触发器创建失败")
        print("=" * 50)

if __name__ == "__main__":
    main()