#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI配置数据库初始化脚本
"""

import sqlite3
import os

def init_ai_config_database():
    """初始化AI配置数据库"""
    db_path = os.path.join(os.path.dirname(__file__), 'ai_config.db')
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建AI服务提供商表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ai_providers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            api_key TEXT NOT NULL,
            base_url TEXT NOT NULL,
            support_text BOOLEAN DEFAULT 0,
            support_image BOOLEAN DEFAULT 0,
            support_audio BOOLEAN DEFAULT 0,
            support_video BOOLEAN DEFAULT 0,
            text_model TEXT,
            image_model TEXT,
            audio_model TEXT,
            video_model TEXT,
            remark TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建启用模型配置表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS active_models (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            model_type TEXT NOT NULL UNIQUE CHECK(model_type IN ('text', 'image', 'audio', 'video')),
            provider_id INTEGER,
            is_enabled BOOLEAN DEFAULT 1,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (provider_id) REFERENCES ai_providers (id)
        )
    ''')
    
    # 插入默认的豆包配置
    cursor.execute('''
        INSERT OR IGNORE INTO ai_providers 
        (name, api_key, base_url, support_text, support_image, text_model, image_model, remark)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        '豆包',
        'f1510fc9-df05-4cc7-b21f-85f7249800b6',
        'https://ark.cn-beijing.volces.com/api/v3',
        1,
        1,
        'doubao-seed-1-6-flash-250615',
        'doubao-1.5-vision-pro-250328',
        '默认豆包AI服务'
    ))
    
    # 插入默认的启用模型配置
    provider_id = cursor.lastrowid or 1
    
    cursor.execute('INSERT OR IGNORE INTO active_models (model_type, provider_id) VALUES (?, ?)', ('text', provider_id))
    cursor.execute('INSERT OR IGNORE INTO active_models (model_type, provider_id) VALUES (?, ?)', ('image', provider_id))
    cursor.execute('INSERT OR IGNORE INTO active_models (model_type) VALUES (?)', ('audio',))
    cursor.execute('INSERT OR IGNORE INTO active_models (model_type) VALUES (?)', ('video',))
    
    conn.commit()
    conn.close()
    
    print(f"✅ AI配置数据库初始化完成: {db_path}")

if __name__ == '__main__':
    init_ai_config_database()