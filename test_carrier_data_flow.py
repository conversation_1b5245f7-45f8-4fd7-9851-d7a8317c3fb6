#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试承运人信息数据流传递
验证从输入提单号/集装箱号到run_visual_agent的完整数据链路
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.carrier_lookup import get_company_info
from task_manager import TaskManager
from task_processor import TaskProcessor
from shipment_manager import ShipmentManager

def test_carrier_detection():
    """测试承运人检测功能"""
    print("=== 测试承运人检测功能 ===")
    
    test_numbers = [
        "MEDUVS935363",  # MSC提单号
        "MSCU1234567",   # MSC集装箱号
        "MAEU1234567",   # Maersk集装箱号
        "COAU1234567890", # COSCO提单号
        "UNKNOWN123456"   # 未知承运人
    ]
    
    for number in test_numbers:
        print(f"\n测试号码: {number}")
        carrier_info = get_company_info(number)
        
        if carrier_info:
            print(f"  ✅ 检测成功")
            print(f"  公司: {carrier_info.get('company', 'N/A')}")
            print(f"  追踪网站: {carrier_info.get('tracking_site', 'N/A')}")
            print(f"  输入框ID: {carrier_info.get('input_element_id', 'N/A')}")
            print(f"  搜索按钮ID: {carrier_info.get('search_button_id', 'N/A')}")
        else:
            print(f"  ❌ 检测失败")

def test_task_creation_flow():
    """测试任务创建流程"""
    print("\n=== 测试任务创建流程 ===")
    
    try:
        # 1. 创建货运记录管理器
        shipment_manager = ShipmentManager()
        
        # 2. 模拟用户输入MSC提单号
        test_bl_number = "MEDUVS935363"
        print(f"\n模拟用户输入提单号: {test_bl_number}")
        
        # 3. 检测承运人信息
        carrier_info = get_company_info(test_bl_number)
        if carrier_info:
            print(f"✅ 承运人检测成功: {carrier_info.get('company')}")
        else:
            print(f"❌ 承运人检测失败")
            return False
        
        # 4. 创建货运记录（模拟点击"添加记录"按钮）
        print(f"\n创建货运记录...")
        record_id = shipment_manager.create_shipment_record(
            bill_of_lading=test_bl_number,
            carrier_company=carrier_info.get('company', ''),
            created_by=f"测试用户-{datetime.now().strftime('%Y%m%d')}"
        )
        
        if record_id:
            print(f"✅ 货运记录创建成功，ID: {record_id}")
        else:
            print(f"❌ 货运记录创建失败")
            return False
        
        # 5. 获取关联的任务ID
        record = shipment_manager.get_shipment_record(record_id)
        if record and record.get('remarks'):
            import re
            match = re.search(r'任务ID: ([a-f0-9-]+)', record['remarks'])
            if match:
                task_id = match.group(1)
                print(f"✅ 找到关联任务ID: {task_id}")
                
                # 6. 获取任务详情
                task_manager = TaskManager()
                task = task_manager.get_task_by_id(task_id)
                if task:
                    print(f"✅ 任务详情获取成功")
                    print(f"  任务名称: {task.get('task_name')}")
                    print(f"  跟踪号: {task.get('tracking_number')}")
                    print(f"  任务类型: {task.get('task_type')}")
                    print(f"  承运人: {task.get('carrier')}")
                    return task
                else:
                    print(f"❌ 任务详情获取失败")
                    return False
            else:
                print(f"❌ 任务ID未找到")
                return False
        else:
            print(f"❌ 记录备注为空")
            return False
            
    except Exception as e:
        print(f"❌ 任务创建流程测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_task_processing_flow(task):
    """测试任务处理流程（模拟，不实际执行AI）"""
    print("\n=== 测试任务处理流程 ===")
    
    try:
        # 创建任务处理器
        def mock_completion_callback(task_id, result_data):
            print(f"[CALLBACK] 任务完成回调: {task_id}")
            if result_data:
                print(f"[CALLBACK] 结果数据: {result_data.keys()}")
        
        task_processor = TaskProcessor(completion_callback=mock_completion_callback)
        
        # 获取跟踪号
        tracking_number = task['tracking_number']
        print(f"\n处理跟踪号: {tracking_number}")
        
        # 模拟_process_bill_of_lading方法中的承运人检测逻辑
        print(f"\n模拟任务处理器中的承运人检测...")
        carrier_info = get_company_info(tracking_number)
        
        if carrier_info:
            # 使用检测到的承运人信息
            url = carrier_info.get('tracking_site', 'https://www.msc.com/en/track-a-shipment')
            input_element_id = carrier_info.get('input_element_id', '#trackingNumber')
            search_button_id = carrier_info.get('search_button_id', None)
            company_name = carrier_info.get('company', '未知')
            
            print(f"✅ 任务处理器中承运人检测成功")
            print(f"  检测到承运人: {company_name}")
            print(f"  使用追踪网站: {url}")
            print(f"  输入框ID: {input_element_id}")
            print(f"  搜索按钮ID: {search_button_id}")
            
            # 模拟调用run_visual_agent（不实际执行）
            print(f"\n模拟调用run_visual_agent:")
            print(f"  run_visual_agent('{tracking_number}', '{url}', '{input_element_id}', {search_button_id})")
            
            return True
        else:
            print(f"❌ 任务处理器中承运人检测失败")
            return False
            
    except Exception as e:
        print(f"❌ 任务处理流程测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试承运人信息数据流传递")
    print("=" * 50)
    
    # 1. 测试承运人检测功能
    test_carrier_detection()
    
    # 2. 测试任务创建流程
    task = test_task_creation_flow()
    if not task:
        print("\n❌ 任务创建流程测试失败，停止后续测试")
        return False
    
    # 3. 测试任务处理流程
    success = test_task_processing_flow(task)
    
    if success:
        print("\n🎉 所有测试通过！")
        print("✅ 承运人信息数据流传递正常")
        print("✅ 从用户输入到run_visual_agent的完整链路已打通")
        return True
    else:
        print("\n❌ 测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)