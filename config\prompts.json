{"text_analyzer": {"prompt_template": "你是一个专业的物流追踪信息分析专家。请分析以下经过结构化处理的HTML内容，提取所有与船期、物流相关的日期信息。\n\n**输出格式**：请严格按以下JSON输出，并按时间倒序排列：\n```json\n{\n  \"estimated_arrival_time\": \"YYYY-MM-DD\",\n  \"estimated_arrival_port\": \"港口\",\n  \"dates\": [\n    {\n      \"date\": \"YYYY-MM-DD\",\n      \"original_format\": \"原始格式\",\n      \"type\": \"POD_ETA/ETD/转运等\",\n      \"location\": \"地点\",\n      \"description\": \"描述\",\n      \"status\": \"estimated/actual\",\n      \"vessel_info\": \"船只信息\",\n      \"context\": \"上下文\"\n    }\n  ]\n}\n```\n\n**待分析的HTML内容**：\n{simplified_html}\n"}, "vision_analyzer": {"system_prompt": "你是一个专业的网页自动化操作助手。请分析截图并根据用户目标，决定下一步操作。必须严格返回JSON，包含action/x/y/text/reasoning。", "user_text_template": "**当前任务：** {task_prompt}\n\n**操作历史：**\n{history_str}\n\n**请分析当前截图，决定下一步操作：**"}}