#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试修复后的触发器功能
验证代码和触发器的协同工作
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from shipment_manager import ShipmentManager, get_beijing_time_str
import sqlite3

def test_comprehensive_functionality():
    """全面测试修复后的功能"""
    print("=== 全面功能测试 ===")
    
    results = []
    
    try:
        manager = ShipmentManager()
        
        # 测试1: 正常的ShipmentManager操作（代码显式设置时间）
        print("\n1. 测试ShipmentManager正常操作...")
        record_id = manager.create_shipment_record(
            bill_of_lading="COMPREHENSIVE_TEST_001",
            container_number="COMP_CONT_001",
            carrier_company="MSC",
            estimated_arrival_time=None,
            remarks="全面功能测试",
            created_by="comprehensive_test"
        )
        
        print(f"   记录创建成功，ID: {record_id}")
        
        # 更新状态（代码会显式设置updated_at）
        manager.update_shipment_status(record_id, "测试处理中", "comprehensive_test")
        
        # 检查时间
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute("SELECT created_at, updated_at FROM shipment_records WHERE id = ?", (record_id,))
        times = cursor.fetchone()
        
        if times and '+08:00' in str(times[0]) and '+08:00' in str(times[1]):
            print("   [SUCCESS] ShipmentManager操作使用东八区时间")
            results.append(("ShipmentManager操作", True))
        else:
            print(f"   [ERROR] 时间格式不正确: {times}")
            results.append(("ShipmentManager操作", False))
        
        # 测试2: 直接数据库操作（触发器自动设置时间）
        print("\n2. 测试触发器自动设置时间...")
        
        # 不显式设置updated_at，让触发器处理
        cursor.execute('''
            UPDATE shipment_records 
            SET status = ?
            WHERE id = ?
        ''', ("触发器测试状态", record_id))
        
        cursor.execute("SELECT updated_at FROM shipment_records WHERE id = ?", (record_id,))
        trigger_time = cursor.fetchone()[0]
        
        if '+08:00' in str(trigger_time):
            print(f"   [SUCCESS] 触发器自动设置东八区时间: {trigger_time}")
            results.append(("触发器自动时间", True))
        else:
            print(f"   [ERROR] 触发器时间格式错误: {trigger_time}")
            results.append(("触发器自动时间", False))
        
        # 测试3: 混合操作场景
        print("\n3. 测试混合操作场景...")
        
        # 先用代码显式更新
        before_explicit = get_beijing_time_str()
        manager.update_shipment_status(record_id, "显式更新状态", "mixed_test")
        
        cursor.execute("SELECT updated_at FROM shipment_records WHERE id = ?", (record_id,))
        explicit_time = cursor.fetchone()[0]
        
        # 再用触发器自动更新
        cursor.execute("UPDATE shipment_records SET remarks = ? WHERE id = ?", ("触发器更新备注", record_id))
        
        cursor.execute("SELECT updated_at FROM shipment_records WHERE id = ?", (record_id,))
        auto_time = cursor.fetchone()[0]
        
        print(f"   显式更新时间: {explicit_time}")
        print(f"   触发器更新时间: {auto_time}")
        
        if ('+08:00' in str(explicit_time) and '+08:00' in str(auto_time) and 
            explicit_time != auto_time):
            print("   [SUCCESS] 混合操作场景正常")
            results.append(("混合操作场景", True))
        else:
            print("   [ERROR] 混合操作场景异常")
            results.append(("混合操作场景", False))
        
        # 测试4: shipment_dates表功能
        print("\n4. 测试shipment_dates表功能...")
        
        test_dates = [
            {
                'date': '2025-08-10 08:00:00',
                'location': '上海港',
                'event': '装船',
                'status': '已完成',
                'event_type': 'departure',
                'vessel_info': {'name': 'MSC COMPREHENSIVE', 'voyage': 'COMP001'}
            }
        ]

        # 重要：提交并释放上面步骤中的写锁，避免与 add_shipment_dates 的写操作互相阻塞
        conn.commit()

        dates_success = manager.add_shipment_dates(record_id, test_dates)

        if dates_success:
            # 检查dates表的created_at
            cursor.execute('''
                SELECT created_at FROM shipment_dates 
                WHERE shipment_id = ? 
                ORDER BY id DESC LIMIT 1
            ''', (record_id,))
            
            dates_time = cursor.fetchone()
            if dates_time and '+08:00' in str(dates_time[0]):
                print(f"   [SUCCESS] shipment_dates表使用东八区时间: {dates_time[0]}")
                results.append(("shipment_dates功能", True))
            else:
                print(f"   [ERROR] dates表时间格式错误: {dates_time}")
                results.append(("shipment_dates功能", False))
        else:
            print("   [ERROR] 添加dates失败")
            results.append(("shipment_dates功能", False))
        
        conn.commit()
        conn.close()
        
        return results
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return [("全面测试", False)]

def main():
    """主测试函数"""
    print("全面功能测试 - 验证修复后的触发器")
    print("=" * 60)
    
    # 运行测试
    results = test_comprehensive_functionality()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results:
        status = "[SUCCESS]" if result else "[FAILED]"
        print(f"{status} {test_name}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("\n[EXCELLENT] 所有功能测试通过！")
        print("\n修复方案总结:")
        print("1. 重新创建了智能触发器，使用东八区时间")
        print("2. 触发器只在代码未显式设置updated_at时生效")
        print("3. 完全兼容现有代码逻辑")
        print("4. shipment_records和shipment_dates都正确使用东八区时间")
        print("5. 提供了自动备份机制，确保时间一致性")
        
        print("\n[INFO] 系统现在具备:")
        print("- 代码优先: 代码显式设置时间时，使用代码时间")
        print("- 自动备份: 代码未设置时，触发器自动使用东八区时间")
        print("- 完全兼容: 不需要修改任何现有代码")
        print("- 时间一致: 所有时间字段都使用东八区 (+08:00)")
    else:
        print(f"\n[WARNING] 还有 {len(results) - success_count} 项测试未通过")
        print("请检查相关配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()