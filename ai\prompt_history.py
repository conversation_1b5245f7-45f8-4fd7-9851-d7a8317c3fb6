#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 提示词历史版本管理
- 历史文件：config/prompts_history.json
- 提供：列出、快照、获取、删除、恢复为当前
"""
from __future__ import annotations
import json
import os
import time
import uuid
from typing import Dict, List, Tuple

HISTORY_PATH = os.path.join('config', 'prompts_history.json')
CURRENT_PATH = os.path.join('config', 'prompts.json')


def _ensure_dirs():
    os.makedirs(os.path.dirname(HISTORY_PATH), exist_ok=True)


def _read_json(path: str) -> Dict:
    try:
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return {}


def _write_json(path: str, obj: Dict) -> None:
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(obj, f, ensure_ascii=False, indent=2)


def list_history() -> List[Dict]:
    """返回历史版本列表（最新在前）"""
    _ensure_dirs()
    data = _read_json(HISTORY_PATH)
    versions = data.get('versions', [])
    # 排序：created_at desc
    versions.sort(key=lambda v: v.get('created_at', ''), reverse=True)
    return versions


def snapshot_current(label: str = '') -> Dict:
    """将当前 prompts.json 作为一个历史版本保存，返回版本对象"""
    _ensure_dirs()
    current = _read_json(CURRENT_PATH)
    hist = _read_json(HISTORY_PATH)
    versions = hist.get('versions', [])
    vid = f"{int(time.time())}-{uuid.uuid4().hex[:8]}"
    entry = {
        'id': vid,
        'label': label or 'snapshot',
        'created_at': time.strftime('%Y-%m-%dT%H:%M:%S'),
        'config': current,
    }
    versions.append(entry)
    hist['versions'] = versions
    _write_json(HISTORY_PATH, hist)
    return entry


def get_version(version_id: str) -> Dict | None:
    for v in list_history():
        if v.get('id') == version_id:
            return v
    return None


def delete_version(version_id: str) -> bool:
    _ensure_dirs()
    hist = _read_json(HISTORY_PATH)
    versions = hist.get('versions', [])
    new_versions = [v for v in versions if v.get('id') != version_id]
    if len(new_versions) == len(versions):
        return False
    hist['versions'] = new_versions
    _write_json(HISTORY_PATH, hist)
    return True


def restore_version(version_id: str) -> bool:
    v = get_version(version_id)
    if not v:
        return False
    cfg = v.get('config')
    if not isinstance(cfg, dict):
        return False
    _ensure_dirs()
    _write_json(CURRENT_PATH, cfg)
    return True

