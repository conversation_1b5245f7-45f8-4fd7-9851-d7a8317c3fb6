#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页抓取执行器
专门负责网页数据抓取任务
"""

import os
import time
import json
from datetime import datetime
from typing import Dict, Optional
import traceback

from task_executor import run_visual_agent
from utils.carrier_lookup import get_company_info
from utils.file_manager import get_file_manager


class ScrapingExecutor:
    """网页抓取任务执行器"""
    
    def __init__(self, max_concurrent_tasks: int = 2):
        self.max_concurrent_tasks = max_concurrent_tasks
        print(f"[INIT] 网页抓取执行器初始化 (最大并发: {max_concurrent_tasks})")
    
    def execute_task(self, task: Dict) -> Dict:
        """
        执行网页抓取任务
        
        Args:
            task: 任务信息字典
            
        Returns:
            Dict: 抓取结果
        """
        task_id = task['id']
        tracking_number = task['tracking_number']
        task_type = task['task_type']
        
        print(f"[SCRAPING] 开始抓取任务: {tracking_number} (类型: {task_type})")
        
        try:
            # 根据跟踪号获取承运人信息
            carrier_info = get_company_info(tracking_number)
            
            if carrier_info:
                # 使用检测到的承运人信息
                url = carrier_info.get('tracking_site', 'https://www.msc.com/en/track-a-shipment')
                input_element_id = carrier_info.get('input_element_id', '#trackingNumber')
                search_button_id = carrier_info.get('search_button_id', None)
                company_name = carrier_info.get('company', '未知')
                
                print(f"[SCRAPING] 检测到承运人: {company_name}")
                print(f"[SCRAPING] 使用追踪网站: {url}")
                print(f"[SCRAPING] 输入框ID: {input_element_id}")
                print(f"[SCRAPING] 搜索按钮ID: {search_button_id}")
            else:
                # 如果无法检测承运人，使用默认的MSC配置
                url = "https://www.msc.com/en/track-a-shipment"
                input_element_id = "#trackingNumber"
                search_button_id = None
                company_name = "MSC (默认)"
                print(f"[WARNING] 无法检测承运人，使用默认MSC配置")
            
            # 调用视觉AI代理进行网页抓取
            print(f"[SCRAPING] 开始网页抓取: {tracking_number}")
            start_time = time.time()
            
            # 调用抓取方法 - 只进行抓取，不包含AI分析
            result = self._perform_scraping_only(tracking_number, url, input_element_id, search_button_id)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result and result.get('success'):
                print(f"[SUCCESS] 网页抓取完成: {tracking_number} (耗时: {duration:.2f}秒)")
                
                # 保存原始数据路径到任务记录中
                if result.get('data', {}).get('raw_data_path'):
                    from task_manager import TaskManager
                    task_manager = TaskManager()
                    task_manager.update_raw_data_path(task_id, result['data']['raw_data_path'])
                
                return {
                    'success': True,
                    'summary': f"{tracking_number} 网页抓取完成",
                    'data': result.get('data', {}),
                    'duration': duration,
                    'company': company_name
                }
            else:
                error_msg = result.get('error', '网页抓取失败') if result else '网页抓取失败'
                print(f"[ERROR] 网页抓取失败: {tracking_number} - {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'duration': duration,
                    'company': company_name
                }
        
        except Exception as e:
            error_msg = f"网页抓取异常: {str(e)}"
            print(f"[ERROR] {error_msg}")
            traceback.print_exc()
            return {
                'success': False,
                'error': error_msg
            }
    
    def _perform_scraping_only(self, tracking_number: str, url: str, 
                              input_element_id: str, search_button_id: Optional[str]) -> Dict:
        """
        执行纯网页抓取（不包含AI分析）
        
        Args:
            tracking_number: 跟踪号
            url: 查询网址
            input_element_id: 输入框选择器
            search_button_id: 搜索按钮选择器
            
        Returns:
            Dict: 抓取结果
        """
        try:
            print(f"[SCRAPING] 执行纯网页抓取: {tracking_number}")
            
            # 暂时使用现有的方法，但只处理抓取部分
            # 注意：这里需要修改 run_visual_agent 以支持 scraping_only 参数
            agent_result = run_visual_agent(tracking_number, url, input_element_id, search_button_id)
            
            # 直接使用抓取结果中的路径，不再重新查找
            if agent_result and agent_result.get('raw_data_path'):
                raw_data_path = agent_result['raw_data_path']
                result_files = {}
                
                # 从抓取结果中获取文件信息
                if agent_result.get('result_files'):
                    agent_files = agent_result['result_files']
                    if agent_files.get('screenshot'):
                        result_files['screenshot'] = agent_files['screenshot']
                
                return {
                    'success': True,
                    'data': {
                        'raw_data_path': raw_data_path,
                        'result_files': result_files,
                        'tracking_number': tracking_number,
                        'scraping_completed': True,
                    }
                }
            
            # 作为备选方案，检查是否抓取成功（基于文件是否生成）
            file_mgr = get_file_manager()
            tracking_files = file_mgr.get_files_for_tracking(tracking_number)
            
            if tracking_files:
                latest_record = tracking_files[0]
                raw_data_path = latest_record['folder_path']
                result_files = {}
                
                # 收集抓取的文件
                for file_info in latest_record['files']:
                    file_name = file_info['name']
                    file_path = file_info['path']
                    
                    if file_name == 'final_result.png':
                        result_files['screenshot'] = file_path
                    elif file_name.startswith('page_content_original_'):
                        result_files['html_original'] = file_path
                    elif file_name.startswith('page_content_simplified_'):
                        result_files['html_simplified'] = file_path
                
                return {
                    'success': True,
                    'data': {
                        'raw_data_path': raw_data_path,
                        'result_files': result_files,
                        'tracking_number': tracking_number,
                        'scraping_completed': True,
                        'storage_info': {
                            'folder_path': latest_record['folder_path'],
                            'year_month': latest_record['year_month'],
                            'created': latest_record['created'].strftime('%Y-%m-%d %H:%M:%S')
                        }
                    }
                }
            else:
                return {
                    'success': False,
                    'error': '网页抓取未能完成或未找到结果文件'
                }
        
        except Exception as e:
            return {
                'success': False,
                'error': f"抓取执行异常: {str(e)}"
            }
    
    def stop(self):
        """停止执行器"""
        print("[STOP] 网页抓取执行器已停止")


if __name__ == "__main__":
    # 测试代码
    executor = ScrapingExecutor()
    
    # 模拟任务
    test_task = {
        'id': 'test-scraping-001',
        'tracking_number': 'MEDUJ0618622', 
        'task_type': 'bill_of_lading',
        'task_name': 'MSC-MEDUJ0618622提单号网页抓取任务'
    }
    
    print("🧪 测试网页抓取执行器...")
    result = executor.execute_task(test_task)
    print(f"📋 测试结果: {json.dumps(result, indent=2, ensure_ascii=False)}")