#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货运记录管理器
管理货运记录主表和时间节点子表的操作
"""

import sqlite3
import json
from datetime import datetime, date, timezone, timedelta
from typing import List, Dict, Optional, Tuple
from task_manager import TaskManager
from db.connection_manager import get_connection_manager

# 定义东八区时区
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time() -> datetime:
    """获取东八区（北京时间）当前时间"""
    return datetime.now(BEIJING_TZ)

def get_beijing_time_str() -> str:
    """获取东八区时间的ISO格式字符串"""
    return get_beijing_time().isoformat()

class ShipmentManager:
    """
    货运记录管理器类
    负责货运记录和时间节点的数据库操作
    """

    def __init__(self, db_path: str = 'db/shipment_records.db'):
        """
        初始化货运记录管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.task_manager = TaskManager()

    def _get_connection(self) -> sqlite3.Connection:
        """
        获取数据库连接

        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        print(f"[DB_LOG] 正在连接数据库: {self.db_path}")
        # 使用连接复用器，返回包装连接；调用 close() 将归还到池中
        conn = get_connection_manager(self.db_path).acquire()
        print(f"[DB_LOG] 数据库连接成功: {self.db_path}")
        return conn

    def create_shipment_record(self,
                             bill_of_lading: Optional[str] = None,
                             container_number: Optional[str] = None,
                             carrier_company: Optional[str] = None,
                             estimated_arrival_time: Optional[datetime] = None,
                             evidence_screenshot: Optional[str] = None,
                             remarks: Optional[str] = None,
                             created_by: str = 'system') -> str:
        """
        创建货运记录并自动创建对应的查询任务

        Args:
            bill_of_lading: 提单号
            container_number: 箱号
            carrier_company: 船公司
            estimated_arrival_time: 预计到港时间
            evidence_screenshot: 佐证截图路径
            remarks: 备注
            created_by: 创建人

        Returns:
            str: 创建的记录ID
        """
        if not bill_of_lading and not container_number:
            raise ValueError("提单号和箱号至少需要提供一个")

        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 创建货运记录，初始状态为'排队中'
            # 显式设置东八区创建时间
            beijing_time = get_beijing_time_str()
            sql = '''
                INSERT INTO shipment_records
                (bill_of_lading, container_number, carrier_company,
                 estimated_arrival_time, evidence_screenshot, remarks, created_by, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (bill_of_lading, container_number, carrier_company,
                      estimated_arrival_time, evidence_screenshot, remarks, created_by, '排队中',
                      beijing_time, beijing_time)

            print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            print(f"[DB_LOG] 使用东八区时间: {beijing_time}")

            cursor.execute(sql, params)
            record_id = cursor.lastrowid
            print(f"[DB_LOG] 执行结果: 创建记录ID {record_id}")

            conn.commit()
            print(f"[DB_LOG] 事务提交成功 - 库:shipment_records.db 表:shipment_records")

            # 自动创建对应的查询任务
            try:
                tracking_number = bill_of_lading or container_number
                task_type = 'bill_of_lading' if bill_of_lading else 'container'

                task_id = self.task_manager.create_task(
                    tracking_number=tracking_number,
                    task_type=task_type,
                    creator_id=created_by,
                    creator_name=created_by,
                    carrier=carrier_company,
                    remarks=f"货运记录ID: {record_id}"
                )

                # 将任务ID关联到货运记录（可选，用于后续追踪）
                # 显式设置东八区更新时间
                beijing_update_time = get_beijing_time_str()
                update_sql = '''
                    UPDATE shipment_records SET remarks = ?, updated_at = ? WHERE id = ?
                '''
                update_params = (f"任务ID: {task_id}" + (f", {remarks}" if remarks else ""), beijing_update_time, record_id)

                print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_records")
                print(f"[DB_LOG] SQL语句(更新备注): {update_sql.strip()}")
                print(f"[DB_LOG] 参数(更新备注): {update_params}")
                print(f"[DB_LOG] 使用东八区更新时间: {beijing_update_time}")

                cursor.execute(update_sql, update_params)
                print(f"[DB_LOG] 执行结果(更新备注): 影响行数 {cursor.rowcount}")

                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:shipment_records.db 表:shipment_records")

                print(f"[SUCCESS] 货运记录创建成功，ID: {record_id}，任务ID: {task_id}")

            except Exception as task_error:
                print(f"[WARNING] 任务创建失败: {task_error}，但货运记录已创建")

            return str(record_id)

        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:shipment_records.db")
            print(f"[ERROR] 创建货运记录失败: {e}")
            raise
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")

    def add_shipment_dates(self, shipment_id: str, dates_data: List[Dict]) -> bool:
        """
        批量添加货运时间节点

        Args:
            shipment_id: 货运记录ID
            dates_data: 时间节点数据列表

        Returns:
            bool: 是否添加成功
        """
        print(f"[INFO] 开始添加时间节点: 货运记录ID={shipment_id}, 节点数量={len(dates_data)}")

        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 先检查货运记录是否存在
            check_sql = 'SELECT id, bill_of_lading, container_number FROM shipment_records WHERE id = ?'
            check_params = (shipment_id,)

            print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] SQL语句(检查记录): {check_sql}")
            print(f"[DB_LOG] 参数(检查记录): {check_params}")

            cursor.execute(check_sql, check_params)
            existing_record = cursor.fetchone()

            if not existing_record:
                print(f"[DB_LOG] 查询结果: 未找到匹配记录 - 库:shipment_records.db 表:shipment_records")
                print(f"[ERROR] 未找到货运记录ID: {shipment_id}")
                return False

            print(f"[DB_LOG] 查询结果: 找到记录 - 库:shipment_records.db 表:shipment_records")
            print(f"[INFO] 找到目标货运记录: ID={existing_record[0]}, 提单号={existing_record[1]}, 箱号={existing_record[2]}")

            # 先清除该货运记录的现有时间节点
            count_sql = 'SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?'
            count_params = (shipment_id,)

            print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_dates")
            print(f"[DB_LOG] SQL语句(统计节点): {count_sql}")
            print(f"[DB_LOG] 参数(统计节点): {count_params}")

            cursor.execute(count_sql, count_params)
            existing_count = cursor.fetchone()[0]
            print(f"[DB_LOG] 查询结果(统计节点): {existing_count} 条")
            print(f"[INFO] 清除现有时间节点: {existing_count} 条")

            delete_sql = 'DELETE FROM shipment_dates WHERE shipment_id = ?'
            delete_params = (shipment_id,)

            print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_dates")
            print(f"[DB_LOG] SQL语句(删除节点): {delete_sql}")
            print(f"[DB_LOG] 参数(删除节点): {delete_params}")

            cursor.execute(delete_sql, delete_params)
            deleted_count = cursor.rowcount
            print(f"[DB_LOG] 执行结果(删除节点): 删除 {deleted_count} 行")

            # 批量插入新的时间节点
            print(f"[INFO] 开始插入新的时间节点...")
            beijing_time = get_beijing_time_str()
            insert_sql = '''
                INSERT INTO shipment_dates
                (shipment_id, date, original_format, type, location,
                 description, status, vessel_info, context, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''

            for i, date_info in enumerate(dates_data):
                print(f"  [INFO] 插入节点 {i+1}/{len(dates_data)}: {date_info.get('date')} - {date_info.get('description')} @ {date_info.get('location')}")
                # 将vessel_info字典转换为JSON字符串
                vessel_info = date_info.get('vessel_info', {})
                vessel_info_json = json.dumps(vessel_info) if vessel_info else None

                insert_params = (
                    shipment_id,
                    date_info.get('date'),
                    date_info.get('original_format'),
                    date_info.get('event_type') or date_info.get('type') or '无',
                    date_info.get('location'),
                    date_info.get('event') or date_info.get('description'),
                    date_info.get('status'),
                    vessel_info_json,
                    date_info.get('context'),
                    beijing_time  # 使用东八区时间
                )

                print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_dates")
                print(f"[DB_LOG] SQL语句(插入节点): {insert_sql.strip()}")
                print(f"[DB_LOG] 参数(插入节点): {insert_params}")
                print(f"[DB_LOG] 使用东八区创建时间: {beijing_time}")

                cursor.execute(insert_sql, insert_params)
                print(f"[DB_LOG] 执行结果(插入节点): 插入成功")

            conn.commit()
            print(f"[DB_LOG] 事务提交成功 - 库:shipment_records.db 表:shipment_dates")
            print(f"[SUCCESS] 成功添加 {len(dates_data)} 个时间节点到货运记录 {shipment_id} (使用东八区时间)")
            return True

        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:shipment_records.db 表:shipment_dates")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:shipment_records.db")
            print(f"[ERROR] 添加时间节点失败: {e}")
            import traceback
            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")

    def handle_task_completion(self, task_id: str, result_data: Dict = None) -> bool:
        """
        处理任务完成后的操作：更新状态、保存结果数据、触发列表刷新

        Args:
            task_id: 任务ID
            result_data: 任务执行结果数据

        Returns:
            bool: 是否处理成功
        """
        try:
            print(f"[INFO] 开始处理任务完成: {task_id}")
            print(f"[INFO] 接收到的结果数据: {result_data}")

            # 获取任务信息以确定任务阶段
            task = self.task_manager.get_task_by_id(task_id)
            if not task:
                print(f"[ERROR] 找不到任务: {task_id}")
                return False
                
            task_stage = task.get('task_stage', 'scraping')
            print(f"[INFO] 任务阶段: {task_stage}")
            
            # 预先检查关联的货运记录是否已完成，如果已完成则跳过所有状态改动但允许查看
            record_id = self._get_shipment_record_id_from_task(task)
            record_is_completed = record_id and self._is_shipment_completed(record_id)
            
            if record_is_completed:
                print(f"[INFO] 货运记录 {record_id} 已完成，将跳过状态变更以保护历史数据，但允许数据查看")
            else:
                print(f"[INFO] 货运记录 {record_id} 未完成，允许正常处理")

            # 1. 同步状态（非AI分析阶段先同步，AI分析阶段先处理数据后再同步）
            # 但如果记录已完成，则跳过状态同步
            if (task_stage != 'ai_analysis' or not result_data) and not record_is_completed:
                if not self.sync_status_with_task(task_id):
                    print(f"[ERROR] 同步任务状态失败: {task_id}")
                    return False
            elif record_is_completed:
                print(f"[INFO] 记录已完成，跳过状态同步以保护历史状态")

            # 2. 如果有结果数据，更新货运记录的相关信息
            if result_data:
                print(f"[DEBUG] 开始处理结果数据...")
                
                if task:
                    # 使用与sync_status_with_task相同的逻辑查找正确的货运记录ID
                    record_id = None
                    tracking_number = task.get('tracking_number', '')
                    remarks = task.get('remarks', '')
                    
                    # 从任务备注中提取货运记录ID
                    if '货运记录ID:' in remarks:
                        record_id = remarks.split('货运记录ID:')[1].split(',')[0].strip()
                        print(f"[DEBUG] 从任务备注中找到货运记录ID: {record_id}")
                        
                        # 检查该记录是否已完成；AI分析阶段即使已完成也允许写入分析结果
                        if record_is_completed and task_stage != 'ai_analysis':
                            print(f"[INFO] 货运记录 {record_id} 已完成，跳过非AI阶段的结果数据更新以保护历史数据")
                            record_id = None  # 设为None，后面就不会更新数据库
                        else:
                            print(f"[DEBUG] 货运记录 {record_id} 可处理结果数据（任务阶段: {task_stage}）")
                    else:
                        print(f"[WARNING] 任务备注中未找到货运记录ID，跳过结果数据处理")
                    
                    if record_id:
                        print(f"[INFO] 找到关联的货运记录ID: {record_id}")
                    else:
                        print(f"[WARNING] 未能确定货运记录ID，跳过结果数据处理")
                        # 如果是AI分析阶段任务完成但无法找到记录ID，仍然返回成功
                        return True  
                
                if record_id:

                    # 更新预计到港时间（如果结果中包含）
                    estimated_arrival_time = None
                    if 'estimated_arrival_time' in result_data and result_data['estimated_arrival_time']:
                        estimated_arrival_time = result_data['estimated_arrival_time']
                        print(f"[DEBUG] 在顶层找到estimated_arrival_time")
                    elif 'data' in result_data and isinstance(result_data['data'], dict):
                        if 'estimated_arrival_time' in result_data['data'] and result_data['data']['estimated_arrival_time']:
                            estimated_arrival_time = result_data['data']['estimated_arrival_time']
                            print(f"[DEBUG] 在data字段中找到estimated_arrival_time")

                    if not estimated_arrival_time:
                        # 尝试从时间节点推导 ETA（兼容不同返回结构）
                        dates_for_eta = None
                        if 'dates_data' in result_data and result_data['dates_data']:
                            dates_for_eta = result_data['dates_data']
                        elif 'dates' in result_data and result_data['dates']:
                            dates_for_eta = result_data['dates']
                        elif 'data' in result_data and isinstance(result_data['data'], dict):
                            data_section = result_data['data']
                            if 'dates_data' in data_section and data_section['dates_data']:
                                dates_for_eta = data_section['dates_data']
                            elif 'dates' in data_section and data_section['dates']:
                                dates_for_eta = data_section['dates']
                        if dates_for_eta:
                            derived_eta = self._derive_eta_from_dates(dates_for_eta)
                            if derived_eta:
                                estimated_arrival_time = derived_eta
                                print(f"[INFO] 从时间节点推导出预计到港时间: {derived_eta}")

                    if estimated_arrival_time:
                        print(f"[INFO] 更新预计到港时间: {estimated_arrival_time}")
                        success = self._update_estimated_arrival_time(record_id, estimated_arrival_time)
                        if success:
                            print(f"[SUCCESS] 预计到港时间更新成功")
                        else:
                            print(f"[ERROR] 预计到港时间更新失败")
                    else:
                        print(f"[WARNING] 结果数据中未包含预计到港时间")

                    # 更新佐证截图路径（如果结果中包含）
                    result_files = None
                    if 'result_files' in result_data and result_data['result_files']:
                        result_files = result_data['result_files']
                        print(f"[DEBUG] 在顶层找到result_files")
                    elif 'data' in result_data and isinstance(result_data['data'], dict):
                        if 'result_files' in result_data['data'] and result_data['data']['result_files']:
                            result_files = result_data['data']['result_files']
                            print(f"[DEBUG] 在data字段中找到result_files")

                    if result_files and 'screenshot' in result_files and result_files['screenshot']:
                        screenshot_path = result_files['screenshot']
                        print(f"[INFO] 更新佐证截图路径: {screenshot_path}")
                        try:
                            success = self._update_evidence_screenshot(record_id, screenshot_path)
                            if success:
                                print(f"[SUCCESS] 佐证截图路径更新成功")
                            else:
                                print(f"[ERROR] 佐证截图路径更新失败")
                        except Exception as e:
                            print(f"[ERROR] 佐证截图更新异常: {e}")
                            print(f"[DEBUG] 记录ID: {record_id}")
                            print(f"[DEBUG] 截图路径: {screenshot_path}")
                            import traceback
                            print(f"[DEBUG] 异常详情: {traceback.format_exc()}")
                    else:
                        print(f"[WARNING] 未找到screenshot路径，result_files: {result_files}")
                        print(f"[DEBUG] 完整的result_data结构: {result_data}")

                    # 添加时间节点数据（如果结果中包含）
                    # 支持两种格式：'dates_data' 和 'dates'，并检查data字段
                    dates_to_add = None
                    if 'dates_data' in result_data and result_data['dates_data']:
                        dates_to_add = result_data['dates_data']
                        print(f"[INFO] 在顶层发现 dates_data 格式的时间节点数据")
                    elif 'dates' in result_data and result_data['dates']:
                        dates_to_add = result_data['dates']
                        print(f"[INFO] 在顶层发现 dates 格式的时间节点数据")
                    elif 'data' in result_data and isinstance(result_data['data'], dict):
                        data_section = result_data['data']
                        if 'dates_data' in data_section and data_section['dates_data']:
                            dates_to_add = data_section['dates_data']
                            print(f"[INFO] 在data字段中发现 dates_data 格式的时间节点数据")
                        elif 'dates' in data_section and data_section['dates']:
                            dates_to_add = data_section['dates']
                            print(f"[INFO] 在data字段中发现 dates 格式的时间节点数据")

                    if dates_to_add:
                        print(f"[INFO] 添加时间节点数据，共 {len(dates_to_add)} 条")
                        for i, date_item in enumerate(dates_to_add):
                            event_desc = date_item.get('event') or date_item.get('description', '未知事件')
                            print(f"  [INFO] 时间节点 {i+1}: {date_item.get('date')} - {event_desc} @ {date_item.get('location')}")
                        success = self.add_shipment_dates(record_id, dates_to_add)
                        if success:
                            print(f"[SUCCESS] 时间节点数据添加成功")
                        else:
                            print(f"[ERROR] 时间节点数据添加失败")
                    else:
                        print(f"[WARNING] 结果数据中未包含时间节点数据 (检查了 dates_data 和 dates 字段)")

                    # 3. 如果是AI分析阶段任务完成，自动更新货运记录状态为"已完成"（仅当记录未完成时）
                    if task_stage == 'ai_analysis' and not record_is_completed:
                        print(f"[INFO] AI分析任务完成，自动更新货运记录状态为'已完成'")
                        success = self.update_shipment_status(record_id, '已完成', 'ai_analysis_complete')
                        if success:
                            print(f"[SUCCESS] 货运记录 {record_id} 状态已更新为'已完成'")
                        else:
                            print(f"[ERROR] 货运记录 {record_id} 状态更新为'已完成'失败")
                    elif task_stage == 'ai_analysis' and record_is_completed:
                        print(f"[INFO] AI分析任务完成，但货运记录 {record_id} 已是已完成状态，跳过状态更新")

                    print(f"[SUCCESS] 货运记录 {record_id} 数据处理完成")
                else:
                    print(f"[WARNING] 任务备注中未找到货运记录ID信息")
                    if task:
                        print(f"[INFO] 任务备注内容: {task.get('remarks', 'None')}")
            else:
                print(f"[WARNING] 未接收到结果数据")
                
                # 即使没有结果数据，如果是AI分析任务完成，也尝试更新状态
                if task_stage == 'ai_analysis':
                    # 从任务信息中获取货运记录ID并更新状态
                    temp_record_id = self._get_shipment_record_id_from_task(task)
                    if temp_record_id and not self._is_shipment_completed(temp_record_id):
                        print(f"[INFO] AI分析任务完成（无结果数据），自动更新货运记录状态为'已完成'")
                        success = self.update_shipment_status(temp_record_id, '已完成', 'ai_analysis_complete_no_data')
                        if success:
                            print(f"[SUCCESS] 货运记录 {temp_record_id} 状态已更新为'已完成'")
                        else:
                            print(f"[ERROR] 货运记录 {temp_record_id} 状态更新失败")
                    elif temp_record_id:
                        print(f"[INFO] 货运记录 {temp_record_id} 已经是'已完成'状态，跳过更新")
                    else:
                        print(f"[WARNING] 未能找到关联的货运记录ID")

            print(f"[SUCCESS] 任务 {task_id} 完成处理")
            
            # 触发UI刷新（复用网页抓取和AI分析的刷新机制）
            if hasattr(self, 'ui_refresh_callback') and callable(self.ui_refresh_callback):
                try:
                    self.ui_refresh_callback()
                    print(f"[UI] 已触发界面刷新")
                except Exception as e:
                    print(f"[ERROR] UI刷新回调失败: {e}")
            
            return True

        except Exception as e:
            print(f"[ERROR] 任务完成处理失败: {e}")
            import traceback
            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")
            return False

    def _update_estimated_arrival_time(self, record_id: str, eta: str) -> bool:
        """
        更新货运记录的预计到港时间

        Args:
            record_id: 货运记录ID
            eta: 预计到港时间

        Returns:
            bool: 是否更新成功
        """
        print(f"[INFO] 开始更新预计到港时间: 记录ID={record_id}, ETA={eta}")

        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # 先检查记录是否存在
            cursor.execute('SELECT id, bill_of_lading, container_number FROM shipment_records WHERE id = ?', (record_id,))
            existing_record = cursor.fetchone()

            if not existing_record:
                print(f"[ERROR] 未找到记录ID: {record_id}")
                return False

            print(f"[INFO] 找到目标记录: ID={existing_record[0]}, 提单号={existing_record[1]}, 箱号={existing_record[2]}")

            # 显式设置东八区更新时间
            beijing_update_time = get_beijing_time_str()

            update_sql = '''
                UPDATE shipment_records
                SET estimated_arrival_time = ?, updated_by = ?, updated_at = ?
                WHERE id = ?
            '''
            params = (eta, 'task_result', beijing_update_time, record_id)

            print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] SQL语句(更新预计到港时间): {update_sql.strip()}")
            print(f"[DB_LOG] 参数(更新预计到港时间): {params}")
            print(f"[DB_LOG] 使用东八区更新时间: {beijing_update_time}")
            cursor.execute(update_sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果(更新预计到港时间): 影响行数 {affected_rows}")

            if affected_rows > 0:
                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:shipment_records.db 表:shipment_records")
                print(f"[SUCCESS] 更新预计到港时间成功: {record_id} -> {eta} (使用东八区时间)")
                return True
            else:
                # SQLite在更新为相同值时可能返回 rowcount=0，属幂等更新，视为成功
                cursor.execute('SELECT estimated_arrival_time FROM shipment_records WHERE id = ?', (record_id,))
                row = cursor.fetchone()
                if row:
                    current_eta = row['estimated_arrival_time'] if isinstance(row, sqlite3.Row) else row[0]
                    if str(current_eta) == str(eta):
                        print(f"[INFO] ETA未变化（已是目标值），视为成功: {record_id} -> {eta}")
                        return True
                print(f"[DB_LOG] 更新失败：未找到匹配记录或ETA未变化但记录缺失 - 库:shipment_records.db 表:shipment_records")
                print(f"[WARNING] 更新操作未影响任何记录: {record_id}")
                return False

        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 更新预计到港时间失败: {e}")
            import traceback
            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:shipment_records.db")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")

    def _update_evidence_screenshot(self, record_id: str, screenshot_path: str) -> bool:
        """
        更新货运记录的佐证截图路径

        Args:
            record_id: 货运记录ID
            screenshot_path: 佐证截图文件路径

        Returns:
            bool: 是否更新成功
        """
        print(f"[INFO] 开始更新佐证截图路径: 记录ID={record_id}, 截图路径={screenshot_path}")

        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # 先检查记录是否存在
            cursor.execute('SELECT id, bill_of_lading, container_number FROM shipment_records WHERE id = ?', (record_id,))
            existing_record = cursor.fetchone()

            if not existing_record:
                print(f"[ERROR] 未找到记录ID: {record_id}")
                return False

            print(f"[INFO] 找到目标记录: ID={existing_record[0]}, 提单号={existing_record[1]}, 箱号={existing_record[2]}")

            # 显式设置东八区更新时间
            beijing_update_time = get_beijing_time_str()

            update_sql = '''
                UPDATE shipment_records
                SET evidence_screenshot = ?, updated_by = ?, updated_at = ?
                WHERE id = ?
            '''
            params = (screenshot_path, 'task_result', beijing_update_time, record_id)

            print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] SQL语句(更新佐证截图): {update_sql.strip()}")
            print(f"[DB_LOG] 参数(更新佐证截图): {params}")
            print(f"[DB_LOG] 使用东八区更新时间: {beijing_update_time}")

            cursor.execute(update_sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果(更新佐证截图): 影响行数 {affected_rows}")

            if affected_rows > 0:
                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:shipment_records.db 表:shipment_records")
                print(f"[SUCCESS] 佐证截图路径更新成功: {record_id} -> {screenshot_path}")
                return True
            else:
                # 更新为相同路径时 rowcount=0，视为幂等成功
                cursor.execute('SELECT evidence_screenshot FROM shipment_records WHERE id = ?', (record_id,))
                row = cursor.fetchone()
                if row:
                    current_path = row['evidence_screenshot'] if isinstance(row, sqlite3.Row) else row[0]
                    if str(current_path) == str(screenshot_path):
                        print(f"[INFO] 截图路径未变化（已是目标值），视为成功: {record_id} -> {screenshot_path}")
                        return True
                print(f"[DB_LOG] 更新失败：未找到匹配的记录或路径未变化但记录缺失 - 库:shipment_records.db 表:shipment_records")
                print(f"[WARNING] 佐证截图路径更新失败: 未找到记录 {record_id}")
                return False

        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 更新佐证截图路径失败: {e}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:shipment_records.db")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")

    def get_shipment_record(self, record_id: str) -> Optional[Dict]:
        """
        获取货运记录详情

        Args:
            record_id: 记录ID

        Returns:
            Dict: 货运记录信息，包含时间节点
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 获取主记录
            cursor.execute('SELECT * FROM shipment_records WHERE id = ?', (record_id,))
            record = cursor.fetchone()

            if not record:
                return None

            # 转换为字典
            record_dict = dict(record)

            # 获取时间节点
            cursor.execute('''
                SELECT * FROM shipment_dates
                WHERE shipment_id = ?
                ORDER BY date ASC
            ''', (record_id,))

            dates = [dict(row) for row in cursor.fetchall()]
            record_dict['dates'] = dates

            return record_dict

        except Exception as e:
            print(f"[ERROR] 获取货运记录失败: {e}")
            return None
        finally:
            conn.close()

    def search_shipment_records(self,
                              bill_of_lading: Optional[str] = None,
                              container_number: Optional[str] = None,
                              carrier_company: Optional[str] = None,
                              status: Optional[str] = None,
                              limit: int = 100) -> List[Dict]:
        """
        搜索货运记录

        Args:
            bill_of_lading: 提单号（模糊搜索）
            container_number: 箱号（模糊搜索）
            carrier_company: 船公司（模糊搜索）
            status: 状态
            limit: 返回记录数限制

        Returns:
            List[Dict]: 货运记录列表
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            where_conditions = []
            params = []

            if bill_of_lading:
                where_conditions.append('bill_of_lading LIKE ?')
                params.append(f'%{bill_of_lading}%')

            if container_number:
                where_conditions.append('container_number LIKE ?')
                params.append(f'%{container_number}%')

            if carrier_company:
                where_conditions.append('carrier_company LIKE ?')
                params.append(f'%{carrier_company}%')

            if status:
                where_conditions.append('status = ?')
                params.append(status)

            where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'

            query = f'''
                SELECT * FROM shipment_records
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT ?
            '''
            params.append(limit)

            cursor.execute(query, params)
            records = [dict(row) for row in cursor.fetchall()]

            return records

        except Exception as e:
            print(f"[ERROR] 搜索货运记录失败: {e}")
            return []
        finally:
            conn.close()

    def sync_status_with_task(self, task_id: str) -> bool:
        """
        根据任务状态同步更新货运记录状态

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否更新成功
        """
        try:
            # 获取任务信息
            task = self.task_manager.get_task_by_id(task_id)
            if not task:
                print(f"[ERROR] 未找到任务: {task_id}")
                return False

            # 状态映射
            status_mapping = {
                'pending': '排队中',
                'processing': '处理中',
                'completed': '已完成',
                'cancelled': '已取消'
            }

            shipment_status = status_mapping.get(task['status'], '未知状态')
            tracking_number = task.get('tracking_number', '')

            # 从任务备注中提取货运记录ID
            remarks = task.get('remarks', '')
            record_id = None
            
            if '货运记录ID:' in remarks:
                record_id = remarks.split('货运记录ID:')[1].split(',')[0].strip()
                print(f"[DEBUG] 从任务备注中找到货运记录ID: {record_id}")
                
                # 检查该记录是否已完成，如果已完成则跳过更新
                if self._is_shipment_completed(record_id):
                    print(f"[INFO] 货运记录 {record_id} 已完成，跳过状态更新以保护历史数据")
                    record_id = None  # 设为None，后面就不会执行更新
                else:
                    print(f"[DEBUG] 货运记录 {record_id} 未完成，可以更新状态")
            else:
                print(f"[WARNING] 任务 {task_id} 备注中未找到货运记录ID")

            if record_id:
                # 更新货运记录状态
                success = self.update_shipment_status(record_id, shipment_status, 'task_sync')
                if success:
                    print(f"[SUCCESS] 货运记录 {record_id} 状态已同步为: {shipment_status}")
                return success
            else:
                print(f"[WARNING] 任务 {task_id} 无法找到关联的货运记录ID")
                return False

        except Exception as e:
            print(f"[ERROR] 状态同步失败: {e}")
            import traceback
            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")
            return False

    def update_shipment_status(self, record_id: str, status: str, updated_by: str = 'system') -> bool:
        """
        更新货运记录状态

        Args:
            record_id: 记录ID
            status: 新状态
            updated_by: 更新人

        Returns:
            bool: 是否更新成功
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 显式设置东八区更新时间
            beijing_update_time = get_beijing_time_str()

            update_sql = '''
                UPDATE shipment_records
                SET status = ?, updated_by = ?, updated_at = ?
                WHERE id = ?
            '''
            params = (status, updated_by, beijing_update_time, record_id)

            print(f"[DB_LOG] 执行SQL - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] SQL语句(更新状态): {update_sql.strip()}")
            print(f"[DB_LOG] 参数(更新状态): {params}")
            print(f"[DB_LOG] 使用东八区更新时间: {beijing_update_time}")

            cursor.execute(update_sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果(更新状态): 影响行数 {affected_rows}")

            if affected_rows > 0:
                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:shipment_records.db 表:shipment_records")
                print(f"[SUCCESS] 货运记录 {record_id} 状态已更新为: {status} (使用东八区时间)")
                return True
            else:
                # 在SQLite中，如果更新后的值与原值相同，rowcount 可能为0，这种情况应视为已是最新状态
                cursor.execute('SELECT status FROM shipment_records WHERE id = ?', (record_id,))
                row = cursor.fetchone()
                if row:
                    current_status = row['status'] if isinstance(row, sqlite3.Row) else row[0]
                    if current_status == status:
                        print(f"[INFO] 状态未变化（已是目标状态），视为成功: {record_id} -> {status}")
                        return True
                print(f"[DB_LOG] 更新失败：未找到匹配记录或状态未变化但记录缺失 - 库:shipment_records.db 表:shipment_records")
                print(f"[WARNING] 未找到ID为 {record_id} 的货运记录或状态不匹配")
                return False

        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:shipment_records.db 表:shipment_records")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:shipment_records.db")
            print(f"[ERROR] 更新货运记录状态失败: {e}")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")

    def get_statistics(self) -> Dict:
        """
        获取货运记录统计信息

        Returns:
            Dict: 统计信息
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            stats = {}

            # 总记录数
            cursor.execute('SELECT COUNT(*) FROM shipment_records')
            stats['total_records'] = cursor.fetchone()[0]

            # 按状态分组统计
            cursor.execute('''
                SELECT status, COUNT(*)
                FROM shipment_records
                GROUP BY status
            ''')
            stats['status_distribution'] = dict(cursor.fetchall())

            # 按船公司分组统计
            cursor.execute('''
                SELECT carrier_company, COUNT(*)
                FROM shipment_records
                WHERE carrier_company IS NOT NULL
                GROUP BY carrier_company
                ORDER BY COUNT(*) DESC
                LIMIT 10
            ''')
            stats['top_carriers'] = dict(cursor.fetchall())

            # 最近7天创建的记录数
            cursor.execute('''
                SELECT COUNT(*)
                FROM shipment_records
                WHERE created_at >= datetime('now', '-7 days')
            ''')
            stats['recent_records'] = cursor.fetchone()[0]

            return stats

        except Exception as e:
            print(f"[ERROR] 获取统计信息失败: {e}")
            return {}
        finally:
            conn.close()

    def get_shipment_details(self, record_id: int) -> Optional[Dict]:
        """
        获取货运记录详情（包含时间节点）

        Args:
            record_id: 记录ID

        Returns:
            Dict: 包含基本信息和时间节点的详情
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 获取基本信息
            cursor.execute('SELECT * FROM shipment_records WHERE id = ?', (record_id,))
            record = cursor.fetchone()

            if not record:
                return None

            # 转换为字典
            basic_info = dict(record)

            # 获取时间节点
            cursor.execute('''
                SELECT date, type, location, description, status, vessel_info, created_at
                FROM shipment_dates
                WHERE shipment_id = ?
                ORDER BY created_at
            ''', (record_id,))

            dates = [dict(row) for row in cursor.fetchall()]

            return {
                'basic_info': basic_info,
                'dates': dates
            }

        except Exception as e:
            print(f"[ERROR] 获取货运记录详情失败: {e}")
            return None
        finally:
            conn.close()

    def delete_shipment_record(self, record_id: str) -> bool:
        """
        删除货运记录（级联删除时间节点）

        Args:
            record_id: 记录ID

        Returns:
            bool: 是否删除成功
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('DELETE FROM shipment_records WHERE id = ?', (record_id,))

            if cursor.rowcount > 0:
                conn.commit()
                print(f"[SUCCESS] 货运记录 {record_id} 已删除")
                return True
            else:
                print(f"[WARNING] 未找到ID为 {record_id} 的货运记录")
                return False

        except Exception as e:
            conn.rollback()
            print(f"[ERROR] 删除货运记录失败: {e}")
            return False
        finally:
            conn.close()

    def delete_shipment(self, record_id: int) -> bool:
        """
        删除货运记录的别名方法（兼容性）

        Args:
            record_id: 记录ID

        Returns:
            bool: 是否删除成功
        """
        return self.delete_shipment_record(str(record_id))
    
    def _get_shipment_record_id_from_task(self, task: Dict) -> Optional[str]:
        """
        从任务信息中提取货运记录ID
        
        Args:
            task: 任务信息字典
            
        Returns:
            Optional[str]: 货运记录ID，如果未找到则返回None
        """
        if not task:
            return None
            
        remarks = task.get('remarks', '')
        if '货运记录ID:' in remarks:
            try:
                record_id = remarks.split('货运记录ID:')[1].split(',')[0].strip()
                return record_id if record_id else None
            except (IndexError, AttributeError):
                return None
        return None
    
    def _is_shipment_completed(self, record_id: str) -> bool:
        """
        检查货运记录是否已完成
        
        Args:
            record_id: 货运记录ID
            
        Returns:
            bool: 如果已完成返回True，否则返回False
        """
        if not record_id:
            return False
            
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT status FROM shipment_records WHERE id = ?', (record_id,))
            current_record = cursor.fetchone()
            
            if current_record and current_record[0] == '已完成':
                return True
            return False
        except Exception as e:
            print(f"[ERROR] 检查货运记录状态失败: {e}")
            return False
        finally:
            conn.close()

# 便捷函数
def create_shipment_with_dates(bill_of_lading: Optional[str] = None,
                              container_number: Optional[str] = None,
                              carrier_company: Optional[str] = None,
                              dates_data: Optional[List[Dict]] = None,
                              created_by: str = 'system') -> Optional[str]:
    """
    创建货运记录并添加时间节点

    Args:
        bill_of_lading: 提单号
        container_number: 箱号
        carrier_company: 船公司
        dates_data: 时间节点数据
        created_by: 创建人

    Returns:
        str: 创建的记录ID
    """
    manager = ShipmentManager()

    # 创建主记录
    record_id = manager.create_shipment_record(
        bill_of_lading=bill_of_lading,
        container_number=container_number,
        carrier_company=carrier_company,
        created_by=created_by
    )

    # 添加时间节点
    if dates_data and record_id:
        manager.add_shipment_dates(record_id, dates_data)

    return record_id

if __name__ == "__main__":
    # 测试代码
    manager = ShipmentManager()

    # 显示统计信息
    stats = manager.get_statistics()
    print("[INFO] 货运记录统计:")
    print(f"  总记录数: {stats.get('total_records', 0)}")
    print(f"  状态分布: {stats.get('status_distribution', {})}")
    print(f"  最近7天: {stats.get('recent_records', 0)}")