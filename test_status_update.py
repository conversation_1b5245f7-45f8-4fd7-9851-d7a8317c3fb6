#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态更新机制
"""

import time
import sqlite3
from shipment_manager import ShipmentManager
from task_manager import TaskManager

def test_status_update():
    """测试货运记录状态更新"""
    print("开始测试状态更新机制...")
    
    # 初始化管理器
    shipment_manager = ShipmentManager()
    task_manager = TaskManager()
    
    # 清理旧测试数据
    print("\n清理旧测试数据...")
    try:
        # 清理货运记录
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute("DELETE FROM shipment_records WHERE bill_of_lading LIKE 'TEST%'")
        conn.commit()
        conn.close()
        print("已清理旧货运记录")
        
        # 清理任务队列
        conn = sqlite3.connect('db/task_queue.db') 
        cursor = conn.cursor()
        cursor.execute("DELETE FROM task_queue WHERE tracking_number LIKE 'TEST%'")
        conn.commit()
        conn.close()
        print("已清理旧任务记录")
    except Exception as e:
        print(f"清理数据时出错: {e}")
    
    # 创建测试记录
    print("\n创建测试货运记录...")
    test_bill = "TEST001"
    record_id = shipment_manager.create_shipment_record(
        bill_of_lading=test_bill,
        carrier_company="测试船公司",
        created_by="状态更新测试"
    )
    print(f"测试记录已创建，ID: {record_id}")
    
    # 检查初始状态
    print("\n检查初始状态...")
    records = shipment_manager.search_shipment_records(bill_of_lading=test_bill)
    if records:
        record = records[0]
        print(f"初始状态: {record['status']}")
    else:
        print("ERROR: 未找到刚创建的记录")
        return
    
    # 等待任务创建
    print("\n等待任务创建...")
    time.sleep(2)
    
    # 检查任务状态
    print("检查相关任务...")
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute("SELECT id, task_name, status, task_stage FROM task_queue WHERE tracking_number = ?", (test_bill,))
    tasks = cursor.fetchall()
    conn.close()
    
    if tasks:
        print(f"找到 {len(tasks)} 个相关任务:")
        for task in tasks:
            print(f"  任务ID: {task[0]}, 名称: {task[1]}, 状态: {task[2]}, 阶段: {task[3]}")
    else:
        print("WARNING: 未找到相关任务")
    
    # 测试手动状态更新
    print("\n测试手动状态更新...")
    success = shipment_manager.update_shipment_status(record_id, "测试处理中", "手动测试")
    if success:
        print("手动状态更新成功")
        
        # 验证更新结果
        records = shipment_manager.search_shipment_records(bill_of_lading=test_bill)
        if records:
            record = records[0]
            print(f"更新后状态: {record['status']}")
        
    else:
        print("ERROR: 手动状态更新失败")
    
    print("\n测试完成")

if __name__ == "__main__":
    test_status_update()