<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Page</title>
</head>
<body>
    <h1>Debug Page</h1>
    <div id="output"></div>
    
    <script>
        function log(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }
        
        log('Starting debug...');
    </script>
    
    <script src="js/router.js"></script>
    <script>
        log('Router loaded: ' + (typeof Router !== 'undefined'));
    </script>
    
    <script src="js/components.js"></script>
    <script>
        log('ComponentManager loaded: ' + (typeof ComponentManager !== 'undefined'));
    </script>
    
    <script src="js/data.js"></script>
    <script>
        log('DataManager loaded: ' + (typeof DataManager !== 'undefined'));
    </script>
    
    <script src="js/app.js"></script>
    <script>
        log('ShipmentApp loaded: ' + (typeof ShipmentApp !== 'undefined'));
        log('Debug complete');
    </script>
</body>
</html>