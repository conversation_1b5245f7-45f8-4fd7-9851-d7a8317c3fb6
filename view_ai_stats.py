#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI调用统计查看工具
用于查看AI调用的统计信息和最近记录
"""

import sys
from db_logger import AICallLogger
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def print_separator(title=""):
    """打印分隔线"""
    print("=" * 60)
    if title:
        print(f" {title} ".center(60, "="))
        print("=" * 60)

def format_datetime(dt_str):
    """格式化日期时间字符串"""
    try:
        dt = datetime.fromisoformat(dt_str)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return dt_str

def main():
    """主函数"""
    logger = AICallLogger()
    
    print_separator("AI调用统计报告")
    
    # 获取统计信息
    stats = logger.get_call_stats(days=30)
    
    # 基本统计
    print("📊 基本统计信息 (最近30天):")
    basic = stats['basic']
    print(f"  总调用次数: {basic['total_calls']}")
    print(f"  成功调用: {basic['success_calls']} ({basic['success_rate']}%)")
    print(f"  失败调用: {basic['error_calls']}")
    print(f"  总Token消耗: {basic['total_tokens']:,}")
    print(f"    - 提示Token: {basic['total_prompt_tokens']:,}")
    print(f"    - 完成Token: {basic['total_completion_tokens']:,}")
    print(f"  平均响应时间: {basic['avg_duration']}秒")
    if basic['total_cost']:
        print(f"  总费用: ${basic['total_cost']:.4f}")
    print()
    
    # 按模型统计
    if stats['by_model']:
        print("🤖 按模型统计:")
        for model_stat in stats['by_model']:
            print(f"  {model_stat['model']}:")
            print(f"    调用次数: {model_stat['calls']}")
            print(f"    Token消耗: {model_stat['tokens']:,}")
            if model_stat['cost']:
                print(f"    费用: ${model_stat['cost']:.4f}")
        print()
    
    # 按内容类型统计
    if stats['by_content_type']:
        print("📝 按内容类型统计:")
        for content_stat in stats['by_content_type']:
            print(f"  {content_stat['content_type']}:")
            print(f"    调用次数: {content_stat['calls']}")
            print(f"    Token消耗: {content_stat['tokens']:,}")
        print()
    
    # 最近调用记录
    print_separator("最近调用记录")
    recent_calls = logger.get_recent_calls(limit=10)
    
    if recent_calls:
        print("🕒 最近10次调用:")
        for i, call in enumerate(recent_calls, 1):
            status_icon = "✅" if call['status'] == 'success' else "❌"
            print(f"  {i:2d}. {status_icon} [{format_datetime(call['request_time'])}]")
            print(f"      模型: {call['model']}")
            print(f"      类型: {call['content_type']}")
            print(f"      耗时: {call['duration_seconds']:.3f}秒")
            print(f"      Token: {call['total_tokens']}")
            if call['business_id']:
                print(f"      业务ID: {call['business_id']}")
            print()
    else:
        print("暂无调用记录")
    
    print_separator()
    print("💡 提示: 使用 'python view_ai_stats.py caller_id' 查看特定调用方的统计")

def view_caller_stats(caller_id):
    """查看特定调用方的统计"""
    logger = AICallLogger()
    
    print_separator(f"调用方 '{caller_id}' 的统计信息")
    
    # 获取特定调用方的统计
    stats = logger.get_call_stats(caller_id=caller_id, days=30)
    
    basic = stats['basic']
    if basic['total_calls'] == 0:
        print(f"未找到调用方 '{caller_id}' 的记录")
        return
    
    print(f"📊 {caller_id} 的统计信息 (最近30天):")
    print(f"  总调用次数: {basic['total_calls']}")
    print(f"  成功率: {basic['success_rate']}%")
    print(f"  总Token消耗: {basic['total_tokens']:,}")
    print(f"  平均响应时间: {basic['avg_duration']}秒")
    print()
    
    # 最近调用记录
    recent_calls = logger.get_recent_calls(limit=5, caller_id=caller_id)
    if recent_calls:
        print(f"🕒 {caller_id} 最近5次调用:")
        for i, call in enumerate(recent_calls, 1):
            status_icon = "✅" if call['status'] == 'success' else "❌"
            print(f"  {i}. {status_icon} [{format_datetime(call['request_time'])}] {call['model']} - {call['total_tokens']} tokens")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        caller_id = sys.argv[1]
        view_caller_stats(caller_id)
    else:
        main()