#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货运记录管理系统 - 主应用程序
商业级PySide6界面设计
"""

import sys
import re
from datetime import datetime
from typing import Optional, List, Dict, Any

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QFrame, QGroupBox, QComboBox, QDateEdit, QTextEdit,
    QMessageBox, QProgressBar, QScrollArea, QGridLayout, QSpacerItem,
    QSizePolicy, QDialog, QDialogButtonBox, QFormLayout, QCheckBox, QFileDialog,
    QMenuBar, QMenu, QInputDialog
)
from PySide6.QtCore import (
    Qt, QTimer, QThread, Signal, QPropertyAnimation, QEasingCurve,
    QRect, QDate, QSize, QUrl
)
from PySide6.QtGui import (
    QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient,
    QIcon, QPen, QFontMetrics, QDesktopServices, QAction, QKeySequence
)

from utils.carrier_lookup import get_company_info
from shipment_manager import ShipmentManager
from task_manager import TaskManager
from task_processor import TaskProcessor
from scheduled_task_processor import ScheduledTaskProcessor
import os
import json
from db.connection_manager import get_pool_stats
from ai.prompt_config import reload_prompt_config


class ImageViewerDialog(QDialog):
    """图片查看器对话框"""

    def __init__(self, image_path: str, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.scale_factor = 1.0
        self.original_pixmap = None
        self.dragging = False
        self.last_pan_point = None

        self.setWindowTitle("佐证截图查看器")

        # 设置窗口大小为屏幕的75%并居中
        screen = QApplication.primaryScreen().geometry()
        width = int(screen.width() * 0.75)
        height = int(screen.height() * 0.75)
        self.resize(width, height)

        # 居中显示
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.move(x, y)

        self.setModal(True)

        self.setup_ui()
        self.load_image()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.zoom_in_btn = ModernButton("放大 (+)", "secondary")
        self.zoom_in_btn.clicked.connect(self.zoom_in)

        self.zoom_out_btn = ModernButton("缩小 (-)", "secondary")
        self.zoom_out_btn.clicked.connect(self.zoom_out)

        self.reset_btn = ModernButton("重置", "secondary")
        self.reset_btn.clicked.connect(self.reset_zoom)

        self.scale_label = QLabel("100%")
        self.scale_label.setStyleSheet("color: #666; font-weight: bold;")

        toolbar_layout.addWidget(self.zoom_in_btn)
        toolbar_layout.addWidget(self.zoom_out_btn)
        toolbar_layout.addWidget(self.reset_btn)
        toolbar_layout.addWidget(self.scale_label)
        toolbar_layout.addStretch()

        # 保存图片按钮
        save_btn = ModernButton("保存图片", "secondary")
        save_btn.clicked.connect(self.save_image)
        toolbar_layout.addWidget(save_btn)

        layout.addLayout(toolbar_layout)

        # 图片显示区域
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background-color: #f9f9f9;
                min-height: 400px;
            }
        """)
        self.image_label.setScaledContents(False)

        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(self.image_label)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignCenter)

        layout.addWidget(self.scroll_area)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 在文件管理器中打开按钮
        open_folder_btn = ModernButton("在文件管理器中打开", "secondary")
        open_folder_btn.clicked.connect(self.open_in_explorer)
        button_layout.addWidget(open_folder_btn)

        button_layout.addStretch()

        # 关闭按钮
        close_btn = ModernButton("关闭", "primary")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def load_image(self):
        """加载图片"""
        if not self.image_path or not os.path.exists(self.image_path):
            self.image_label.setText("图片文件不存在或路径无效")
            self.image_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #e0e0e0;
                    border-radius: 8px;
                    background-color: #f9f9f9;
                    color: #666;
                    font-size: 14px;
                }
            """)
            return

        try:
            self.original_pixmap = QPixmap(self.image_path)
            if self.original_pixmap.isNull():
                self.image_label.setText("无法加载图片文件")
                return

            self.update_image()

        except Exception as e:
            self.image_label.setText(f"加载图片时出错: {str(e)}")

    def update_image(self):
        """更新图片显示"""
        if self.original_pixmap:
            scaled_pixmap = self.original_pixmap.scaled(
                int(self.original_pixmap.width() * self.scale_factor),
                int(self.original_pixmap.height() * self.scale_factor),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
            self.image_label.resize(scaled_pixmap.size())
            self.scale_label.setText(f"{int(self.scale_factor * 100)}%")

    def zoom_in(self):
        """放大图片"""
        if self.scale_factor < 5.0:  # 最大放大5倍
            self.scale_factor *= 1.25
            self.update_image()

    def zoom_out(self):
        """缩小图片"""
        if self.scale_factor > 0.1:  # 最小缩小到10%
            self.scale_factor /= 1.25
            self.update_image()

    def reset_zoom(self):
        """重置缩放"""
        self.scale_factor = 1.0
        self.update_image()

    def wheelEvent(self, event):
        """鼠标滚轮缩放"""
        if event.modifiers() == Qt.ControlModifier:
            if event.angleDelta().y() > 0:
                self.zoom_in()
            else:
                self.zoom_out()
        else:
            super().wheelEvent(event)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.last_pan_point = event.position().toPoint()
            self.setCursor(Qt.ClosedHandCursor)
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging and self.last_pan_point:
            delta = event.position().toPoint() - self.last_pan_point

            # 移动滚动条
            h_bar = self.scroll_area.horizontalScrollBar()
            v_bar = self.scroll_area.verticalScrollBar()

            h_bar.setValue(h_bar.value() - delta.x())
            v_bar.setValue(v_bar.value() - delta.y())

            self.last_pan_point = event.position().toPoint()
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            self.last_pan_point = None
            self.setCursor(Qt.ArrowCursor)
        super().mouseReleaseEvent(event)

    def save_image(self):
         """保存图片到本地"""
         if not self.original_pixmap:
             QMessageBox.warning(self, "警告", "没有可保存的图片")
             return

         try:
             # 获取原始文件名和扩展名
             original_name = os.path.basename(self.image_path) if self.image_path else "screenshot.png"
             name, ext = os.path.splitext(original_name)

             # 打开保存对话框
             file_path, _ = QFileDialog.getSaveFileName(
                 self,
                 "保存图片",
                 original_name,
                 "PNG文件 (*.png);;JPEG文件 (*.jpg);;所有文件 (*.*)"
             )

             if file_path:
                 # 保存图片
                 success = self.original_pixmap.save(file_path)
                 if success:
                     QMessageBox.information(self, "成功", f"图片已保存到：\n{file_path}")
                 else:
                     QMessageBox.warning(self, "失败", "保存图片失败")

         except Exception as e:
             QMessageBox.critical(self, "错误", f"保存图片时出错：{str(e)}")

    def open_in_explorer(self):
        """在文件管理器中打开图片所在文件夹"""
        if self.image_path and os.path.exists(self.image_path):
            folder_path = os.path.dirname(self.image_path)
            QDesktopServices.openUrl(QUrl.fromLocalFile(folder_path))


class ClickableLabel(QLabel):
    """可点击的标签组件"""
    clicked = Signal()

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setCursor(Qt.PointingHandCursor)
        self.setStyleSheet("""
            QLabel {
                color: #2196F3;
                text-decoration: underline;
            }
            QLabel:hover {
                color: #1976D2;
                background-color: #E3F2FD;
                border-radius: 4px;
                padding: 2px;
            }
        """)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class ModernCard(QFrame):
    """现代化卡片组件"""

    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            ModernCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin: 8px;
            }
            ModernCard:hover {
                border: 1px solid #2196F3;
            }
        """)

        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 16, 20, 20)
        self.layout.setSpacing(12)

        if title:
            self.title_label = QLabel(title)
            self.title_label.setFont(QFont("Segoe UI", 14, QFont.Weight.DemiBold))
            self.title_label.setStyleSheet("color: #1a1a1a; margin-bottom: 8px;")
            self.layout.addWidget(self.title_label)

    def add_widget(self, widget):
        self.layout.addWidget(widget)

    def add_layout(self, layout):
        self.layout.addLayout(layout)


class ModernButton(QPushButton):
    """现代化按钮组件"""

    def __init__(self, text: str, button_type: str = "primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10, QFont.Weight.Medium))

        if button_type == "primary":
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2196F3, stop:1 #1976D2);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 24px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1976D2, stop:1 #1565C0);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1565C0, stop:1 #0D47A1);
                }
                QPushButton:disabled {
                    background: #BDBDBD;
                    color: #757575;
                }
            """)
        elif button_type == "secondary":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #f5f5f5;
                    color: #424242;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 8px 24px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #eeeeee;
                    border-color: #bdbdbd;
                }
                QPushButton:pressed {
                    background-color: #e0e0e0;
                }
            """)


class ModernLineEdit(QLineEdit):
    """现代化输入框组件"""

    def __init__(self, placeholder: str = "", parent=None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder)
        self.setMinimumHeight(44)
        self.setFont(QFont("Segoe UI", 10))
        self.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px 16px;
                background-color: #fafafa;
                selection-background-color: #2196F3;
            }
            QLineEdit:focus {
                border-color: #2196F3;
                background-color: #ffffff;
            }
            QLineEdit:hover {
                border-color: #bdbdbd;
            }
        """)


class ModernTableWidget(QTableWidget):
    """现代化表格组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        self.horizontalHeader().setStretchLastSection(True)
        self.setFont(QFont("Segoe UI", 9))

        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #f2f4f7;  /* 更浅的选中背景，避免遮盖蓝色内容 */
                color: #1f2937;            /* 深灰文字，保持可读性 */
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e8e8e8);
                color: #424242;
                padding: 8px 8px;
                border: none;
                border-bottom: 2px solid #2196F3;
                font-weight: 600;
                height: 24px;
            }
        """)
        self.horizontalHeader().setMinimumHeight(24)
        self.verticalHeader().setDefaultSectionSize(28)

    def keyPressEvent(self, event):
        """支持 Ctrl+C 复制所选单元格/行到剪贴板"""
        try:
            is_copy = False
            try:
                # 优先使用标准匹配
                is_copy = event.matches(QKeySequence.Copy)
            except Exception:
                # 低版本兼容
                is_copy = (event.key() == Qt.Key_C and (event.modifiers() & Qt.ControlModifier))

            if is_copy:
                ranges = self.selectedRanges()
                text = ""
                if ranges:
                    lines = []
                    for r in ranges:
                        for row in range(r.topRow(), r.bottomRow() + 1):
                            cols_text = []
                            for col in range(r.leftColumn(), r.rightColumn() + 1):
                                item = self.item(row, col)
                                cols_text.append(item.text() if item else "")
                            lines.append("\t".join(cols_text))
                    text = "\n".join(lines)
                else:
                    items = self.selectedItems()
                    if items:
                        text = "\n".join([i.text() for i in items])
                if text:
                    QApplication.clipboard().setText(text)
                    return  # 拦截默认处理
        except Exception:
            pass
        finally:
            super().keyPressEvent(event)


class CarrierInfoWidget(QGroupBox):
    """承运人信息显示组件 - Navicat紧凑风格"""

    def __init__(self, parent=None):
        super().__init__("承运人信息", parent)

        # 设置Navicat风格样式
        self.setStyleSheet("""
            QGroupBox {
                font-size: 10px;
                font-weight: bold;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                margin-top: 6px;
                padding-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 6px;
                padding: 0 3px 0 3px;
            }
            QLabel {
                font-size: 9px;
                padding: 1px;
            }
        """)

        # 创建信息显示区域
        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 6, 6, 6)
        layout.setSpacing(2)

        self.info_layout = QGridLayout()
        self.info_layout.setVerticalSpacing(1)
        self.info_layout.setHorizontalSpacing(4)
        self.info_layout.setContentsMargins(0, 0, 0, 0)

        # 公司名称
        self.company_label = QLabel("公司名称:")
        self.company_label.setStyleSheet("font-weight: normal; color: #495057;")
        self.company_value = QLabel("未检测")
        self.company_value.setStyleSheet("color: #666; font-weight: normal;")
        self.company_value.setWordWrap(True)  # 启用文本换行

        # 查询网址
        self.url_label = QLabel("查询网址:")
        self.url_label.setStyleSheet("font-weight: normal; color: #495057;")
        self.url_value = QLabel("未检测")
        self.url_value.setStyleSheet("color: #2196F3; font-weight: normal;")
        self.url_value.setWordWrap(True)

        # 输入框ID
        self.input_id_label = QLabel("输入框ID:")
        self.input_id_label.setStyleSheet("font-weight: normal; color: #495057;")
        self.input_id_value = QLabel("未检测")
        self.input_id_value.setStyleSheet("color: #666; font-weight: normal;")
        self.input_id_value.setWordWrap(True)  # 启用文本换行

        # 搜索按钮ID
        self.search_btn_id_label = QLabel("搜索按钮ID:")
        self.search_btn_id_label.setStyleSheet("font-weight: normal; color: #495057;")
        self.search_btn_id_value = QLabel("未检测")
        self.search_btn_id_value.setStyleSheet("color: #666; font-weight: normal;")
        self.search_btn_id_value.setWordWrap(True)  # 启用文本换行

        # 检测状态
        self.status_label = QLabel("检测状态:")
        self.status_label.setStyleSheet("font-weight: normal; color: #495057;")
        self.status_value = QLabel("等待输入")
        self.status_value.setStyleSheet("color: #FF9800; font-weight: normal;")
        self.status_value.setWordWrap(True)  # 启用文本换行

        # 添加到布局
        self.info_layout.addWidget(self.company_label, 0, 0)
        self.info_layout.addWidget(self.company_value, 0, 1)
        self.info_layout.addWidget(self.url_label, 1, 0)
        self.info_layout.addWidget(self.url_value, 1, 1)
        self.info_layout.addWidget(self.input_id_label, 2, 0)
        self.info_layout.addWidget(self.input_id_value, 2, 1)
        self.info_layout.addWidget(self.search_btn_id_label, 3, 0)
        self.info_layout.addWidget(self.search_btn_id_value, 3, 1)
        self.info_layout.addWidget(self.status_label, 4, 0)
        self.info_layout.addWidget(self.status_value, 4, 1)

        self.info_layout.setColumnStretch(1, 1)
        layout.addLayout(self.info_layout)

        # 承运人管理按钮 - Navicat紧凑风格
        manage_btn = QPushButton("承运人管理")
        manage_btn.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: 1px solid #ced4da;
                border-radius: 2px;
                padding: 3px 8px;
                font-size: 9px;
                color: #495057;
                height: 18px;
                margin-top: 2px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        # 通过父窗口查找方法调用
        manage_btn.clicked.connect(self.open_carrier_management_from_widget)
        layout.addWidget(manage_btn)

    def open_carrier_management_from_widget(self):
        """从子组件调用主窗口的承运人管理方法"""
        # 查找父窗口中的方法
        parent = self.parent()
        while parent:
            if hasattr(parent, 'open_carrier_management'):
                parent.open_carrier_management()
                return
            parent = parent.parent()

        # 如果找不到父窗口方法，显示错误
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(self, "错误", "无法找到承运人管理功能")

    def update_info(self, company_info: Dict[str, Any]):
        """更新承运人信息"""
        if company_info:
            self.company_value.setText(company_info.get('company', '未知'))
            self.url_value.setText(company_info.get('tracking_site', '无'))
            self.input_id_value.setText(company_info.get('input_element_id', '无'))
            self.search_btn_id_value.setText(company_info.get('search_button_id', '无'))
            self.status_value.setText("检测成功")
            self.status_value.setStyleSheet("color: #4CAF50; font-weight: normal;")
        else:
            self.company_value.setText("未识别")
            self.url_value.setText("无")
            self.input_id_value.setText("无")
            self.search_btn_id_value.setText("无")
            self.status_value.setText("检测失败")
            self.status_value.setStyleSheet("color: #F44336; font-weight: normal;")

class ConnectionPoolStatsDialog(QDialog):
    """连接池状态查看对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("连接池状态")
        self.resize(420, 240)
        layout = QVBoxLayout(self)

        self.info_label = QLabel("查看 SQLite 连接池当前状态（两个库）")
        layout.addWidget(self.info_label)

        self.table = QTableWidget(0, 5)
        self.table.setHorizontalHeaderLabels(["数据库", "最大连接", "已创建", "空闲", "使用中"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)

        btn_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新")
        self.close_btn = QPushButton("关闭")
        self.refresh_btn.clicked.connect(self.refresh)
        self.close_btn.clicked.connect(self.accept)
        btn_layout.addWidget(self.refresh_btn)
        btn_layout.addWidget(self.close_btn)
        layout.addLayout(btn_layout)

        self.refresh()

    def refresh(self):
        dbs = [
            ("shipment_records.db", "db/shipment_records.db"),
            ("task_queue.db", "db/task_queue.db"),
        ]
        self.table.setRowCount(len(dbs))
        for row, (name, path) in enumerate(dbs):
            stats = get_pool_stats(path)
            self.table.setItem(row, 0, QTableWidgetItem(name))
            self.table.setItem(row, 1, QTableWidgetItem(str(stats.get("maxsize"))))
            self.table.setItem(row, 2, QTableWidgetItem(str(stats.get("created"))))
            self.table.setItem(row, 3, QTableWidgetItem(str(stats.get("available"))))
class PromptEditorDialog(QDialog):
    """AI 提示词配置编辑器"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("AI 提示词配置编辑器")
        self.resize(700, 520)
        layout = QVBoxLayout(self)

        self.path = os.path.join("config", "prompts.json")
        self.editor = QTextEdit()
        self.editor.setFont(QFont("Consolas", 10))
        layout.addWidget(self.editor)

        btns = QHBoxLayout()
        self.load_btn = QPushButton("重新载入文件")
        self.save_btn = QPushButton("保存并应用")
        self.close_btn = QPushButton("关闭")
        self.load_btn.clicked.connect(self.load_file)
        self.save_btn.clicked.connect(self.save_and_apply)
        self.close_btn.clicked.connect(self.accept)
        btns.addWidget(self.load_btn)
        btns.addWidget(self.save_btn)
        btns.addWidget(self.close_btn)
        layout.addLayout(btns)

        self.load_file()

    def load_file(self):
        try:
            if not os.path.exists(self.path):
                # 初始化示例配置
                os.makedirs(os.path.dirname(self.path), exist_ok=True)
                with open(self.path, 'w', encoding='utf-8') as f:
                    f.write('{\n  "text_analyzer": {},\n  "vision_analyzer": {}\n}\n')
            with open(self.path, 'r', encoding='utf-8') as f:
                self.editor.setPlainText(f.read())
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载配置失败: {str(e)}")

    def save_and_apply(self):
        try:
            # 校验 JSON
            content = self.editor.toPlainText()
            json.loads(content)
            with open(self.path, 'w', encoding='utf-8') as f:
                f.write(content)
            reload_prompt_config()
            QMessageBox.information(self, "成功", "已保存并应用新的提示词配置")
        except json.JSONDecodeError as je:
            QMessageBox.critical(self, "格式错误", f"JSON 解析失败: {str(je)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")

            self.table.setItem(row, 4, QTableWidgetItem(str(stats.get("in_use"))))


class ShipmentDetailDialog(QDialog):
    """货运记录详情对话框"""

    def __init__(self, shipment_id: int, shipment_manager: ShipmentManager, parent=None):
        super().__init__(parent)
        self.shipment_id = shipment_id
        self.shipment_manager = shipment_manager
        self.screenshot_path = None

        self.setWindowTitle(f"货运记录详情 - ID: {shipment_id}")

        # 设置窗口大小为屏幕的75%并居中
        screen = QApplication.primaryScreen().geometry()
        width = int(screen.width() * 0.75)
        height = int(screen.height() * 0.75)
        self.resize(width, height)

        # 居中显示
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.move(x, y)

        self.setModal(True)

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 创建垂直分割器来控制上下部分比例
        main_splitter = QSplitter(Qt.Vertical)

        # 上部分：基本信息和佐证截图
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)  # 移除上部分外边距

        # 左侧：基本信息卡片（移除标题）
        basic_card = ModernCard("")  # 空标题
        basic_card.layout.setContentsMargins(6, 3, 6, 3)  # 进一步减少内边距
        basic_card.layout.setSpacing(1)  # 进一步减少间距
        self.basic_info_layout = QGridLayout()
        self.basic_info_layout.setVerticalSpacing(-2)  # 负间距使行更紧凑
        self.basic_info_layout.setHorizontalSpacing(4)  # 更紧凑的水平间距
        self.basic_info_layout.setContentsMargins(0, 0, 0, 0)  # 无边距
        basic_card.add_layout(self.basic_info_layout)
        top_layout.addWidget(basic_card, 3)  # 调整比例，占3/4宽度

        # 右侧：图片预览卡片（移除标题）
        image_card = ModernCard("")  # 空标题
        image_card.layout.setContentsMargins(3, 3, 3, 3)  # 最小内边距
        image_card.layout.setSpacing(0)  # 无间距

        # 图片显示标签 - 充满整个右侧区域，使用ClickableLabel来避免线程问题
        self.preview_image_label = ClickableLabel("暂无佐证截图")
        self.preview_image_label.setAlignment(Qt.AlignCenter)
        self.preview_image_label.setMinimumSize(120, 80)  # 设置最小尺寸
        self.preview_image_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许扩展
        self.preview_image_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #e0e0e0;
                border-radius: 8px;
                background-color: #f9f9f9;
                color: #999;
                font-size: 11px;
            }
        """)
        # 连接点击信号到查看完整截图方法
        self.preview_image_label.clicked.connect(self.view_full_screenshot)

        image_card.add_widget(self.preview_image_label)
        top_layout.addWidget(image_card, 1)  # 占1/4宽度

        main_splitter.addWidget(top_widget)

        # 下部分：时间节点卡片
        dates_card = ModernCard("物流时间节点")
        dates_card.title_label.setFont(QFont("Segoe UI", 10, QFont.Weight.DemiBold))  # 进一步减小标题字体
        dates_card.layout.setContentsMargins(10, 5, 10, 10)  # 调整下部分边距
        self.dates_table = ModernTableWidget()
        self.dates_table.setColumnCount(5)
        self.dates_table.setHorizontalHeaderLabels(["日期", "类型", "地点", "描述", "状态"])

        # 设置表格列宽
        header = self.dates_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 120)  # 日期列
        header.resizeSection(1, 100)  # 类型列
        header.resizeSection(2, 150)  # 地点列
        header.resizeSection(3, 200)  # 描述列

        dates_card.add_widget(self.dates_table)
        main_splitter.addWidget(dates_card)

        # 设置分割器比例：上部分1，下部分4（减少上部分高度）
        main_splitter.setSizes([1, 4])
        layout.addWidget(main_splitter)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_data(self):
        """加载数据"""
        # 加载基本信息
        details = self.shipment_manager.get_shipment_details(self.shipment_id)
        if details:
            basic_info = details['basic_info']

            # 清空并重新添加基本信息
            while self.basic_info_layout.count():
                child = self.basic_info_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

            # 处理提单号或箱号显示
            bill_display = basic_info.get('bill_of_lading') or basic_info.get('container_number') or ''

            fields = [
                ("提单号/箱号", bill_display),
                ("船公司", basic_info.get('carrier_company', '')),
                ("状态", basic_info.get('status', '')),
                ("预计到港时间", basic_info.get('estimated_arrival_time', '')),
                ("备注", basic_info.get('remarks', '')),
                ("创建时间", basic_info.get('created_at', '')),
                ("更新时间", basic_info.get('updated_at', ''))
            ]

            # 使用网格布局，分两列显示以节省垂直空间
            for i, (label, value) in enumerate(fields):
                row = i // 2
                col = (i % 2) * 2

                label_widget = QLabel(f"{label}:")
                label_widget.setStyleSheet("font-weight: 600; color: #666; font-size: 10px; margin: 1px 0px;")  # 进一步减小字体和边距
                value_widget = QLabel(str(value))
                value_widget.setStyleSheet("color: #424242; font-weight: 500; font-size: 10px; margin: 1px 0px;")  # 进一步减小字体和边距

                self.basic_info_layout.addWidget(label_widget, row, col)
                self.basic_info_layout.addWidget(value_widget, row, col + 1)

            # 加载佐证截图
            self.screenshot_path = basic_info.get('evidence_screenshot')
            self.load_screenshot_preview()

            # 加载时间节点
            dates = details['dates']
            self.dates_table.setRowCount(len(dates))

            if dates:
                for row, date_info in enumerate(dates):
                    # 格式化日期显示
                    date_str = date_info.get('date', '')
                    if date_str and 'T' in date_str:
                        date_str = date_str.split('T')[0]  # 只显示日期部分

                    # 填充表格数据
                    self.dates_table.setItem(row, 0, QTableWidgetItem(date_str))
                    self.dates_table.setItem(row, 1, QTableWidgetItem(date_info.get('type', '')))
                    self.dates_table.setItem(row, 2, QTableWidgetItem(date_info.get('location', '')))
                    self.dates_table.setItem(row, 3, QTableWidgetItem(date_info.get('description', '')))
                    self.dates_table.setItem(row, 4, QTableWidgetItem(date_info.get('status', '')))

                print(f"[INFO] 加载了 {len(dates)} 条时间节点记录")
            else:
                # 显示无数据提示
                self.dates_table.setRowCount(1)
                no_data_item = QTableWidgetItem("暂无时间节点数据")
                no_data_item.setTextAlignment(Qt.AlignCenter)
                self.dates_table.setItem(0, 0, no_data_item)
                self.dates_table.setSpan(0, 0, 1, 5)  # 合并所有列
                print(f"[INFO] 该货运记录暂无时间节点数据")

    def load_screenshot_preview(self):
        """加载佐证截图预览"""
        if self.screenshot_path and os.path.exists(self.screenshot_path):
            try:
                # 加载图片
                pixmap = QPixmap(self.screenshot_path)
                if not pixmap.isNull():
                    # 获取预览区域的实际大小
                    label_size = self.preview_image_label.size()
                    if label_size.width() < 120:  # 如果还没有正确设置大小，使用默认值
                        label_size = QSize(200, 120)

                    # 缩放图片以充满预览区域
                    scaled_pixmap = pixmap.scaled(
                        label_size,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.preview_image_label.setPixmap(scaled_pixmap)
                    self.preview_image_label.setScaledContents(True)  # 允许内容缩放
                    self.preview_image_label.setStyleSheet("""
                        QLabel {
                            border: 2px solid #e0e0e0;
                            border-radius: 8px;
                            background-color: #fff;
                        }
                    """)

                    # ClickableLabel已经有内置的点击处理和光标设置

                else:
                    self.preview_image_label.setText("无法加载图片")

            except Exception as e:
                self.preview_image_label.setText(f"加载图片出错: {str(e)}")
                print(f"[ERROR] 加载截图预览失败: {e}")
        else:
            self.preview_image_label.setText("暂无佐证截图")

    def view_full_screenshot(self):
        """查看完整截图"""
        if self.screenshot_path and os.path.exists(self.screenshot_path):
            try:
                dialog = ImageViewerDialog(self.screenshot_path, self)
                dialog.exec()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法打开图片: {str(e)}")
        else:
            QMessageBox.information(self, "提示", "暂无佐证截图可查看")

    def open_screenshot_folder(self):
        """打开截图所在文件夹"""
        if self.screenshot_path and os.path.exists(self.screenshot_path):
            folder_path = os.path.dirname(self.screenshot_path)
            QDesktopServices.openUrl(QUrl.fromLocalFile(folder_path))


class TaskStatusDialog(QDialog):
    """任务状态查看器对话框"""

    def __init__(self, task_manager: TaskManager, task_processor: TaskProcessor, parent=None):
        super().__init__(parent)
        self.task_manager = task_manager
        self.task_processor = task_processor
        self.setWindowTitle("任务状态查看器")
        self.resize(800, 600)
        self.setModal(False)  # 非模态对话框，允许同时操作主窗口

        # 创建自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_status)
        self.refresh_timer.start(3000)  # 每3秒刷新一次

        self.setup_ui()
        self.refresh_status()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 顶部控制区域
        control_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("立即刷新")
        self.refresh_btn.clicked.connect(self.refresh_status)

        self.auto_refresh_checkbox = QCheckBox("自动刷新 (3秒)")
        self.auto_refresh_checkbox.setChecked(True)
        self.auto_refresh_checkbox.toggled.connect(self.toggle_auto_refresh)

        control_layout.addWidget(self.refresh_btn)
        control_layout.addWidget(self.auto_refresh_checkbox)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        # 状态信息区域
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 9))
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        layout.addWidget(self.status_text)

        # 底部按钮
        button_box = QHBoxLayout()
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_box.addStretch()
        button_box.addWidget(close_btn)
        layout.addLayout(button_box)

    def toggle_auto_refresh(self, enabled: bool):
        """切换自动刷新"""
        if enabled:
            self.refresh_timer.start(3000)
        else:
            self.refresh_timer.stop()

    def refresh_status(self):
        """刷新任务状态"""
        try:
            from datetime import datetime

            status_info = []
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            status_info.append(f"📊 任务状态查看器 - {current_time}")
            status_info.append("=" * 60)

            # 处理器基本信息
            processor_status = self.task_processor.get_status() if self.task_processor else {"total": 0, "pending": 0, "processing": 0, "completed": 0}
            running_status = "🟢 运行中" if processor_status.get('running', False) else "🔴 已停止"

            # 修正模式显示逻辑 - 适配新的定时任务处理器
            if self.task_processor and hasattr(self.task_processor, '__class__') and 'ScheduledTaskProcessor' in str(self.task_processor.__class__):
                mode = "定时任务模式"
            else:
                mode = "传统模式"

            status_info.append(f"🔧 任务处理器状态: {running_status}")
            status_info.append(f"处理模式: {mode}")
            status_info.append("")

            if self.task_processor and hasattr(self.task_processor, '__class__') and 'ScheduledTaskProcessor' in str(self.task_processor.__class__):
                # 定时任务模式状态
                status_info.append("🌐 网页抓取阶段:")
                scraping_info = processor_status.get('scraping', {})
                active_scraping = scraping_info.get('active_tasks_count', 0)
                max_scraping = scraping_info.get('max_concurrent_tasks', 0)
                status_info.append(f"   活跃任务: {active_scraping}/{max_scraping}")
                check_interval = scraping_info.get('check_interval', 10)
                status_info.append(f"   检查间隔: {check_interval}秒")

                # 显示活跃的抓取任务
                active_scraping_tasks = scraping_info.get('active_tasks', {})
                if active_scraping_tasks:
                    status_info.append("   正在处理的任务:")
                    for task_id, task_info in active_scraping_tasks.items():
                        duration = int(task_info.get('duration_seconds', 0))
                        task_name = task_info.get('task_name', 'Unknown')
                        status_info.append(f"     • {task_name} (运行 {duration}秒)")
                else:
                    status_info.append("   当前无抓取任务")

                status_info.append("")
                status_info.append("🤖 AI分析阶段:")
                ai_info = processor_status.get('ai_analysis', {})
                active_ai = ai_info.get('active_tasks_count', 0)
                max_ai = ai_info.get('max_concurrent_tasks', 0)
                status_info.append(f"   活跃任务: {active_ai}/{max_ai}")
                ai_check_interval = ai_info.get('check_interval', 5)
                status_info.append(f"   检查间隔: {ai_check_interval}秒")

                # 显示活跃的AI分析任务
                active_ai_tasks = ai_info.get('active_tasks', {})
                if active_ai_tasks:
                    status_info.append("   正在处理的任务:")
                    for task_id, task_info in active_ai_tasks.items():
                        duration = int(task_info.get('duration_seconds', 0))
                        task_name = task_info.get('task_name', 'Unknown')
                        status_info.append(f"     • {task_name} (运行 {duration}秒)")
                else:
                    status_info.append("   当前无AI分析任务")

                # 阶段统计信息
                stage_stats = self.task_manager.get_stage_statistics()
                if stage_stats:
                    status_info.append("")
                    status_info.append("📈 阶段统计:")

                    scraping_stats = stage_stats.get('scraping', {})
                    if scraping_stats:
                        status_info.append(f"   网页抓取: 待处理 {scraping_stats.get('pending', 0)}, "
                                         f"进行中 {scraping_stats.get('processing', 0)}, "
                                         f"已完成 {scraping_stats.get('completed', 0)}")

                    ai_stats = stage_stats.get('ai_analysis', {})
                    if ai_stats:
                        status_info.append(f"   AI分析: 待处理 {ai_stats.get('pending', 0)}, "
                                         f"进行中 {ai_stats.get('processing', 0)}, "
                                         f"已完成 {ai_stats.get('completed', 0)}")
            else:
                # 传统模式状态
                active_count = processor_status.get('active_tasks_count', 0)
                max_count = processor_status.get('max_concurrent_tasks', 0)
                status_info.append(f"📋 传统模式状态:")
                status_info.append(f"   活跃任务: {active_count}/{max_count}")
                status_info.append(f"   工作线程: {processor_status.get('worker_threads_count', 0)} 个")

                # 显示活跃任务
                active_tasks = processor_status.get('active_tasks', {})
                if active_tasks:
                    status_info.append("   正在处理的任务:")
                    for task_id, task_info in active_tasks.items():
                        duration = int(task_info.get('duration_seconds', 0))
                        task_name = task_info.get('task_name', 'Unknown')
                        status_info.append(f"     • {task_name} (运行 {duration}秒)")
                else:
                    status_info.append("   当前无任务")

                # 总体统计信息
                overall_stats = self.task_manager.get_task_statistics()
                if overall_stats:
                    status_info.append("")
                    status_info.append("📈 任务统计:")
                    status_dist = overall_stats.get('status_distribution', {})
                    for status_name, count in status_dist.items():
                        status_info.append(f"   {status_name}: {count}")

            # 显示结果
            self.status_text.setPlainText("\n".join(status_info))

            # 滚动到底部
            cursor = self.status_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            self.status_text.setTextCursor(cursor)

        except Exception as e:
            error_msg = f"刷新状态时出错: {str(e)}"
            self.status_text.setPlainText(error_msg)

    def closeEvent(self, event):
        """关闭对话框时停止定时器"""
        self.refresh_timer.stop()
        event.accept()


class PromptHistoryDialog(QDialog):
    """AI 提示词历史版本管理器"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("AI 提示词历史版本")
        self.resize(720, 420)
        layout = QVBoxLayout(self)

        # 顶部按钮区
        btns = QHBoxLayout()
        self.btn_refresh = QPushButton("刷新")
        self.btn_snapshot = QPushButton("保存当前为历史")
        self.btn_restore = QPushButton("恢复所选为当前")
        self.btn_delete = QPushButton("删除所选")
        btns.addWidget(self.btn_refresh)
        btns.addWidget(self.btn_snapshot)
        btns.addWidget(self.btn_restore)
        btns.addWidget(self.btn_delete)
        layout.addLayout(btns)

        # 列表
        self.table = QTableWidget(0, 4)
        self.table.setHorizontalHeaderLabels(["ID", "标签", "创建时间", "摘要"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)

        from ai.prompt_history import list_history, snapshot_current, restore_version, delete_version
        self._list_history = list_history
        self._snapshot_current = snapshot_current
        self._restore_version = restore_version
        self._delete_version = delete_version

        self.btn_refresh.clicked.connect(self.refresh)
        self.btn_snapshot.clicked.connect(self.snapshot)
        self.btn_restore.clicked.connect(self.restore)
        self.btn_delete.clicked.connect(self.delete)

        self.refresh()

    def refresh(self):
        try:
            versions = self._list_history()
            self.table.setRowCount(len(versions))
            for r, v in enumerate(versions):
                self.table.setItem(r, 0, QTableWidgetItem(v.get('id', '')))
                self.table.setItem(r, 1, QTableWidgetItem(v.get('label', '')))
                self.table.setItem(r, 2, QTableWidgetItem(v.get('created_at', '')))
                summary = json.dumps(v.get('config', {}))[:160] + '...'
                self.table.setItem(r, 3, QTableWidgetItem(summary))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载历史失败: {str(e)}")

    def _current_id(self):
        row = self.table.currentRow()
        if row < 0:
            return None
        return self.table.item(row, 0).text()

    def snapshot(self):
        try:
            label, ok = QInputDialog.getText(self, "保存历史", "为当前配置填写一个标签：")
            if not ok:
                return
            self._snapshot_current(label.strip())
            QMessageBox.information(self, "成功", "已保存当前配置为历史版本")
            self.refresh()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存历史失败: {str(e)}")

    def restore(self):
        vid = self._current_id()
        if not vid:
            QMessageBox.warning(self, "提示", "请先选择一个历史版本")
            return
        try:
            if self._restore_version(vid):
                QMessageBox.information(self, "成功", "已恢复所选版本为当前配置")
            else:
                QMessageBox.warning(self, "提示", "恢复失败，请检查该版本是否有效")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"恢复失败: {str(e)}")

    def delete(self):
        vid = self._current_id()
        if not vid:
            QMessageBox.warning(self, "提示", "请先选择一个历史版本")
            return
        try:
            if self._delete_version(vid):
                QMessageBox.information(self, "成功", "已删除所选历史版本")
                self.refresh()
            else:
                QMessageBox.warning(self, "提示", "删除失败，未找到该版本")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")





class BatchAddDialog(QDialog):
    """批量添加货运记录（支持直接粘贴Excel列）"""
    def __init__(self, shipment_manager: ShipmentManager, parent=None):
        super().__init__(parent)
        self.shipment_manager = shipment_manager
        self.valid_items = []  # [{number, type, company}]
        self.invalid_items = []  # [number]
        self.setWindowTitle("批量添加记录")
        self.resize(720, 520)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        tip = QLabel("将Excel中的提单号/箱号一列直接粘贴到下面，多行或带制表符均可。支持自动去重与承运人识别。")
        tip.setStyleSheet("color:#666;")
        layout.addWidget(tip)

        self.input_edit = QTextEdit()
        self.input_edit.setPlaceholderText("在此粘贴：每行/每列一个，如\nMSCU1234567\nMEDUVS935363\n...")
        self.input_edit.textChanged.connect(self.on_text_changed)
        layout.addWidget(self.input_edit, 1)

        # 状态与选项
        opts_layout = QHBoxLayout()
        self.dedup_check = QCheckBox("自动去重")
        self.dedup_check.setChecked(True)
        self.dedup_check.stateChanged.connect(self.on_text_changed)
        opts_layout.addWidget(self.dedup_check)
        opts_layout.addStretch()
        self.stats_label = QLabel("待解析：0，有效：0，无效：0")
        opts_layout.addWidget(self.stats_label)
        layout.addLayout(opts_layout)

        # 预览表
        self.preview = QTableWidget(0, 4)
        self.preview.setHorizontalHeaderLabels(["编号", "船公司", "类型", "备注"])
        self.preview.horizontalHeader().setStretchLastSection(True)
        self.preview.verticalHeader().setVisible(False)
        self.preview.setEditTriggers(QTableWidget.NoEditTriggers)
        layout.addWidget(self.preview, 2)

        # 按钮区
        btn_box = QDialogButtonBox()
        self.btn_import = btn_box.addButton("开始导入", QDialogButtonBox.AcceptRole)
        self.btn_cancel = btn_box.addButton(QDialogButtonBox.Cancel)
        self.btn_import.setEnabled(False)
        self.btn_import.clicked.connect(self.on_import)
        self.btn_cancel.clicked.connect(self.reject)
        layout.addWidget(btn_box)

    def on_text_changed(self):
        self.parse_and_preview()

    def is_container_number(self, s: str) -> bool:
        # 简单ISO 6346格式：4字母+7数字（不校验校验位）
        return bool(re.fullmatch(r"[A-Z]{4}\d{7}", s))

    def split_tokens(self, text: str):
        # 支持换行、制表符、逗号、分号、空白分隔
        raw = re.split(r"[\s,;]+", text.strip()) if text.strip() else []
        items = [t.upper() for t in raw if t]
        if self.dedup_check.isChecked():
            seen = set()
            deduped = []
            for t in items:
                if t not in seen:
                    seen.add(t)
                    deduped.append(t)
            return deduped
        return items

    def parse_and_preview(self):
        text = self.input_edit.toPlainText()
        tokens = self.split_tokens(text)
        self.valid_items = []
        self.invalid_items = []

        self.preview.setRowCount(0)

        for tok in tokens:
            info = get_company_info(tok)
            if info:
                item_type = 'container' if self.is_container_number(tok) else 'bill'
                self.valid_items.append({
                    'number': tok,
                    'type': item_type,
                    'company': info.get('company', '')
                })
                self._add_preview_row(tok, info.get('company', ''), '箱号' if item_type=='container' else '提单', 'OK')
            else:
                self.invalid_items.append(tok)
                self._add_preview_row(tok, '-', '-', '未识别承运人/格式未知')

        self.stats_label.setText(f"待解析：{len(tokens)}，有效：{len(self.valid_items)}，无效：{len(self.invalid_items)}")
        self.btn_import.setEnabled(len(self.valid_items) > 0)

    def _add_preview_row(self, number: str, company: str, kind: str, note: str):
        row = self.preview.rowCount()
        self.preview.insertRow(row)
        self.preview.setItem(row, 0, QTableWidgetItem(number))
        self.preview.setItem(row, 1, QTableWidgetItem(company))
        self.preview.setItem(row, 2, QTableWidgetItem(kind))
        self.preview.setItem(row, 3, QTableWidgetItem(note))

    def on_import(self):
        if not self.valid_items:
            QMessageBox.warning(self, "提示", "没有可导入的数据")
            return

        success, failed = 0, []
        for item in self.valid_items:
            try:
                if item['type'] == 'container':
                    self.shipment_manager.create_shipment_record(
                        container_number=item['number'],
                        carrier_company=item['company'],
                        created_by='批量导入'
                    )
                else:
                    self.shipment_manager.create_shipment_record(
                        bill_of_lading=item['number'],
                        carrier_company=item['company'],
                        created_by='批量导入'
                    )
                success += 1
            except Exception as e:
                failed.append((item['number'], str(e)))

        msg = f"导入完成：成功 {success} 条"
        if failed:
            msg += f"，失败 {len(failed)} 条\n"
            for n, err in failed[:5]:
                msg += f"- {n}: {err}\n"
            if len(failed) > 5:
                msg += f"... 其余 {len(failed)-5} 条省略"
        QMessageBox.information(self, "结果", msg)
        self.accept()

class ContainerHelperApp(QMainWindow):
    """货运记录管理系统主应用"""

    def __init__(self):
        super().__init__()
        self.shipment_manager = ShipmentManager()
        # 设置UI刷新回调，让shipment_manager能触发界面刷新（通过主线程调度，避免跨线程卡死）
        self.shipment_manager.ui_refresh_callback = lambda: QTimer.singleShot(0, self.load_shipments)
        self.task_manager = TaskManager()
        # TaskProcessor 将在 setup_ui 完成后初始化
        self.task_processor = None
        self.current_carrier_info = None

        self.setWindowTitle("货运记录管理系统 - Container Helper")
        self.showMaximized()  # 最大化显示
        self.setMinimumSize(1200, 800)

        # 设置应用样式 - Navicat风格
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                color: #495057;
            }
            QSplitter::handle {
                background-color: #dee2e6;
                width: 1px;
                height: 1px;
            }
            QSplitter::handle:hover {
                background-color: #adb5bd;
            }
        """)

        self.setup_menu_bar()
        # 确保菜单栏显示
        self.menuBar().setVisible(True)
        self.setup_ui()
        self.load_shipments()

        # 默认自动启动任务处理器（不再依赖环境变量）
        if self.task_processor is not None:
            self.task_processor.start()
        # 初始化菜单文案
        if hasattr(self, 'update_task_processor_action_text'):
            self.update_task_processor_action_text()

    def closeEvent(self, event):
        """应用关闭时的清理工作"""
        try:
            # 停止状态刷新定时器
            if hasattr(self, 'status_refresh_timer') and self.status_refresh_timer.isActive():
                self.status_refresh_timer.stop()
                print("[STOP] 状态刷新定时器已停止")

            # 停止任务处理器
            if hasattr(self, 'task_processor') and self.task_processor is not None and self.task_processor.running:
                self.task_processor.stop()
        except Exception as e:
            print(f"关闭任务处理器时出错: {e}")
        finally:
            event.accept()

    def setup_menu_bar(self):
        """设置菜单栏 - Navicat紧凑风格"""
        menubar = self.menuBar()

        # 设置菜单栏样式 - 更紧凑的Navicat风格
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                padding: 1px;
                font-size: 9px;
                height: 20px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 2px 6px;
                margin: 0px;
                border-radius: 1px;
                height: 18px;
            }
            QMenuBar::item:selected {
                background-color: #e9ecef;
                color: #495057;
            }
            QMenuBar::item:pressed {
                background-color: #dee2e6;
            }
            QMenu {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 2px;
                padding: 1px;
                font-size: 9px;
            }
            QMenu::item {
                padding: 3px 10px;
                margin: 0px;
                border-radius: 1px;
                height: 16px;
            }
            QMenu::item:selected {
                background-color: #e9ecef;
                color: #495057;
            }
            QMenu::separator {
                height: 1px;
                background-color: #dee2e6;
                margin: 1px 2px;
            }
        """)

        # 系统菜单
        system_menu = menubar.addMenu("系统")

        # 船公司管理
        carrier_action = QAction("船公司管理", self)
        carrier_action.setStatusTip("管理船公司信息和配置")
        carrier_action.triggered.connect(self.open_carrier_management)
        system_menu.addAction(carrier_action)

        # AI模型管理
        ai_model_action = QAction("AI模型管理", self)
        ai_model_action.setStatusTip("管理AI服务提供商和模型配置")
        ai_model_action.triggered.connect(self.open_ai_model_management)
        system_menu.addAction(ai_model_action)

        system_menu.addSeparator()

        # 退出
        exit_action = QAction("退出", self)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        system_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具")

        # 任务处理器控制
        self.task_processor_action = QAction("停止任务处理器", self) if getattr(self, 'task_processor', None) and self.task_processor and self.task_processor.running else QAction("启动任务处理器", self)
        self.task_processor_action.setStatusTip("启动或停止任务处理器")
        self.task_processor_action.triggered.connect(self.toggle_task_processor)
        tools_menu.addAction(self.task_processor_action)

        # 任务状态查看器
        task_status_action = QAction("任务状态查看器", self)
        task_status_action.setStatusTip("查看任务处理状态和统计信息")
        task_status_action.triggered.connect(self.open_task_status_viewer)
        tools_menu.addAction(task_status_action)

        tools_menu.addSeparator()

        # 连接池状态
        pool_action = QAction("连接池状态", self)
        pool_action.setStatusTip("查看SQLite连接池的当前状态")
        pool_action.triggered.connect(self.open_connection_pool_stats)
        tools_menu.addAction(pool_action)

        # 提示词历史
        prompts_history_action = QAction("AI提示词历史版本", self)
        prompts_history_action.setStatusTip("管理与切换AI提示词历史版本")
        prompts_history_action.triggered.connect(self.open_prompt_history)
        tools_menu.addAction(prompts_history_action)

        tools_menu.addSeparator()

        # AI调用日志
        ai_log_action = QAction("AI调用日志", self)
        ai_log_action.setStatusTip("查看AI服务调用统计和日志")
        ai_log_action.triggered.connect(self.open_ai_log_viewer)
        tools_menu.addAction(ai_log_action)

        # 数据库管理
        db_action = QAction("数据库管理", self)
        db_action.setStatusTip("数据库维护和管理工具")
        db_action.triggered.connect(self.open_database_management)
        tools_menu.addAction(db_action)

        # 编辑AI提示词配置
        edit_prompts_action = QAction("编辑AI提示词配置", self)
        edit_prompts_action.setStatusTip("在应用内打开并编辑 config/prompts.json，保存后立即生效")
        edit_prompts_action.triggered.connect(self.open_prompt_editor)
        tools_menu.addAction(edit_prompts_action)

        # 重新加载AI提示词配置（热加载）
        reload_prompts_action = QAction("重新加载AI提示词配置", self)
        reload_prompts_action.setStatusTip("从config/prompts.json重新加载AI提示词，不重启应用")
        reload_prompts_action.triggered.connect(self.reload_ai_prompts)
        tools_menu.addAction(reload_prompts_action)

        help_menu = menubar.addMenu("帮助")

        # 关于
        about_action = QAction("关于", self)
        about_action.setStatusTip("关于Container Helper")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_ui(self):
        """设置用户界面 - 紧凑设计"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 紧凑边距
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(3)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)

        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)

        # 设置分割器比例 - 缩小左侧面板宽度三分之一
        splitter.setSizes([230, 970])

        # 现在初始化定时任务处理器，此时 handle_task_completion 方法已经定义
        self.task_processor = ScheduledTaskProcessor(
            scraping_interval=10,      # 10秒检查一次网页抓取任务
            ai_interval=5,             # 5秒检查一次AI分析任务
            status_update_interval=30, # 30秒进行一次状态维护
            max_scraping_tasks=2,      # 最多2个并发抓取任务
            max_ai_tasks=3,            # 最多3个并发AI分析任务
            completion_callback=self.handle_task_completion
        )
        print("[INFO] 定时任务处理器初始化完成")

        # 设置状态更新定时器
        self.setup_status_refresh_timer()
        # 初始化“完成任务精准刷新”的检查点
        self._last_completed_scan = None

    def create_left_panel(self) -> QWidget:
        """创建左侧输入面板 - Navicat紧凑风格"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(3, 3, 3, 3)
        layout.setSpacing(3)

        # 添加记录区域 - 紧凑版
        input_group = QGroupBox("添加记录")
        input_group.setStyleSheet("""
            QGroupBox {
                font-size: 10px;
                font-weight: bold;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                margin-top: 6px;
                padding-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 6px;
                padding: 0 3px 0 3px;
            }
        """)
        input_layout = QVBoxLayout(input_group)
        input_layout.setContentsMargins(6, 6, 6, 6)
        input_layout.setSpacing(4)

        # 输入框 - 紧凑版
        self.bill_input = QLineEdit()
        self.bill_input.setPlaceholderText("提单号或集装箱号...")
        self.bill_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 2px;
                padding: 4px 6px;
                font-size: 10px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #80bdff;
                outline: 0;
            }
        """)
        self.bill_input.textChanged.connect(self.on_bill_input_changed)
        input_layout.addWidget(self.bill_input)

        # 按钮布局 - 紧凑版
        button_layout = QHBoxLayout()
        button_layout.setSpacing(3)

        self.detect_btn = QPushButton("检测")
        self.detect_btn.setMaximumHeight(24)
        self.detect_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 2px 8px;
                border-radius: 2px;
                font-size: 10px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        self.detect_btn.clicked.connect(self.detect_carrier)

        self.add_btn = QPushButton("添加")
        self.add_btn.setMaximumHeight(24)
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 2px 8px;
                border-radius: 2px;
                font-size: 10px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        self.add_btn.clicked.connect(self.add_shipment)
        self.add_btn.setEnabled(False)

        # 批量添加按钮
        self.batch_btn = QPushButton("批量添加")
        self.batch_btn.setMaximumHeight(24)
        self.batch_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 2px;
                padding: 3px 8px;
                font-size: 10px;
            }
            QPushButton:hover { background-color: #5a6268; }
        """)
        self.batch_btn.clicked.connect(self.open_batch_add)

        button_layout.addWidget(self.detect_btn)
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.batch_btn)
        input_layout.addLayout(button_layout)

        layout.addWidget(input_group)

        # 承运人信息区域 - 紧凑版
        self.carrier_info_widget = CarrierInfoWidget()
        layout.addWidget(self.carrier_info_widget)

        # 添加弹性空间
        layout.addStretch()

        return panel

    def create_right_panel(self) -> QWidget:
        """创建右侧列表面板 - 紧凑设计"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(3, 3, 3, 3)
        layout.setSpacing(3)

        # 搜索工具栏 - Navicat紧凑风格
        search_toolbar = QHBoxLayout()
        search_toolbar.setSpacing(3)

        # 搜索输入框
        search_label = QLabel("搜索:")
        search_label.setStyleSheet("font-size: 9px; color: #495057; padding: 2px;")
        search_toolbar.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("提单号、箱号...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 2px;
                padding: 2px 4px;
                font-size: 9px;
                background-color: white;
                height: 16px;
            }
            QLineEdit:focus {
                border-color: #80bdff;
                outline: 0;
            }
        """)
        self.search_input.textChanged.connect(self.on_search_changed)
        self.search_input.setMaximumWidth(120)
        search_toolbar.addWidget(self.search_input)

        # 状态筛选
        status_label = QLabel("状态:")
        status_label.setStyleSheet("font-size: 9px; color: #495057; padding: 2px;")
        search_toolbar.addWidget(status_label)

        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部状态", "进行中", "已完成", "异常"])
        self.status_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #ced4da;
                border-radius: 2px;
                padding: 2px 4px;
                font-size: 9px;
                background-color: white;
                height: 16px;
            }
            QComboBox:focus {
                border-color: #80bdff;
            }
            QComboBox::drop-down {
                border: none;
                width: 12px;
            }
            QComboBox::down-arrow {
                width: 8px;
                height: 8px;
            }
        """)
        self.status_combo.currentTextChanged.connect(self.on_search_changed)
        self.status_combo.setMaximumWidth(70)
        search_toolbar.addWidget(self.status_combo)

        # 船公司筛选
        carrier_label = QLabel("船公司:")
        carrier_label.setStyleSheet("font-size: 9px; color: #495057; padding: 2px;")
        search_toolbar.addWidget(carrier_label)

        self.carrier_combo = QComboBox()
        self.carrier_combo.addItem("全部船公司")
        self.carrier_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #ced4da;
                border-radius: 2px;
                padding: 2px 4px;
                font-size: 9px;
                background-color: white;
                height: 16px;
            }
            QComboBox:focus {
                border-color: #80bdff;
            }
            QComboBox::drop-down {
                border: none;
                width: 12px;
            }
            QComboBox::down-arrow {
                width: 8px;
                height: 8px;
            }
        """)
        self.carrier_combo.currentTextChanged.connect(self.on_search_changed)
        self.carrier_combo.setMaximumWidth(80)
        search_toolbar.addWidget(self.carrier_combo)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: 1px solid #ced4da;
                border-radius: 2px;
                padding: 2px 6px;
                font-size: 9px;
                color: #495057;
                height: 16px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_shipments_list)
        refresh_btn.setMaximumWidth(40)
        search_toolbar.addWidget(refresh_btn)

        search_toolbar.addStretch()
        layout.addLayout(search_toolbar)



        # 状态栏和按钮行 - 紧凑版
        status_layout = QHBoxLayout()
        status_layout.setSpacing(5)

        status_label = QLabel("记录列表")
        status_label.setStyleSheet("font-size: 11px; font-weight: bold; padding: 2px;")
        status_layout.addWidget(status_label)

        # 任务完成提示
        self.task_completion_notice = QLabel("")
        self.task_completion_notice.setStyleSheet("""
            QLabel {
                color: #F44336;
                font-size: 10px;
                font-weight: 600;
                margin-left: 5px;
            }
        """)
        self.task_completion_notice.hide()  # 初始隐藏
        status_layout.addWidget(self.task_completion_notice)

        status_layout.addStretch()
        layout.addLayout(status_layout)

        # 按钮行 - 放在记录列表标题下面
        button_layout = QHBoxLayout()
        button_layout.setSpacing(3)

        self.delete_btn = QPushButton("删除")
        self.delete_btn.setMaximumWidth(50)
        self.delete_btn.setMaximumHeight(20)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                padding: 2px 6px;
                border-radius: 2px;
                font-size: 9px;
                font-weight: normal;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_selected_shipment)
        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 表格 - 紧凑版
        self.shipments_table = ModernTableWidget()
        self.shipments_table.setColumnCount(12)
        self.shipments_table.setHorizontalHeaderLabels([
            "行号", "提单号/箱号", "船公司", "状态", "网页抓取", "AI分析", "预计到港时间", "物流节点", "佐证截图", "备注", "创建时间", "更新时间"
        ])
        self.shipments_table.doubleClicked.connect(self.show_shipment_details)
        self.shipments_table.cellClicked.connect(self.handle_status_click)

        # 应用AI调用日志页面的样式
        self.shipments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ecf0f1;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 9px;
                border: 1px solid #ecf0f1;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
        background-color: #FFFBE6;  /* 淡灰色选中背景，避免遮盖蓝色文字 */
        color: #212529;             /* 深色字体，保持可读性 */
            }
            QHeaderView::section {


                background-color: #34495e;
                color: white;
                padding: 6px;
                border: none;
                font-weight: bold;
                font-size: 9px;
            }
        """)

        # 修复样式定义（覆盖可能的损坏样式）
        self.shipments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ecf0f1;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 9px;
                border: 1px solid #ecf0f1;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #FFFBE6;
                color: #212529;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 6px;
                border: none;
                font-weight: bold;
                font-size: 9px;
            }
        """)

        self.shipments_table.setAlternatingRowColors(True)
        self.shipments_table.verticalHeader().setDefaultSectionSize(22)  # 紧凑行高
        self.shipments_table.verticalHeader().setVisible(True)  # 显示垂直表头（行号）

        # 为“提单号/箱号”列提供右键复制菜单
        self.shipments_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.shipments_table.customContextMenuRequested.connect(self.show_shipments_table_menu)

        # 设置列宽 - 紧凑版
        header = self.shipments_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Interactive)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 网页抓取
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # AI分析
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 预计到港时间
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 物流节点
        header.setSectionResizeMode(8, QHeaderView.Interactive)       # 佐证截图
        header.setSectionResizeMode(9, QHeaderView.Interactive)       # 备注
        header.setSectionResizeMode(10, QHeaderView.Interactive)      # 创建时间
        header.setSectionResizeMode(11, QHeaderView.Interactive)      # 更新时间
        self.shipments_table.setColumnWidth(0, 50)
        self.shipments_table.setColumnWidth(1, 120)
        self.shipments_table.setColumnWidth(4, 80)   # 网页抓取列宽
        self.shipments_table.setColumnWidth(5, 80)   # AI分析列宽
        self.shipments_table.setColumnWidth(7, 60)   # 物流节点
        self.shipments_table.setColumnWidth(9, 150)  # 备注
        self.shipments_table.setColumnWidth(10, 100) # 创建时间
        self.shipments_table.setColumnWidth(11, 100) # 更新时间

        layout.addWidget(self.shipments_table)

        # 移除操作按钮布局，删除按钮已移到上方

        return panel

    def show_shipments_table_menu(self, pos):
        """在表格上显示右键菜单，支持复制提单号/箱号"""
        try:
            index = self.shipments_table.indexAt(pos)
            if not index.isValid():
                return
            row = index.row()

            # 第1列（索引1）为“提单号/箱号”
            item = self.shipments_table.item(row, 1)
            value = item.text() if item else ""
            if not value:
                return

            menu = QMenu(self)
            copy_action = QAction("复制提单号/箱号", self)
            copy_action.triggered.connect(lambda: QApplication.clipboard().setText(value))
            menu.addAction(copy_action)

            # 也允许复制整行（可选，便于导出）
            def copy_full_row():
                cols = self.shipments_table.columnCount()
                texts = []
                for c in range(cols):
                    it = self.shipments_table.item(row, c)
                    texts.append(it.text() if it else "")
                QApplication.clipboard().setText("\t".join(texts))

            menu.addAction("复制整行", copy_full_row)

            global_pos = self.shipments_table.viewport().mapToGlobal(pos)
            menu.exec(global_pos)
        except Exception as e:
            # 安静失败，不影响主流程
            print(f"[WARN] 右键复制菜单异常: {e}")

    def on_bill_input_changed(self):
        """输入框内容变化处理"""
        text = self.bill_input.text().strip()
        self.detect_btn.setEnabled(bool(text))
        if not text:
            self.current_carrier_info = None
            self.carrier_info_widget.update_info(None)
            self.add_btn.setEnabled(False)

    def detect_carrier(self):
        """检测承运人"""
        bill_number = self.bill_input.text().strip()
        if not bill_number:
            return

        try:
            self.current_carrier_info = get_company_info(bill_number)
            self.carrier_info_widget.update_info(self.current_carrier_info)
            self.add_btn.setEnabled(bool(self.current_carrier_info))
        except Exception as e:
            QMessageBox.warning(self, "检测失败", f"承运人检测失败: {str(e)}")
            self.current_carrier_info = None
            self.carrier_info_widget.update_info(None)
            self.add_btn.setEnabled(False)

    def add_shipment(self):
        """添加货运记录并创建对应的查询任务"""
        bill_number = self.bill_input.text().strip()
        if not bill_number or not self.current_carrier_info:
            QMessageBox.warning(self, "警告", "请输入提单号并选择船公司")
            return

        try:
            # 创建货运记录（使用完整参数）
            shipment_id = self.shipment_manager.create_shipment_record(
                bill_of_lading=bill_number,
                carrier_company=self.current_carrier_info.get('company', ''),
                created_by=f"用户-{datetime.now().strftime('%Y%m%d')}"  # 添加创建者信息
            )

            print(f"[SUCCESS] 货运记录创建成功，ID: {shipment_id}")

            # 显示成功信息（包含更多详情）
            success_msg = f"货运记录创建成功！\n\n"
            success_msg += f"记录ID: {shipment_id}\n"
            success_msg += f"提单号: {bill_number}\n"
            success_msg += f"船公司: {self.current_carrier_info.get('company', '')}\n\n"
            success_msg += "系统将自动创建查询任务来获取物流信息。"

            QMessageBox.information(self, "创建成功", success_msg)

            # 清空输入
            self.bill_input.clear()
            self.current_carrier_info = None
            self.carrier_info_widget.update_info(None)
            self.add_btn.setEnabled(False)

            # 刷新列表
            self.load_shipments()

            # 启动状态监控（因为新任务已创建）
            self.start_status_monitoring()

            print(f"[INFO] 货运记录和关联任务创建完成")

        except Exception as e:
            error_msg = f"创建货运记录失败：{str(e)}"
            print(f"[ERROR] {error_msg}")
            QMessageBox.critical(self, "创建失败", error_msg)

    def open_batch_add(self):
        """打开批量添加对话框"""
        dlg = BatchAddDialog(self.shipment_manager, self)
        if dlg.exec() == QDialog.Accepted:
            # 刷新列表和状态监控
            self.load_shipments()
            self.start_status_monitoring()

    def load_shipments(self):
        """加载货运记录列表"""
        try:
            shipments = self.shipment_manager.search_shipment_records()
            self.update_shipments_table(shipments)
            self.update_carrier_filter(shipments)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载记录失败: {str(e)}")

    def update_shipments_table(self, shipments: List[Dict]):
        """更新货运记录表格"""
        self.shipments_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            # 处理提单号或箱号显示
            bill_display = shipment.get('bill_of_lading') or shipment.get('container_number') or ''

            # 处理预计到港时间显示
            eta_display = shipment.get('estimated_arrival_time', '') or ''
            if eta_display:
                try:
                    # 如果是datetime格式，转换为更友好的显示格式
                    if 'T' in eta_display:
                        eta_display = eta_display.split('T')[0]
                except:
                    pass

            # 获取物流节点数量
            try:
                import sqlite3
                conn = sqlite3.connect('db/shipment_records.db')
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (shipment['id'],))
                dates_count = cursor.fetchone()[0]
                conn.close()

                if dates_count > 0:
                    dates_display = f"{dates_count}条"
                else:
                    dates_display = "无"
            except:
                dates_display = "?"

            # 处理备注显示（截取前20个字符）
            remarks_display = shipment.get('remarks', '') or ''
            if len(remarks_display) > 20:
                remarks_display = remarks_display[:20] + '...'

            # 格式化显示时间
            created_at = shipment.get('created_at', '')
            updated_at = shipment.get('updated_at', '')
            if created_at and 'T' in created_at:
                created_at = created_at.split('T')[0]
            if updated_at and 'T' in updated_at:
                updated_at = updated_at.split('T')[0]

            # 获取任务状态信息（按记录ID精确匹配，避免同提单号/箱号的其他记录被影响）
            scraping_status, ai_status = self.get_task_stages_status(
                shipment.get('bill_of_lading') or shipment.get('container_number') or '',
                shipment.get('id')
            )

            # 第一列显示行号，将ID保存在item的UserRole中
            row_number_item = QTableWidgetItem(str(row + 1))
            row_number_item.setData(Qt.UserRole, shipment['id'])  # 保存真实ID
            self.shipments_table.setItem(row, 0, row_number_item)
            self.shipments_table.setItem(row, 1, QTableWidgetItem(bill_display))
            self.shipments_table.setItem(row, 2, QTableWidgetItem(shipment.get('carrier_company', '')))
            self.shipments_table.setItem(row, 3, QTableWidgetItem(shipment.get('status', '')))

            # 网页抓取状态（第4列）
            scraping_item = self.create_status_item(scraping_status)
            self.shipments_table.setItem(row, 4, scraping_item)

            # AI分析状态（第5列）
            ai_item = self.create_status_item(ai_status)
            self.shipments_table.setItem(row, 5, ai_item)

            self.shipments_table.setItem(row, 6, QTableWidgetItem(eta_display))    # 预计到港时间
            self.shipments_table.setItem(row, 7, QTableWidgetItem(dates_display))  # 物流节点数量

            # 处理佐证截图显示 - 使用可点击的组件
            screenshot_path = shipment.get('evidence_screenshot')
            if screenshot_path and os.path.exists(screenshot_path):
                # 创建可点击的标签
                screenshot_label = ClickableLabel("查看截图")
                screenshot_label.clicked.connect(lambda path=screenshot_path: self.view_screenshot(path))
                self.shipments_table.setCellWidget(row, 8, screenshot_label)
            else:
                self.shipments_table.setItem(row, 8, QTableWidgetItem(''))

            self.shipments_table.setItem(row, 9, QTableWidgetItem(remarks_display))
            self.shipments_table.setItem(row, 10, QTableWidgetItem(created_at))
            self.shipments_table.setItem(row, 11, QTableWidgetItem(updated_at))

        # 表格更新完成后，检查并管理定时器
        if hasattr(self, 'status_refresh_timer'):
            self.check_and_manage_timer()

    def get_task_stages_status(self, tracking_number: str, record_id: int = None):
        """获取任务的两个阶段状态（优先按记录ID精准匹配，避免同号多记录互相影响）"""
        if not tracking_number:
            return {"status": "无任务", "clickable": False}, {"status": "无任务", "clickable": False}

        try:
            import sqlite3

            # 首先检查货运记录状态：若传入 record_id 则按ID判断；否则按号码判断最近一条
            shipment_conn = sqlite3.connect('db/shipment_records.db')
            try:
                shipment_cursor = shipment_conn.cursor()
                if record_id is not None:
                    shipment_cursor.execute("SELECT status FROM shipment_records WHERE id = ?", (record_id,))
                else:
                    shipment_cursor.execute("""
                        SELECT status FROM shipment_records
                        WHERE bill_of_lading = ? OR container_number = ?
                        ORDER BY updated_at DESC LIMIT 1
                    """, (tracking_number, tracking_number))
                shipment_record = shipment_cursor.fetchone()

                if shipment_record and shipment_record[0] == '已完成':
                    # 货运记录已完成，返回已完成状态，不再查询任务队列
                    return (
                        {"status": "已完成", "clickable": False, "task_id": None},
                        {"status": "已完成", "clickable": False, "task_id": None}
                    )
            finally:
                shipment_conn.close()

            # 货运记录未完成，查询任务队列状态
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()

            status_mapping = {
                'pending': '待处理',
                'processing': '进行中',
                'completed': '已完成',
                'failed': '失败'
            }

            scraping_status = {"status": "未开始", "clickable": False, "task_id": None}
            ai_status = {"status": "未开始", "clickable": False, "task_id": None}

            # 优先查找进行中或待处理的网页抓取任务（如果有 record_id，增加备注中带该ID 的限制）
            if record_id is not None:
                cursor.execute(
                    """
                    SELECT id, status, created_at, error_message
                    FROM task_queue
                    WHERE tracking_number = ? AND task_stage = 'scraping'
                      AND status IN ('pending', 'processing')
                      AND remarks LIKE ?
                    ORDER BY created_at DESC
                    LIMIT 1
                    """,
                    (tracking_number, f'%货运记录ID: {record_id}%')
                )
            else:
                cursor.execute(
                    """
                    SELECT id, status, created_at, error_message
                    FROM task_queue
                    WHERE tracking_number = ? AND task_stage = 'scraping'
                    AND status IN ('pending', 'processing')
                    ORDER BY created_at DESC
                    LIMIT 1
                    """,
                    (tracking_number,)
                )

            scraping_task = cursor.fetchone()

            # 如果没有进行中的抓取任务，查找最新的已完成抓取任务
            if not scraping_task:
                cursor.execute("""
                    SELECT id, status, created_at, error_message
                    FROM task_queue
                    WHERE tracking_number = ? AND task_stage = 'scraping'
                    ORDER BY created_at DESC
                    LIMIT 1
                """, (tracking_number,))
                scraping_task = cursor.fetchone()

            if scraping_task:
                scraping_id, scraping_db_status, scraping_created, scraping_error = scraping_task
                scraping_status = {
                    "task_id": scraping_id,
                    "clickable": True,
                    "error_message": scraping_error,
                    "status": status_mapping.get(scraping_db_status, scraping_db_status)
                }

                # 优先查找进行中或待处理的AI分析任务（若有 record_id，要求备注包含该ID）
                if record_id is not None:
                    cursor.execute(
                        """
                        SELECT id, status, created_at, error_message
                        FROM task_queue
                        WHERE tracking_number = ?
                          AND task_stage = 'ai_analysis'
                          AND status IN ('pending', 'processing')
                          AND (parent_task_id = ? OR created_at >= ?)
                          AND remarks LIKE ?
                        ORDER BY created_at DESC
                        LIMIT 1
                        """,
                        (tracking_number, scraping_id, scraping_created, f'%货运记录ID: {record_id}%')
                    )
                else:
                    cursor.execute(
                        """
                        SELECT id, status, created_at, error_message
                        FROM task_queue
                        WHERE tracking_number = ?
                        AND task_stage = 'ai_analysis'
                        AND status IN ('pending', 'processing')
                        AND (parent_task_id = ? OR created_at >= ?)
                        ORDER BY created_at DESC
                        LIMIT 1
                        """,
                        (tracking_number, scraping_id, scraping_created)
                    )

                ai_task = cursor.fetchone()

                # 如果没有进行中的AI任务，查找最新的已完成AI任务（若有 record_id，要求备注包含该ID）
                if not ai_task:
                    if record_id is not None:
                        cursor.execute(
                            """
                            SELECT id, status, created_at, error_message
                            FROM task_queue
                            WHERE tracking_number = ?
                              AND task_stage = 'ai_analysis'
                              AND (parent_task_id = ? OR created_at >= ?)
                              AND remarks LIKE ?
                            ORDER BY created_at DESC
                            LIMIT 1
                            """,
                            (tracking_number, scraping_id, scraping_created, f'%货运记录ID: {record_id}%')
                        )
                    else:
                        cursor.execute(
                            """
                            SELECT id, status, created_at, error_message
                            FROM task_queue
                            WHERE tracking_number = ?
                            AND task_stage = 'ai_analysis'
                            AND (parent_task_id = ? OR created_at >= ?)
                            ORDER BY created_at DESC
                            LIMIT 1
                            """,
                            (tracking_number, scraping_id, scraping_created)
                        )
                    ai_task = cursor.fetchone()

                if ai_task:
                    # 找到了AI分析任务
                    ai_id, ai_db_status, ai_created, ai_error = ai_task
                    ai_status = {
                        "task_id": ai_id,
                        "clickable": True,
                        "error_message": ai_error,
                        "status": status_mapping.get(ai_db_status, ai_db_status)
                    }
                else:
                    # 没有AI分析任务，根据抓取状态推断
                    if scraping_db_status == 'completed':
                        ai_status["status"] = "准备中"
                    elif scraping_db_status in ['pending', 'processing']:
                        ai_status["status"] = "等待中"
                    else:
                        ai_status["status"] = "未开始"

            conn.close()
            return scraping_status, ai_status

        except Exception as e:
            print(f"[ERROR] 获取任务状态失败: {e}")
            import traceback
            traceback.print_exc()
            return {"status": "错误", "clickable": False}, {"status": "错误", "clickable": False}

    def create_status_item(self, status_info: dict):
        """创建状态显示项"""
        item = QTableWidgetItem(status_info["status"])

        # 根据状态设置颜色
        if status_info["status"] == "已完成":
            item.setBackground(QColor(230, 255, 230))  # 淡绿色背景
            item.setForeground(QColor(34, 139, 34))     # 深绿色文字
        elif status_info["status"] == "进行中":
            item.setBackground(QColor(255, 248, 220))   # 淡黄色背景
            item.setForeground(QColor(255, 140, 0))     # 橙色文字
        elif status_info["status"] == "失败":
            item.setBackground(QColor(255, 230, 230))   # 淡红色背景
            item.setForeground(QColor(220, 20, 60))     # 深红色文字
        elif status_info["status"] == "待处理":
            item.setBackground(QColor(240, 240, 240))   # 灰色背景
            item.setForeground(QColor(105, 105, 105))   # 深灰色文字
        elif status_info["status"] == "等待中":
            item.setBackground(QColor(245, 245, 220))   # 浅米色背景
            item.setForeground(QColor(139, 69, 19))     # 棕色文字
        elif status_info["status"] == "准备中":
            item.setBackground(QColor(230, 230, 250))   # 淡紫色背景
            item.setForeground(QColor(72, 61, 139))     # 深蓝紫色文字
        elif status_info["status"] in ["未开始", "无任务"]:
            item.setBackground(QColor(250, 250, 250))   # 极浅灰色背景
            item.setForeground(QColor(169, 169, 169))   # 浅灰色文字

        # 存储任务ID和错误信息，用于点击查看详情
        if status_info.get("task_id"):
            item.setData(Qt.UserRole, {
                "task_id": status_info["task_id"],
                "error_message": status_info.get("error_message", ""),
                "clickable": status_info.get("clickable", False)
            })

        return item

    def handle_status_click(self, row: int, column: int):
        """处理状态列点击事件"""
        # 只处理网页抓取(第4列)和AI分析(第5列)点击
        if column not in [4, 5]:
            return

        item = self.shipments_table.item(row, column)
        if not item:
            return

        # AI分析列：无论状态如何都尝试打开弹窗
        if column == 5:
            task_data = item.data(Qt.UserRole) or {}
            self.open_ai_analysis_popup(row, task_data)
            return

        # 网页抓取列：保持原有逻辑（仅当可点击时弹出）
        task_data = item.data(Qt.UserRole)
        if not task_data or not task_data.get("clickable"):
            return

        task_id = task_data.get("task_id")
        error_message = task_data.get("error_message", "")
        stage_name = "网页抓取"

        # 显示任务详情对话框
        self.show_task_detail_dialog(task_id, stage_name, error_message)

    def open_ai_analysis_popup(self, row: int, task_data: dict):
        """AI分析列点击：始终弹窗
        优先顺序：
        1) 若单元格绑定了任务ID，直接打开详情
        2) 否则查找该号码最近的AI分析任务并打开
        3) 若仍无，询问是否基于最新抓取结果创建AI分析任务
        """
        try:
            # 从表格获取跟踪号与记录ID
            number_item = self.shipments_table.item(row, 1)
            tracking_number = number_item.text() if number_item else ""
            id_item = self.shipments_table.item(row, 0)
            record_id = id_item.data(Qt.UserRole) if id_item else None

            # 1) 有直接绑定的任务则打开
            if task_data.get("task_id"):
                self.show_task_detail_dialog(task_data["task_id"], "AI分析", task_data.get("error_message", ""))
                return

            # 2) 查找该号码最近的 AI 分析任务
            import sqlite3
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()

            ai_task = None
            if tracking_number:
                try:
                    if record_id is not None:
                        cursor.execute(
                            """
                            SELECT id FROM task_queue
                            WHERE tracking_number = ? AND task_stage = 'ai_analysis'
                              AND remarks LIKE ?
                            ORDER BY created_at DESC
                            LIMIT 1
                            """,
                            (tracking_number, f'%货运记录ID: {record_id}%')
                        )
                        ai_task = cursor.fetchone()
                    if not ai_task:
                        cursor.execute(
                            """
                            SELECT id FROM task_queue
                            WHERE tracking_number = ? AND task_stage = 'ai_analysis'
                            ORDER BY created_at DESC
                            LIMIT 1
                            """,
                            (tracking_number,)
                        )
                        ai_task = cursor.fetchone()
                finally:
                    conn.close()

            if ai_task and ai_task[0]:
                self.show_task_detail_dialog(ai_task[0], "AI分析", "")
                return

            # 3) 询问是否创建新的 AI 分析任务
            reply = QMessageBox.question(
                self,
                "AI分析",
                "当前无AI分析任务。是否基于最新抓取结果创建一个AI分析任务？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return

            # 查找可用的抓取结果（完成且无错误，优先匹配记录ID）
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()
            try:
                scraping_task = None
                if tracking_number:
                    if record_id is not None:
                        cursor.execute(
                            """
                            SELECT id, raw_data_path FROM task_queue
                            WHERE tracking_number = ? AND task_stage = 'scraping'
                              AND status = 'completed' AND (error_message IS NULL OR error_message = '')
                              AND remarks LIKE ?
                            ORDER BY created_at DESC
                            LIMIT 1
                            """,
                            (tracking_number, f'%货运记录ID: {record_id}%')
                        )
                        scraping_task = cursor.fetchone()
                    if not scraping_task:
                        cursor.execute(
                            """
                            SELECT id, raw_data_path FROM task_queue
                            WHERE tracking_number = ? AND task_stage = 'scraping'
                              AND status = 'completed' AND (error_message IS NULL OR error_message = '')
                            ORDER BY created_at DESC
                            LIMIT 1
                            """,
                            (tracking_number,)
                        )
                        scraping_task = cursor.fetchone()
            finally:
                conn.close()

            if not scraping_task or not scraping_task[1]:
                QMessageBox.information(self, "无法创建", "未找到可用的抓取结果（或数据路径为空）。请先完成网页抓取。")
                return

            parent_task_id, raw_data_path = scraping_task[0], scraping_task[1]
            # 备注带上记录ID，便于后续精准匹配
            remarks = f"货运记录ID: {record_id}" if record_id else None

            ai_task_id = self.task_manager.create_ai_analysis_task(
                parent_task_id=parent_task_id,
                raw_data_path=raw_data_path,
                priority=0,
                remarks=remarks
            )

            if ai_task_id:
                QMessageBox.information(self, "已创建", f"已创建AI分析任务：{ai_task_id}")
                # 打开刚创建的任务详情
                self.show_task_detail_dialog(ai_task_id, "AI分析", "")
                # 刷新表格显示
                self.load_shipments()
            else:
                QMessageBox.warning(self, "创建失败", "创建AI分析任务失败。")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开AI分析窗口失败: {str(e)}")

    def show_task_detail_dialog(self, task_id: str, stage_name: str, error_message: str):
        """显示任务详情对话框"""
        try:
            import sqlite3
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, task_name, status, created_at, started_at, completed_at,
                       error_message, retry_count, max_retries, result_summary
                FROM task_queue WHERE id = ?
            """, (task_id,))

            task_info = cursor.fetchone()
            conn.close()

            if not task_info:
                QMessageBox.warning(self, "任务不存在", f"任务 {task_id} 不存在")
                return

            dialog = TaskDetailDialog(task_info, stage_name, self)
            result = dialog.exec()

            # 如果用户选择重试，执行重试操作
            if result == QDialog.Accepted and hasattr(dialog, 'should_retry') and dialog.should_retry:
                self.retry_task(task_id, stage_name)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"查看任务详情失败: {str(e)}")

    def retry_task(self, task_id: str, stage_name: str):
        """重试任务"""
        try:
            # 重置任务状态为pending
            import sqlite3
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE task_queue
                SET status = 'pending', error_message = NULL, started_at = NULL, completed_at = NULL
                WHERE id = ?
            """, (task_id,))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "重试成功", f"{stage_name}任务已重置为待处理状态，系统将自动重新执行")

            # 刷新货运记录列表
            self.load_shipments()

        except Exception as e:
            QMessageBox.critical(self, "重试失败", f"重试{stage_name}任务失败: {str(e)}")

    def update_carrier_filter(self, shipments: List[Dict]):
        """更新船公司筛选器"""
        carriers = set(shipment.get('carrier_company', '') for shipment in shipments if shipment.get('carrier_company'))

        current_text = self.carrier_combo.currentText()
        self.carrier_combo.clear()
        self.carrier_combo.addItem("全部船公司")
        self.carrier_combo.addItems(sorted(carriers))

        # 恢复之前的选择
        index = self.carrier_combo.findText(current_text)
        if index >= 0:
            self.carrier_combo.setCurrentIndex(index)

    def on_search_changed(self):
        """搜索条件变化处理"""
        keyword = self.search_input.text().strip()
        status = self.status_combo.currentText()
        carrier = self.carrier_combo.currentText()

        # 构建搜索条件
        filters = {}
        if keyword:
            filters['keyword'] = keyword
        if status != "全部状态":
            filters['status'] = status
        if carrier != "全部船公司":
            filters['carrier'] = carrier

        try:
            # 构建搜索参数
            search_params = {}
            if 'keyword' in filters:
                # 关键词可能是提单号或箱号
                search_params['bill_of_lading'] = filters['keyword']
                search_params['container_number'] = filters['keyword']
            if 'carrier' in filters:
                search_params['carrier_company'] = filters['carrier']
            if 'status' in filters:
                search_params['status'] = filters['status']

            shipments = self.shipment_manager.search_shipment_records(**search_params)
            self.update_shipments_table(shipments)
        except Exception as e:
            QMessageBox.warning(self, "搜索失败", f"搜索失败: {str(e)}")

    def show_shipment_details(self):
        """显示货运记录详情"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0:
            # 从UserRole中获取真实的ID
            shipment_id = self.shipments_table.item(current_row, 0).data(Qt.UserRole)
            dialog = ShipmentDetailDialog(shipment_id, self.shipment_manager, self)
            dialog.exec()

    def delete_selected_shipment(self):
        """删除选中的货运记录"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0:
            # 从UserRole中获取真实的ID
            shipment_id = self.shipments_table.item(current_row, 0).data(Qt.UserRole)
            bill_number = self.shipments_table.item(current_row, 1).text()

            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除货运记录 '{bill_number}' 吗？\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    success = self.shipment_manager.delete_shipment_record(shipment_id)
                    if success:
                        self.load_shipments()
                        QMessageBox.information(self, "删除成功", f"货运记录 '{bill_number}' 已删除")
                    else:
                        QMessageBox.warning(self, "删除失败", "删除操作失败，请重试")
                except Exception as e:
                    QMessageBox.critical(self, "删除错误", f"删除过程中发生错误：{str(e)}")
        else:
            QMessageBox.information(self, "提示", "请先选择要删除的货运记录")

    def open_carrier_management(self):
        """打开承运人管理界面"""
        try:
            import subprocess
            import sys
            # 启动承运人管理应用
            subprocess.Popen([sys.executable, 'carrier_management_ui.py'])
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"无法启动承运人管理界面: {str(e)}")

    def open_ai_model_management(self):
        """打开AI模型管理界面"""
        try:
            import subprocess
            import sys
            # 启动AI模型管理应用
            subprocess.Popen([sys.executable, 'ai_model_management_ui.py'])
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"无法启动AI模型管理界面: {str(e)}")

    def open_task_status_viewer(self):
        """显示任务状态查看器"""
        try:
            dialog = TaskStatusDialog(self.task_manager, self.task_processor, self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开任务状态查看器: {str(e)}")

    def open_connection_pool_stats(self):
        """显示连接池状态"""
        try:
            dialog = PoolStatusDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开连接池状态窗口: {str(e)}")

    def open_prompt_history(self):
        """打开AI提示词历史版本管理"""
        try:
            dialog = PromptHistoryDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开提示词历史管理: {str(e)}")

    def open_ai_log_viewer(self):
        """打开AI调用日志查看器"""
        try:
            import subprocess
            import sys
            # 启动AI日志查看器应用
            subprocess.Popen([sys.executable, 'ai_log_viewer_ui.py'])
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"无法启动AI日志查看器: {str(e)}")

    def open_database_management(self):
        """打开数据库管理界面"""
        try:
            import subprocess
            import sys
            # 启动数据库管理应用
            subprocess.Popen([sys.executable, 'database_management_ui.py'])
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"无法启动数据库管理界面: {str(e)}")

    def open_prompt_editor(self):
        """打开AI提示词配置编辑器"""
        try:
            dialog = PromptEditorDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开提示词编辑器: {str(e)}")

    def reload_ai_prompts(self):
        """重新加载AI提示词配置"""
        try:
            reload_prompt_config()
            QMessageBox.information(self, "提示", "AI提示词配置已重新加载")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"重载AI提示词失败: {str(e)}")

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h2>Container Helper</h2>
        <p><strong>版本:</strong> 1.0.0</p>
        <p><strong>描述:</strong> AI驱动的智能集装箱货运跟踪系统</p>

        <h3>主要功能</h3>
        <ul>
        <li>• 自动识别船公司信息</li>
        <li>• 智能货运信息查询</li>
        <li>• 自动化网页抓取和数据分析</li>
        <li>• 现代化用户界面</li>
        <li>• 实时任务处理和状态监控</li>
        </ul>

        <h3>技术栈</h3>
        <ul>
        <li>• PySide6 - 现代化桌面界面</li>
        <li>• Playwright - 网页自动化</li>
        <li>• AI集成 - 智能数据分析</li>
        <li>• SQLite - 本地数据存储</li>
        </ul>

        <p style="margin-top: 20px;">
        <strong>开发团队:</strong> Container Helper Team<br>
        <strong>技术支持:</strong> <a href="https://github.com/container-helper">GitHub</a>
        </p>
        """

        # 创建关于对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("关于 Container Helper")
        msg_box.setText(about_text)
        msg_box.setTextFormat(Qt.RichText)
        msg_box.setIcon(QMessageBox.Information)

        # 添加关闭按钮
        msg_box.setStandardButtons(QMessageBox.Ok)

        # 设置对话框大小
        msg_box.resize(500, 400)

        msg_box.exec()

    def toggle_task_processor(self):
        """启动/停止任务处理器（停止前检查是否有进行中的任务）"""
        try:
            if not self.task_processor or not self.task_processor.running:
                if not self.task_processor:
                    print("[ERROR] 任务处理器未初始化")
                    return

                self.task_processor.start()
                self.update_task_processor_action_text()

                # 根据任务处理器类型显示不同的启动消息
                if self.task_processor and hasattr(self.task_processor, '__class__') and 'ScheduledTaskProcessor' in str(self.task_processor.__class__):
                    QMessageBox.information(
                        self,
                        "定时任务处理器",
                        "定时任务处理器已启动\n\n"
                        "运行模式说明:\n"
                        "• 网页抓取：每10秒检查待处理任务\n"
                        "• AI分析：每5秒检查待处理任务\n"
                        "• 状态维护：每30秒执行一次\n"
                        "• 支持高并发处理"
                    )
                else:
                    QMessageBox.information(self, "任务处理器", "任务处理器已启动")
            else:
                # 检查是否有正在处理的任务
                try:
                    status = self.task_processor.get_status()
                    dist = status.get('status_distribution') or {}
                    processing_count = dist.get('processing', 0)

                    if processing_count and int(processing_count) > 0:
                        QMessageBox.warning(self, "无法停止", f"当前有 {processing_count} 个进行中的任务，请稍后再试。")
                        return
                except:
                    pass

                # 无进行中任务，允许停止
                if self.task_processor:
                    self.task_processor.stop()
                self.update_task_processor_action_text()

                if self.task_processor and hasattr(self.task_processor, '__class__') and 'ScheduledTaskProcessor' in str(self.task_processor.__class__):
                    QMessageBox.information(self, "定时任务处理器", "定时任务处理器已停止")
                else:
                    QMessageBox.information(self, "任务处理器", "任务处理器已停止")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作任务处理器时发生错误: {str(e)}")

    def update_task_processor_action_text(self):
        """根据运行状态更新菜单项文案"""
        if hasattr(self, 'task_processor_action'):
            if self.task_processor and self.task_processor.running:
                if self.task_processor and hasattr(self.task_processor, '__class__') and 'ScheduledTaskProcessor' in str(self.task_processor.__class__):
                    self.task_processor_action.setText("停止定时任务处理器")
                    self.task_processor_action.setStatusTip("停止定时任务处理器（网页抓取+AI分析）")
                else:
                    self.task_processor_action.setText("停止任务处理器")
                    self.task_processor_action.setStatusTip("停止任务处理器")
            else:
                if self.task_processor and hasattr(self.task_processor, '__class__') and 'ScheduledTaskProcessor' in str(self.task_processor.__class__):
                    self.task_processor_action.setText("启动定时任务处理器")
                    self.task_processor_action.setStatusTip("启动定时任务处理器（网页抓取+AI分析）")
                else:
                    self.task_processor_action.setText("启动任务处理器")
                    self.task_processor_action.setStatusTip("启动任务处理器")

    def view_screenshot(self, screenshot_path: str):
        """查看佐证截图"""
        if not screenshot_path or not os.path.exists(screenshot_path):
            QMessageBox.warning(self, "文件不存在", "截图文件不存在")
            return

        try:
            dialog = ImageViewerDialog(screenshot_path, self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "打开失败", f"无法打开截图文件: {str(e)}")

    def show_pool_status(self):
        """显示连接池状态"""
        try:
            dialog = PoolStatusDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开连接池状态: {str(e)}")

    def handle_task_completion(self, task_id: str, task_stage: str, result_data: Dict = None):
        """
        处理任务完成事件 - 适配新的定时任务处理器

        Args:
            task_id: 任务ID
            task_stage: 任务阶段 ('scraping' 或 'ai_analysis')
            result_data: 任务结果数据
        """
        try:
            print(f"[CALLBACK] 收到任务完成通知: {task_id}, 阶段: {task_stage}")

            if task_stage == 'scraping':
                # 网页抓取阶段完成
                print(f"[WEB] 网页抓取任务 {task_id} 已完成")
                # 显示网页抓取完成提示
                QTimer.singleShot(0, lambda: self.show_stage_completion_notice("网页抓取"))
                # 刷新任务状态显示
                QTimer.singleShot(500, self.refresh_task_status)  # 延迟0.5秒刷新任务状态

            elif task_stage == 'ai_analysis':
                # AI分析阶段完成，更新货运记录
                print(f"[APP] 收到AI分析完成回调: {task_id}")
                print(f"[APP] result_data类型: {type(result_data)}, keys: {list(result_data.keys()) if result_data else 'None'}")
                if result_data:
                    print(f"[APP] ETA: {result_data.get('estimated_arrival_time', 'None')}")
                    print(f"[APP] 日期数据: {len(result_data.get('dates_data', []))} 条")

                success = self.shipment_manager.handle_task_completion(task_id, result_data)
                if success:
                    print(f"[AI] AI分析任务 {task_id} 已完成，货运记录状态已更新")
                    # 显示AI分析完成提示
                    QTimer.singleShot(0, lambda: self.show_stage_completion_notice("AI分析"))
                    # 立即刷新货运记录列表以显示更新的状态
                    QTimer.singleShot(0, self.load_shipments)  # 立即刷新
                    # 确保状态监控定时器处于活跃状态
                    QTimer.singleShot(100, self.ensure_status_timer_running)
                    print(f"[REFRESH] 已触发货运记录列表刷新")
                else:
                    print(f"[ERROR] AI分析任务 {task_id} 完成，但货运记录更新失败")

            elif task_stage == 'status_update':
                # 处理状态更新请求
                self._handle_status_update(result_data)

            else:
                # 兼容其他模式
                success = self.shipment_manager.handle_task_completion(task_id, result_data)
                if success:
                    print(f"[SUCCESS] 任务 {task_id} 已完成，货运记录状态已更新")
                    # 刷新货运记录列表以显示更新的状态
                    QTimer.singleShot(1000, self.load_shipments)  # 延迟1秒刷新
                    print(f"[REFRESH] 已触发货运记录列表刷新")
                else:
                    print(f"[WARNING] 任务 {task_id} 完成处理失败，请检查日志")

            # 共同刷新任务状态显示
            QTimer.singleShot(500, self.refresh_task_status)  # 延迟0.5秒刷新任务状态

        except Exception as e:
            print(f"[ERROR] 任务完成回调处理异常: {e}")
            import traceback
            traceback.print_exc()

    def _handle_status_update(self, result_data: Dict):
        """处理货运记录状态更新请求"""
        try:
            print(f"[APP_DEBUG] 收到状态更新请求，数据: {result_data}")
            if result_data and 'shipment_status_update' in result_data:
                update_info = result_data['shipment_status_update']
                record_id = update_info.get('record_id')
                new_status = update_info.get('new_status')

                print(f"[APP_DEBUG] 准备更新记录ID: {record_id}, 新状态: {new_status}")

                if record_id and new_status:
                    print(f"[STATUS] 更新货运记录 {record_id} 状态: {new_status}")
                    success = self.shipment_manager.update_shipment_status(record_id, new_status, 'task_status_sync')
                    if success:
                        print(f"[SUCCESS] 货运记录状态已更新")
                        # 立即刷新界面
                        QTimer.singleShot(0, self.load_shipments)
                        print(f"[DEBUG] 界面刷新已触发")
                    else:
                        print(f"[ERROR] 货运记录状态更新失败")
                else:
                    print(f"[ERROR] 缺少必要参数 - record_id: {record_id}, new_status: {new_status}")
            else:
                print(f"[ERROR] 状态更新数据格式不正确")
        except Exception as e:
            print(f"[ERROR] 处理状态更新失败: {e}")
            import traceback
            traceback.print_exc()

    def show_stage_completion_notice(self, stage_name: str):
        """
        显示阶段完成提示

        Args:
            stage_name: 阶段名称，如 '网页抓取' 或 'AI分析'
        """
        if stage_name == "AI分析":
            # AI分析完成时显示刷新提示
            self.task_completion_notice.setText("AI分析完成，请刷新查看")
            self.task_completion_notice.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)
        else:
            # 网页抓取完成时显示进度提示
            self.task_completion_notice.setText(f"{stage_name}完成，等待AI分析")
            self.task_completion_notice.setStyleSheet("""
                QLabel {
                    background-color: #fff3cd;
                    color: #856404;
                    border: 1px solid #ffeaa7;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
            """)

        self.task_completion_notice.show()

        # 设置自动隐藏定时器
        QTimer.singleShot(5000, self.hide_task_completion_notice)

    def hide_task_completion_notice(self):
        """隐藏任务完成提示"""
        if hasattr(self, 'task_completion_notice'):
            self.task_completion_notice.hide()

    def refresh_list(self):
        """
        刷新货运记录列表
        """
        try:
            # 隐藏任务完成提示
            self.hide_task_completion_notice()
            self.load_shipments()
            print("[SUCCESS] 货运记录列表已刷新")
        except Exception as e:
            print(f"[ERROR] 刷新列表失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新列表失败: {str(e)}")

    def refresh_shipments_list(self):
        """
        刷新货运记录列表 - 别名方法
        """
        self.load_shipments()

    def setup_status_refresh_timer(self):
        """设置状态刷新定时器"""
        # 创建定时器用于自动刷新任务状态
        self.status_refresh_timer = QTimer()
        self.status_refresh_timer.timeout.connect(self.refresh_task_status)

        # 初始不启动，当有任务进行中时才启动
        self.status_refresh_timer.setSingleShot(False)
        self.status_refresh_interval = 3000  # 3秒刷新一次

        print("[INFO] 状态刷新定时器已初始化")

    def refresh_task_status(self):
        """自动刷新任务状态"""
        try:
            # 只更新表格中的状态列，避免完全重新加载
            current_row_count = self.shipments_table.rowCount()

            # 为减少开销，打开一次数据库连接复用
            import sqlite3
            shipment_conn = None
            try:
                shipment_conn = sqlite3.connect('db/shipment_records.db')
                shipment_cursor = shipment_conn.cursor()
            except Exception:
                shipment_conn = None
                shipment_cursor = None

            for row in range(current_row_count):
                # 获取跟踪号与当前行对应的真实记录ID（首列 UserRole 中存了 id）
                tracking_item = self.shipments_table.item(row, 1)  # 提单号/箱号列
                id_item = self.shipments_table.item(row, 0)        # 行号列（保存了 UserRole=id）
                if not tracking_item or not id_item:
                    continue

                tracking_number = tracking_item.text() if tracking_item else ''
                record_id = id_item.data(Qt.UserRole) if id_item else None
                if not tracking_number:
                    continue

                # 使用 record_id 精确匹配，避免同提单号的历史记录被“串状态”
                scraping_status, ai_status = self.get_task_stages_status(tracking_number, record_id)

                # 更新网页抓取状态（第4列）
                scraping_item = self.create_status_item(scraping_status)
                self.shipments_table.setItem(row, 4, scraping_item)

                # 更新AI分析状态（第5列）
                ai_item = self.create_status_item(ai_status)
                self.shipments_table.setItem(row, 5, ai_item)

                # 同步刷新主记录状态（第3列），解决“数据库已更新但UI仍显示排队中”的问题
                if shipment_cursor and record_id is not None:
                    try:
                        shipment_cursor.execute('SELECT status FROM shipment_records WHERE id = ?', (record_id,))
                        row_status = shipment_cursor.fetchone()
                        if row_status and row_status[0]:
                            self.shipments_table.setItem(row, 3, QTableWidgetItem(row_status[0]))
                    except Exception:
                        pass

            # 检查是否有进行中的任务，决定是否继续定时器
            self.check_and_manage_timer()

        except Exception as e:
            print(f"[ERROR] 自动刷新任务状态失败: {e}")
        finally:
            try:
                if shipment_conn:
                    shipment_conn.close()
            except Exception:
                pass

    def check_and_manage_timer(self):
        """检查任务状态并管理定时器"""
        try:
            # 检查是否有进行中的任务或最近有排队中的任务
            has_active_tasks = False
            has_queued_tasks = False
            current_row_count = self.shipments_table.rowCount()

            for row in range(current_row_count):
                # 检查网页抓取状态
                scraping_item = self.shipments_table.item(row, 4)
                ai_item = self.shipments_table.item(row, 5)
                # 检查整体状态
                status_item = self.shipments_table.item(row, 3)

                if (scraping_item and scraping_item.text() in ["待处理", "进行中"]) or \
                   (ai_item and ai_item.text() in ["待处理", "进行中"]):
                    has_active_tasks = True
                    break

                # 检查是否有排队中的记录
                if status_item and status_item.text() == "排队中":
                    has_queued_tasks = True

            # 扩展定时器管理逻辑：有活跃任务或排队任务时保持定时器运行
            should_run_timer = has_active_tasks or has_queued_tasks

            if should_run_timer and not self.status_refresh_timer.isActive():
                self.status_refresh_timer.start(self.status_refresh_interval)
                print(f"[START] 启动状态自动刷新定时器 (活跃任务: {has_active_tasks}, 排队任务: {has_queued_tasks})")
            elif not should_run_timer and self.status_refresh_timer.isActive():
                self.status_refresh_timer.stop()
                print("[STOP] 停止状态自动刷新定时器（无活跃或排队任务）")

            # 在每次状态刷新后，针对最近完成的抓取/AI任务做"精准刷新"
            self.refresh_completed_rows_if_needed()

        except Exception as e:
            print(f"[ERROR] 管理定时器状态失败: {e}")

    def start_status_monitoring(self):
        """手动启动状态监控"""
        if not self.status_refresh_timer.isActive():
            self.status_refresh_timer.start(self.status_refresh_interval)
            print("[START] 手动启动状态监控定时器")

    def ensure_status_timer_running(self):
        """确保状态刷新定时器处于运行状态"""
        if not self.status_refresh_timer.isActive():
            self.status_refresh_timer.start(self.status_refresh_interval)
            print("[ENSURE] 确保状态刷新定时器运行")

    def refresh_completed_rows_if_needed(self):
        """精准刷新：对最近完成的抓取/AI分析（并集）的记录刷新 ETA/节点两列。
        - 每次调用只查询过去60秒内完成的任务；
        - 仅刷新当前表格中匹配这些提单号/箱号的行，避免整表 reload；
        """
        try:
            import sqlite3
            from datetime import datetime, timedelta

            # 仅每2秒最多执行一次，避免过于频繁
            now = datetime.now()
            if getattr(self, '_last_completed_scan', None) and (now - self._last_completed_scan).total_seconds() < 2:
                return
            self._last_completed_scan = now

            # 查找最近完成的任务（抓取或AI分析），时间窗口 60 秒
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT tracking_number, MAX(completed_at)
                FROM task_queue
                WHERE status = 'completed'
                  AND completed_at IS NOT NULL
                  AND datetime(completed_at) >= datetime('now', '-60 seconds')
                GROUP BY tracking_number
                """
            )
            rows = cursor.fetchall()
            conn.close()

            if not rows:
                return

            # 建立一个快速映射，查 shipment 的最新 ETA 和 节点数量
            completed_numbers = [r[0] for r in rows if r and r[0]]
            if not completed_numbers:
                return

            # 读取 shipment 数据
            sconn = sqlite3.connect('db/shipment_records.db')
            scur = sconn.cursor()

            # 为效率，只遍历当前表格行，匹配这些号码后局部刷新两列
            row_count = self.shipments_table.rowCount()
            for row in range(row_count):
                number_item = self.shipments_table.item(row, 1)
                id_item = self.shipments_table.item(row, 0)  # 首列的 UserRole 存放当前行对应的记录ID
                if not number_item or not id_item:
                    continue

                number = number_item.text()
                if number not in completed_numbers:
                    continue

                record_id = id_item.data(Qt.UserRole)
                if not record_id:
                    continue

                # 针对“同号多记录”的情况，按当前行自己的记录ID精确刷新，避免串改其它记录
                # 查询当前记录的 ETA
                scur.execute(
                    """
                    SELECT estimated_arrival_time
                    FROM shipment_records
                    WHERE id = ?
                    LIMIT 1
                    """,
                    (record_id,)
                )
                rec = scur.fetchone()
                if not rec:
                    continue
                eta = rec[0]

                # 格式化 ETA
                eta_display = eta or ''
                if eta_display and 'T' in eta_display:
                    eta_display = eta_display.split('T')[0]

                # 查询当前记录的节点数
                scur.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (record_id,))
                dates_count = scur.fetchone()[0]
                dates_display = f"{dates_count}条" if dates_count and dates_count > 0 else "无"

                # 写回表格（ETA在第6列，节点在第7列；索引从0开始即列6->索引6，列7->索引7）
                self.shipments_table.setItem(row, 6, QTableWidgetItem(eta_display))
                self.shipments_table.setItem(row, 7, QTableWidgetItem(dates_display))
        except Exception as e:
            print(f"[WARN] 精准刷新失败: {e}")

        if not self.status_refresh_timer.isActive():
            self.status_refresh_timer.start(self.status_refresh_interval)
            print("[START] 手动启动状态监控定时器")


class TaskDetailDialog(QDialog):
    """任务详情对话框"""

    def __init__(self, task_info: tuple, stage_name: str, parent=None):
        super().__init__(parent)
        self.task_info = task_info
        self.stage_name = stage_name
        self.should_retry = False

        self.setWindowTitle(f"{stage_name}任务详情")
        self.resize(600, 450)
        self.setModal(True)

        self.setup_ui()
        self.load_task_info()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 任务信息卡片
        info_card = ModernCard("任务信息")

        # 创建表单布局
        form_layout = QFormLayout()

        self.task_id_label = QLabel()
        self.task_name_label = QLabel()
        self.status_label = QLabel()
        self.created_at_label = QLabel()
        self.started_at_label = QLabel()
        self.completed_at_label = QLabel()
        self.retry_count_label = QLabel()

        form_layout.addRow("任务ID:", self.task_id_label)
        form_layout.addRow("任务名称:", self.task_name_label)
        form_layout.addRow("任务状态:", self.status_label)
        form_layout.addRow("创建时间:", self.created_at_label)
        form_layout.addRow("开始时间:", self.started_at_label)
        form_layout.addRow("完成时间:", self.completed_at_label)
        form_layout.addRow("重试次数:", self.retry_count_label)

        info_card.add_layout(form_layout)
        layout.addWidget(info_card)

        # 错误信息和结果摘要
        details_card = ModernCard("详细信息")
        details_layout = QVBoxLayout()

        # 错误信息
        error_label = QLabel("错误信息:")
        error_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        details_layout.addWidget(error_label)

        self.error_text = QTextEdit()
        self.error_text.setMaximumHeight(100)
        self.error_text.setReadOnly(True)
        details_layout.addWidget(self.error_text)

        # 结果摘要
        result_label = QLabel("结果摘要:")
        result_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        details_layout.addWidget(result_label)

        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(100)
        self.result_text.setReadOnly(True)
        details_layout.addWidget(self.result_text)

        details_card.add_layout(details_layout)
        layout.addWidget(details_card)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.retry_btn = ModernButton("重试任务", "primary")
        self.retry_btn.clicked.connect(self.retry_task)

        self.close_btn = ModernButton("关闭", "secondary")
        self.close_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.retry_btn)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def load_task_info(self):
        """加载任务信息"""
        if not self.task_info:
            return

        (task_id, task_name, status, created_at, started_at, completed_at,
         error_message, retry_count, max_retries, result_summary) = self.task_info

        self.task_id_label.setText(task_id or "")
        self.task_name_label.setText(task_name or "")

        # 设置状态显示（带颜色）
        status_text = status or ""
        if status == 'completed':
            status_text = "已完成"
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        elif status == 'processing':
            status_text = "进行中"
            self.status_label.setStyleSheet("color: #f39c12; font-weight: bold;")
        elif status == 'failed':
            status_text = "失败"
            self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        elif status == 'pending':
            status_text = "待处理"
            self.status_label.setStyleSheet("color: #95a5a6; font-weight: bold;")

        self.status_label.setText(status_text)

        self.created_at_label.setText(created_at or "")
        self.started_at_label.setText(started_at or "暂未开始")
        self.completed_at_label.setText(completed_at or "暂未完成")
        self.retry_count_label.setText(f"{retry_count or 0} / {max_retries or 3}")

        # 错误信息
        if error_message:
            self.error_text.setText(error_message)
        else:
            self.error_text.setText("无错误信息")

        # 结果摘要
        if result_summary:
            self.result_text.setText(result_summary)
        else:
            self.result_text.setText("暂无结果摘要")

        # 只有失败的任务才能重试
        can_retry = status in ['failed', 'cancelled']
        self.retry_btn.setEnabled(can_retry)
        if not can_retry:
            self.retry_btn.setText("无需重试")

    def retry_task(self):
        """标记需要重试并关闭对话框"""
        self.should_retry = True
        self.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("Container Helper")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Container Helper Team")

    # 设置全局字体
    font = QFont("Segoe UI", 9)
    app.setFont(font)

    # 创建并显示主窗口
    window = ContainerHelperApp()
    window.show()

    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
