#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东八区时间测试脚本
验证数据库操作中的时间记录是否正确使用东八区时间
"""

import sys
import os
from datetime import datetime, timezone, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from task_manager import TaskManager, create_bill_of_lading_task, get_beijing_time, get_beijing_time_str
from shipment_manager import ShipmentManager

def test_beijing_time_functions():
    """测试东八区时间函数"""
    print("=" * 60)
    print("测试东八区时间函数")
    print("=" * 60)
    
    beijing_time = get_beijing_time()
    beijing_time_str = get_beijing_time_str()
    system_time = datetime.now()
    
    print(f"[TIME] 系统时间: {system_time}")
    print(f"[TIME] 东八区时间: {beijing_time}")
    print(f"[TIME] 东八区时间字符串: {beijing_time_str}")
    print(f"[TIME] 时区信息: {beijing_time.tzinfo}")
    print(f"[TIME] UTC偏移: {beijing_time.utcoffset()}")
    
    # 验证是否是正确的东八区时间
    expected_offset = timedelta(hours=8)
    actual_offset = beijing_time.utcoffset()
    
    if actual_offset == expected_offset:
        print("[SUCCESS] 东八区时间设置正确！")
    else:
        print(f"[ERROR] 东八区时间设置错误，预期偏移: {expected_offset}，实际偏移: {actual_offset}")

def test_task_manager_beijing_time():
    """测试TaskManager的东八区时间记录"""
    print("\n" + "=" * 60)
    print("测试 TaskManager 东八区时间记录")
    print("=" * 60)
    
    try:
        manager = TaskManager()
        
        # 测试创建任务（记录创建时间）
        print("\n[TEST] 1. 测试创建任务（东八区时间）...")
        current_beijing_time = get_beijing_time_str()
        print(f"[TIME] 创建任务前的东八区时间: {current_beijing_time}")
        
        task_id = create_bill_of_lading_task(
            bl_number="BEIJING_TIME_TEST_001",
            creator_id="beijing_test_user",
            creator_name="东八区测试用户",
            carrier="MSC",
            priority=1,
            remarks="东八区时间测试任务"
        )
        print(f"[TEST] 任务创建成功，ID: {task_id}")
        
        # 获取任务详情，检查创建时间
        task = manager.get_task_by_id(task_id)
        if task:
            print(f"[TIME] 数据库中的创建时间: {task.get('created_at', 'N/A')}")
            print(f"[TIME] 当前东八区时间: {get_beijing_time_str()}")
        
        # 测试更新任务状态（记录开始时间）
        print("\n[TEST] 2. 测试更新任务状态为processing...")
        before_update_time = get_beijing_time_str()
        print(f"[TIME] 更新前的东八区时间: {before_update_time}")
        
        success = manager.update_task_status(task_id, "processing")
        if success:
            task = manager.get_task_by_id(task_id)
            print(f"[TIME] 数据库中的开始时间: {task.get('started_at', 'N/A')}")
        
        # 测试完成任务（记录完成时间）
        print("\n[TEST] 3. 测试完成任务...")
        before_complete_time = get_beijing_time_str()
        print(f"[TIME] 完成前的东八区时间: {before_complete_time}")
        
        success = manager.update_task_status(
            task_id, 
            "completed", 
            result_summary="东八区时间测试完成"
        )
        if success:
            task = manager.get_task_by_id(task_id)
            print(f"[TIME] 数据库中的完成时间: {task.get('completed_at', 'N/A')}")
        
        print(f"[SUCCESS] TaskManager 东八区时间测试完成")
        
    except Exception as e:
        print(f"[ERROR] TaskManager时间测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_shipment_manager_beijing_time():
    """测试ShipmentManager的东八区时间记录"""
    print("\n" + "=" * 60)
    print("测试 ShipmentManager 东八区时间记录")
    print("=" * 60)
    
    try:
        manager = ShipmentManager()
        
        # 测试创建货运记录
        print("\n[TEST] 1. 测试创建货运记录（东八区时间）...")
        current_beijing_time = get_beijing_time_str()
        print(f"[TIME] 创建记录前的东八区时间: {current_beijing_time}")
        
        record_id = manager.create_shipment_record(
            bill_of_lading="BEIJING_TIME_BL_001",
            container_number="BEIJING_TIME_CONT_001",
            carrier_company="MSC",
            estimated_arrival_time=get_beijing_time(),  # 使用东八区时间
            remarks="东八区时间测试货运记录",
            created_by="beijing_test_user"
        )
        print(f"[TEST] 货运记录创建成功，ID: {record_id}")
        print(f"[TIME] 创建记录后的东八区时间: {get_beijing_time_str()}")
        
        # 测试添加时间节点
        print("\n[TEST] 2. 测试添加时间节点（东八区时间）...")
        before_add_dates_time = get_beijing_time_str()
        print(f"[TIME] 添加节点前的东八区时间: {before_add_dates_time}")
        
        test_dates = [
            {
                'date': '2025-08-06 10:00:00',
                'location': '上海港',
                'event': '装船',
                'status': '已完成',
                'event_type': 'departure',
                'vessel_info': {'name': 'MSC OSCAR', 'voyage': 'BEIJING001'}
            },
            {
                'date': '2025-08-16 14:30:00',
                'location': '洛杉矶港',
                'event': '到港',
                'status': '预计',
                'event_type': 'arrival',
                'vessel_info': {'name': 'MSC OSCAR', 'voyage': 'BEIJING001'}
            }
        ]
        
        success = manager.add_shipment_dates(record_id, test_dates)
        print(f"[TEST] 时间节点添加结果: {success}")
        print(f"[TIME] 添加节点后的东八区时间: {get_beijing_time_str()}")
        
        print(f"[SUCCESS] ShipmentManager 东八区时间测试完成")
        
    except Exception as e:
        print(f"[ERROR] ShipmentManager时间测试失败: {e}")
        import traceback
        traceback.print_exc()

def verify_database_times():
    """验证数据库中的时间记录"""
    print("\n" + "=" * 60)
    print("验证数据库中的时间记录")
    print("=" * 60)
    
    try:
        import sqlite3
        
        # 检查任务队列表
        print("\n[VERIFY] 检查任务队列表的时间记录...")
        conn = sqlite3.connect('db/task_queue.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, task_name, created_at, started_at, completed_at 
            FROM task_queue 
            WHERE tracking_number LIKE 'BEIJING_TIME_TEST%' 
            ORDER BY created_at DESC LIMIT 5
        """)
        
        tasks = cursor.fetchall()
        for task in tasks:
            print(f"[TIME] 任务 {task['task_name']}:")
            print(f"  - 创建时间: {task['created_at']}")
            print(f"  - 开始时间: {task['started_at']}")
            print(f"  - 完成时间: {task['completed_at']}")
        
        conn.close()
        
        # 检查货运记录表
        print("\n[VERIFY] 检查货运记录表的时间记录...")
        conn = sqlite3.connect('db/shipment_records.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, bill_of_lading, created_at, updated_at, estimated_arrival_time
            FROM shipment_records 
            WHERE bill_of_lading LIKE 'BEIJING_TIME_BL%' 
            ORDER BY created_at DESC LIMIT 5
        """)
        
        records = cursor.fetchall()
        for record in records:
            print(f"[TIME] 货运记录 {record['bill_of_lading']}:")
            print(f"  - 创建时间: {record['created_at']}")
            print(f"  - 更新时间: {record['updated_at']}")
            print(f"  - 预计到港时间: {record['estimated_arrival_time']}")
        
        # 检查时间节点表
        cursor.execute("""
            SELECT sd.date, sd.location, sd.description, sd.created_at
            FROM shipment_dates sd
            JOIN shipment_records sr ON sd.shipment_id = sr.id
            WHERE sr.bill_of_lading LIKE 'BEIJING_TIME_BL%'
            ORDER BY sd.created_at DESC LIMIT 10
        """)
        
        dates = cursor.fetchall()
        print(f"\n[VERIFY] 检查时间节点表的时间记录...")
        for date_record in dates:
            print(f"[TIME] 时间节点 {date_record['location']} - {date_record['description']}:")
            print(f"  - 节点时间: {date_record['date']}")
            print(f"  - 创建时间: {date_record['created_at']}")
        
        conn.close()
        
    except Exception as e:
        print(f"[ERROR] 验证数据库时间记录失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("[TEST] 开始东八区时间测试...")
    print(f"[TEST] 测试开始时间: {get_beijing_time_str()}")
    
    # 运行所有测试
    test_beijing_time_functions()
    test_task_manager_beijing_time()
    test_shipment_manager_beijing_time()
    verify_database_times()
    
    print("\n" + "=" * 60)
    print("[SUCCESS] 东八区时间测试完成")
    print("[INFO] 请检查上述输出，确认:")
    print("   1. 东八区时间函数工作正常")
    print("   2. 数据库中的时间记录使用东八区时间")
    print("   3. 创建时间、更新时间都是东八区时间")
    print("   4. 时间格式包含正确的时区信息(+08:00)")
    print("=" * 60)

if __name__ == "__main__":
    main()