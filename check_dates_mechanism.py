#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 shipment_dates 表数据插入机制
分析为什么现在没有数据插入
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

import sqlite3
from shipment_manager import ShipmentManager

def check_shipment_dates_mechanism():
    """检查 shipment_dates 表数据插入机制"""
    print("=== 检查 shipment_dates 表数据插入机制 ===")
    
    try:
        # 1. 检查表是否存在及结构
        print("\n1. 检查 shipment_dates 表结构...")
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='shipment_dates'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("   [OK] shipment_dates 表存在")
            
            cursor.execute("PRAGMA table_info(shipment_dates)")
            columns = cursor.fetchall()
            print("   表结构:")
            for col in columns:
                print(f"     - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        else:
            print("   [ERROR] shipment_dates 表不存在!")
            return False
        
        # 2. 检查现有数据
        print("\n2. 检查现有数据...")
        cursor.execute("SELECT COUNT(*) FROM shipment_dates")
        count = cursor.fetchone()[0]
        print(f"   总记录数: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM shipment_dates ORDER BY id DESC LIMIT 5")
            recent = cursor.fetchall()
            print("   最近5条记录:")
            for record in recent:
                print(f"     ID: {record[0]}, shipment_id: {record[1]}, date: {record[2]}")
                print(f"         location: {record[5]}, created_at: {record[9]}")
        
        conn.close()
        
        # 3. 测试 add_shipment_dates 方法
        print("\n3. 测试 add_shipment_dates 方法...")
        manager = ShipmentManager()
        
        # 先创建一个测试记录
        record_id = manager.create_shipment_record(
            bill_of_lading="DATES_TEST_001",
            container_number="DATES_CONT_001",
            carrier_company="TEST",
            estimated_arrival_time=None,
            remarks="测试dates插入",
            created_by="dates_test"
        )
        
        print(f"   创建测试记录 ID: {record_id}")
        
        # 准备测试数据
        test_dates = [
            {
                'date': '2025-08-10 10:00:00',
                'location': '测试港口',
                'event': '测试事件',
                'status': '测试状态',
                'event_type': 'test',
                'vessel_info': {'name': 'TEST_VESSEL', 'voyage': 'TEST001'}
            }
        ]
        
        print("   准备插入测试dates数据...")
        success = manager.add_shipment_dates(record_id, test_dates)
        
        if success:
            print("   [SUCCESS] add_shipment_dates 方法执行成功")
            
            # 验证数据是否真的插入了
            conn = sqlite3.connect('db/shipment_records.db')
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (record_id,))
            new_count = cursor.fetchone()[0]
            
            if new_count > 0:
                print(f"   [VERIFIED] 成功插入 {new_count} 条dates记录")
                
                cursor.execute("""
                    SELECT date, location, description, created_at 
                    FROM shipment_dates 
                    WHERE shipment_id = ?
                """, (record_id,))
                
                inserted_data = cursor.fetchall()
                for data in inserted_data:
                    print(f"     - 日期: {data[0]}, 地点: {data[1]}")
                    print(f"       事件: {data[2]}, 创建时间: {data[3]}")
                    
                    if '+08:00' in str(data[3]):
                        print("       [OK] 使用东八区时间")
                    else:
                        print(f"       [WARNING] 时间格式可能有问题: {data[3]}")
                
                result = True
            else:
                print("   [ERROR] 数据没有真正插入到数据库")
                result = False
            
            conn.close()
        else:
            print("   [ERROR] add_shipment_dates 方法执行失败")
            result = False
        
        return result
        
    except Exception as e:
        print(f"[ERROR] 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_workflow():
    """分析完整的工作流程"""
    print("\n=== 分析完整工作流程 ===")
    
    print("\n正常的数据插入流程应该是:")
    print("1. 用户调用 ShipmentManager.create_shipment_record() 创建主记录")
    print("2. 然后调用 ShipmentManager.add_shipment_dates() 添加时间节点")
    print("3. 或者通过 process_ai_result() 自动调用")
    
    print("\nshipment_dates 表的数据来源:")
    print("- AI解析结果中的 dates_data")
    print("- 手动调用 add_shipment_dates 方法")
    print("- 不依赖于数据库触发器")
    
    print("\n如果现在没有数据，可能的原因:")
    print("1. AI解析没有返回 dates_data")
    print("2. add_shipment_dates 方法有bug")
    print("3. 数据库锁定或权限问题")
    print("4. 数据被意外删除")

if __name__ == "__main__":
    print("shipment_dates 表数据插入机制检查")
    print("=" * 60)
    
    success = check_shipment_dates_mechanism()
    analyze_workflow()
    
    if success:
        print("\n[CONCLUSION] add_shipment_dates 方法工作正常")
        print("如果实际使用中没有数据，请检查:")
        print("1. AI解析结果是否包含 dates_data")
        print("2. 是否有调用 add_shipment_dates 方法")
        print("3. 调用时是否有错误发生")
    else:
        print("\n[CONCLUSION] add_shipment_dates 方法有问题，需要修复")