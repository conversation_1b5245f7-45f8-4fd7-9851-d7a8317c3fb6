#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已完成货运记录保护机制
验证已完成的货运记录不会被任务执行错误更新状态
"""

import sqlite3
import os
from datetime import datetime

def test_completed_record_protection():
    """测试已完成记录保护机制"""
    
    if not os.path.exists('db/shipment_records.db'):
        print("❌ 货运记录数据库不存在")
        return
        
    if not os.path.exists('db/task_queue.db'):
        print("❌ 任务队列数据库不存在")
        return
    
    print("🔒 测试已完成货运记录保护机制...")
    print("=" * 60)
    
    # 连接两个数据库
    shipment_conn = sqlite3.connect('db/shipment_records.db')
    shipment_conn.row_factory = sqlite3.Row
    task_conn = sqlite3.connect('db/task_queue.db')
    task_conn.row_factory = sqlite3.Row
    
    shipment_cursor = shipment_conn.cursor()
    task_cursor = task_conn.cursor()
    
    # 查找已完成的货运记录
    shipment_cursor.execute("""
        SELECT id, bill_of_lading, container_number, status, updated_at
        FROM shipment_records 
        WHERE status = '已完成'
        ORDER BY updated_at DESC
        LIMIT 5
    """)
    
    completed_records = shipment_cursor.fetchall()
    
    print(f"📋 找到 {len(completed_records)} 条已完成的货运记录:")
    
    for record in completed_records:
        tracking_number = record['bill_of_lading'] or record['container_number']
        
        print(f"\n✅ 已完成记录: ID {record['id']}, 跟踪号: {tracking_number}")
        print(f"   状态: {record['status']}, 最后更新: {record['updated_at']}")
        
        # 查看该跟踪号是否有进行中的任务
        task_cursor.execute("""
            SELECT id, task_stage, status, remarks, created_at
            FROM task_queue 
            WHERE tracking_number = ? 
            AND status IN ('pending', 'processing')
            ORDER BY created_at DESC
        """, (tracking_number,))
        
        active_tasks = task_cursor.fetchall()
        
        if active_tasks:
            print(f"   ⚠️  发现该跟踪号有 {len(active_tasks)} 个活跃任务:")
            for task in active_tasks:
                task_id_short = task['id'][:8]
                
                # 检查任务备注中的货运记录ID
                linked_record_id = "未知"
                if task['remarks'] and '货运记录ID:' in task['remarks']:
                    try:
                        linked_record_id = task['remarks'].split('货运记录ID:')[1].split(',')[0].strip()
                    except:
                        linked_record_id = "解析失败"
                
                protection_status = "🔒 受保护" if linked_record_id == str(record['id']) else "✅ 安全"
                
                print(f"      🔄 {task['task_stage']} 任务: {task_id_short}, 状态: {task['status']}")
                print(f"         关联记录ID: {linked_record_id}, {protection_status}")
                
                if linked_record_id == str(record['id']):
                    print(f"         💡 修复后逻辑: 将检测到记录已完成，查找最新未完成记录进行更新")
        else:
            print(f"   ✅ 该跟踪号没有活跃任务，记录安全")
        
        # 查看该跟踪号是否有其他未完成记录
        shipment_cursor.execute("""
            SELECT id, status, created_at
            FROM shipment_records 
            WHERE (bill_of_lading = ? OR container_number = ?)
            AND status != '已完成'
            ORDER BY created_at DESC
        """, (tracking_number, tracking_number))
        
        unfinished_records = shipment_cursor.fetchall()
        
        if unfinished_records:
            print(f"   📝 该跟踪号有 {len(unfinished_records)} 条未完成记录:")
            for unfinished in unfinished_records:
                print(f"      🆔 ID: {unfinished['id']}, 状态: {unfinished['status']}, 创建: {unfinished['created_at']}")
            print(f"      ➡️  活跃任务应该更新最新未完成记录: {unfinished_records[0]['id']}")
        else:
            print(f"   ℹ️  该跟踪号没有其他未完成记录")
    
    # 统计保护机制效果
    print(f"\n" + "=" * 60)
    print("🛡️  保护机制说明:")
    print("   1. 检查任务备注中的货运记录ID对应记录是否已完成")
    print("   2. 如果已完成，自动查找该跟踪号的最新未完成记录")
    print("   3. 如果没有未完成记录，跳过状态更新，保护历史数据")
    print("   4. 确保已完成的历史记录不会被意外修改")
    
    # 模拟保护逻辑测试
    print(f"\n🧪 模拟保护逻辑测试:")
    if completed_records:
        test_record = completed_records[0]
        test_tracking = test_record['bill_of_lading'] or test_record['container_number']
        
        print(f"   测试记录: ID {test_record['id']}, 跟踪号: {test_tracking}")
        
        # 模拟检查逻辑
        shipment_cursor.execute('SELECT status FROM shipment_records WHERE id = ?', (test_record['id'],))
        status_check = shipment_cursor.fetchone()
        
        if status_check and status_check[0] == '已完成':
            print(f"   ✅ 状态检查: 记录已完成，触发保护机制")
            
            # 查找替代记录
            shipment_cursor.execute('''
                SELECT id FROM shipment_records 
                WHERE (bill_of_lading = ? OR container_number = ?)
                AND status != '已完成'
                ORDER BY created_at DESC 
                LIMIT 1
            ''', (test_tracking, test_tracking))
            
            alternative = shipment_cursor.fetchone()
            if alternative:
                print(f"   🎯 找到替代记录: ID {alternative['id']}")
            else:
                print(f"   🚫 无替代记录，跳过更新")
    
    shipment_conn.close()
    task_conn.close()

if __name__ == "__main__":
    test_completed_record_protection()