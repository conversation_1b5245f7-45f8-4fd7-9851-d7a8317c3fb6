# 船期查询个人工作台产品需求文档

## 1. 产品概述

一个现代化的船期查询个人工作台，通过创建查询任务来触发服务器自动查询船期信息。

* 采用Manus风格的深色主题界面设计，左侧导航栏配合右侧主内容区域，提供专业的查询体验。

* 核心功能是新建查询任务，用户创建任务后服务器会自动执行查询并返回结果展示，暂时使用模拟数据。

## 2. 核心功能

### 2.1 用户角色

| 角色   | 注册方式      | 核心权限               |
| ---- | --------- | ------------------ |
| 默认用户 | 无需注册，直接访问 | 可使用所有船期查询功能，保存查询历史 |

### 2.2 功能模块

我们的船期查询工作台包含以下主要页面：

1. **工作台主页**：新建查询任务、任务列表、快速操作面板
2. **任务创建页**：查询条件设置、任务配置、提交执行
3. **查询结果页**：任务执行结果、船期数据展示、筛选分析
4. **任务管理页**：历史任务、任务状态、批量操作
5. **设置页面**：个人偏好、通知设置、界面配置

### 2.3 页面详情

| 页面名称  | 模块名称   | 功能描述                      |
| ----- | ------ | ------------------------- |
| 工作台主页 | 新建任务按钮 | 大尺寸主要操作按钮，点击进入任务创建流程      |
| 工作台主页 | 任务列表   | 显示最近创建的查询任务，包含状态、进度和结果预览  |
| 工作台主页 | 快速操作   | 常用查询模板，一键创建预设查询任务         |
| 工作台主页 | 统计面板   | 显示任务执行统计、成功率、数据概览         |
| 任务创建页 | 查询条件   | 起始港、目的港、船公司、时间范围等查询参数设置   |
| 任务创建页 | 任务配置   | 任务名称、执行频率、通知设置等配置选项       |
| 任务创建页 | 提交执行   | 验证查询条件，提交任务到服务器执行队列       |
| 查询结果页 | 结果展示   | 以表格和卡片形式展示查询到的船期数据（模拟数据）  |
| 查询结果页 | 数据筛选   | 按船公司、开船时间、运输时间等条件筛选结果     |
| 查询结果页 | 导出功能   | 支持导出Excel、PDF等格式的查询结果     |
| 任务管理页 | 历史任务   | 查看所有历史查询任务，支持重新执行和删除      |
| 任务管理页 | 任务状态   | 实时显示任务执行状态：排队中、执行中、已完成、失败 |
| 任务管理页 | 批量操作   | 批量删除、重新执行、导出多个任务结果        |
| 设置页面  | 界面设置   | 深色/浅色主题切换、字体大小、布局偏好       |
| 设置页面  | 通知设置   | 任务完成通知、邮件提醒、浏览器推送配置       |

## 3. 核心流程

用户打开工作台后，点击"新建查询任务"按钮进入任务创建页面，设置查询条件和任务配置后提交执行。服务器接收任务并开始查询船期信息，用户可以在任务管理页面查看执行状态。任务完成后，用户在查询结果页面查看船期数据，可以进行筛选、分析和导出操作。

```mermaid
graph TD
    A[工作台主页] --> B[任务创建页]
    B --> C[任务管理页]
    C --> D[查询结果页]
    A --> C
    A --> E[设置页面]
    D --> A
```

## 4. 用户界面设计

### 4.1 设计风格

* **主色调**：深色背景(#1a1a1a)，侧边栏深灰(#2d2d2d)，主内容区(#f5f5f5)，蓝色强调色(#007acc)

* **按钮样式**：现代化扁平按钮，主要按钮使用蓝色，次要按钮使用灰色边框

* **字体**：-apple-system, BlinkMacSystemFont, 'Segoe UI'，标题14-18px，正文12-14px

* **布局风格**：左侧固定导航栏(240px)，右侧主内容区域，卡片式内容布局

* **图标风格**：使用Feather Icons或类似的简洁线性图标，统一16px尺寸

### 4.2 页面设计概览

| 页面名称  | 模块名称   | UI元素                           |
| ----- | ------ | ------------------------------ |
| 工作台主页 | 新建任务按钮 | 大尺寸蓝色主按钮，居中显示，带加号图标和"新建查询任务"文字 |
| 工作台主页 | 任务列表   | 卡片式布局，每个任务卡片显示名称、状态、创建时间和操作按钮  |
| 工作台主页 | 统计面板   | 顶部横向统计卡片，显示总任务数、成功率、最近执行等数据    |
| 任务创建页 | 查询表单   | 分步骤表单，包含港口选择器、日期选择器、下拉菜单等控件    |
| 任务创建页 | 提交按钮   | 底部固定的蓝色提交按钮，带加载状态和成功反馈         |
| 查询结果页 | 数据表格   | 响应式表格，支持排序、筛选，每行包含船期详细信息       |
| 查询结果页 | 筛选面板   | 左侧折叠式筛选面板，包含多选框、滑块、日期范围等控件     |
| 任务管理页 | 任务列表   | 表格式布局，显示任务状态徽章、进度条、操作菜单        |
| 设置页面  | 设置分组   | 垂直分组的设置项，使用开关、选择器、输入框等控件       |

### 4.3 响应式设计

产品采用桌面优先的响应式设计，主要面向桌面端用户，在移动端提供基本功能访问。支持PWA渐进式Web应用，可离线使用基本功能，使用HTML、CSS、JavaScript等基础web技术开发，确保兼容性和性能。

### 4.4 技术实现

* **前端技术栈**：纯HTML5、CSS3、原生JavaScript，不依赖复杂框架

* **PWA功能**：Service Worker缓存、Web App Manifest、离线数据存储

* **数据模拟**：使用本地JSON数据模拟后台接口响应，便于开发和演示

* **单页应用**：使用History API实现路由，动态加载页面内容

* **响应式布局**：CSS Grid和Flexbox实现自适应布局，支持多种屏幕尺寸

