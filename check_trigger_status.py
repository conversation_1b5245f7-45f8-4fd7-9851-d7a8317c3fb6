#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前数据库触发器状态和系统影响
"""

import sqlite3
import os

def check_current_trigger_status():
    """检查当前触发器状态"""
    print("=== 当前数据库触发器状态检查 ===")
    
    db_path = 'db/shipment_records.db'
    if not os.path.exists(db_path):
        print(f"[ERROR] 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查所有触发器
        print("\n1. 检查现有触发器...")
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='trigger'")
        triggers = cursor.fetchall()
        
        if triggers:
            print(f"   找到 {len(triggers)} 个触发器:")
            for name, sql in triggers:
                print(f"   - {name}")
                if sql:
                    print(f"     SQL: {sql[:100]}...")
        else:
            print("   [WARNING] 当前没有任何触发器!")
        
        # 检查表结构
        print("\n2. 检查 shipment_records 表结构...")
        cursor.execute("PRAGMA table_info(shipment_records)")
        columns = cursor.fetchall()
        
        for col in columns:
            col_name = col[1]
            if col_name in ['created_at', 'updated_at']:
                print(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # 检查 shipment_dates 表是否存在
        print("\n3. 检查 shipment_dates 表...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='shipment_dates'")
        dates_table = cursor.fetchone()
        
        if dates_table:
            print("   [OK] shipment_dates 表存在")
            cursor.execute("SELECT COUNT(*) FROM shipment_dates")
            count = cursor.fetchone()[0]
            print(f"   记录数量: {count}")
        else:
            print("   [ERROR] shipment_dates 表不存在!")
        
        conn.close()
        
    except Exception as e:
        print(f"[ERROR] 检查失败: {e}")

def analyze_trigger_deletion_impact():
    """分析触发器删除的影响"""
    print("\n=== 触发器删除影响分析 ===")
    
    # 从原始数据库初始化文件分析触发器的真正作用
    init_file = 'db/init_shipment_database.py'
    if os.path.exists(init_file):
        print("\n分析原始数据库初始化文件...")
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'CREATE TRIGGER' in content:
            print("原始触发器代码:")
            lines = content.split('\n')
            in_trigger = False
            for line in lines:
                if 'CREATE TRIGGER' in line:
                    in_trigger = True
                if in_trigger:
                    print(f"   {line}")
                if in_trigger and line.strip().endswith("END"):
                    break
        
        print("\n[ANALYSIS] 基于原始代码分析:")
        print("- 触发器名称: update_shipment_records_timestamp")
        print("- 触发时机: AFTER UPDATE ON shipment_records")
        print("- 作用: 自动更新 updated_at 字段为 CURRENT_TIMESTAMP")
        print("- 问题: CURRENT_TIMESTAMP 使用系统时区，不是东八区")
        print("- 我的错误: 完全删除了触发器，应该修改为使用东八区时间")

if __name__ == "__main__":
    check_current_trigger_status()
    analyze_trigger_deletion_impact()