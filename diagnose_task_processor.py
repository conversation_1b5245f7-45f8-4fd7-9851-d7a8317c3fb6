#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查任务处理器状态和任务执行情况
"""

import sqlite3
import os
import time
from datetime import datetime

def check_task_processor_working():
    """检查任务处理器是否正常工作"""
    print("🔍 检查任务处理器状态...")
    
    if not os.path.exists('db/task_queue.db'):
        print("❌ 任务队列数据库不存在")
        return False
    
    # 查看当前pending任务
    conn = sqlite3.connect('db/task_queue.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("\n📊 当前任务状态分布:")
    cursor.execute("""
        SELECT status, COUNT(*) as count
        FROM task_queue 
        GROUP BY status
    """)
    
    status_dist = cursor.fetchall()
    for row in status_dist:
        print(f"   {row['status']:15} {row['count']:3} 个")
    
    # 查看是否有长时间pending的任务
    print("\n⏰ 检查长时间待处理的任务:")
    cursor.execute("""
        SELECT id, task_name, tracking_number, status, 
               datetime(created_at) as created_time,
               julianday('now') - julianday(created_at) as hours_ago
        FROM task_queue 
        WHERE status = 'pending'
        ORDER BY created_at
        LIMIT 10
    """)
    
    pending_tasks = cursor.fetchall()
    if pending_tasks:
        for task in pending_tasks:
            hours = task['hours_ago'] * 24  # 转换为小时
            print(f"   {task['tracking_number']:15} {task['status']:10} {hours:.1f}小时前创建")
        
        if any(task['hours_ago'] * 24 > 0.1 for task in pending_tasks):  # 超过6分钟
            print("   ⚠️  发现长时间pending的任务，可能任务处理器未运行")
    else:
        print("   ✅ 无长时间pending任务")
    
    # 查看最近的processing/completed任务
    print("\n📈 检查最近任务执行情况:")
    cursor.execute("""
        SELECT id, task_name, tracking_number, status, 
               datetime(created_at) as created_time,
               datetime(started_at) as started_time,
               datetime(completed_at) as completed_time
        FROM task_queue 
        WHERE status IN ('processing', 'completed', 'failed')
        ORDER BY created_at DESC
        LIMIT 5
    """)
    
    recent_tasks = cursor.fetchall()
    if recent_tasks:
        for task in recent_tasks:
            print(f"   {task['tracking_number']:15} {task['status']:10}")
            print(f"     创建: {task['created_time']}")
            if task['started_time']:
                print(f"     开始: {task['started_time']}")
            if task['completed_time']:
                print(f"     完成: {task['completed_time']}")
            print("     ---")
    else:
        print("   ⚠️  无最近执行的任务，可能任务处理器从未运行")
    
    conn.close()

def check_task_processor_file():
    """检查任务处理器文件是否存在"""
    print("\n🔍 检查任务处理器相关文件:")
    
    required_files = [
        'task_processor.py',
        'task_executor.py', 
        'task_manager.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 缺失")
    
    # 检查两阶段处理器
    two_stage_files = [
        'scraping_executor.py',
        'ai_analysis_executor.py',
        'two_stage_task_processor.py'
    ]
    
    print("\n   两阶段处理器文件:")
    for file in two_stage_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 缺失")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议:")
    print("   1. 确保app.py启动时任务处理器正常启动")
    print("   2. 在应用中使用'工具->任务状态查看器'检查处理器状态")
    print("   3. 尝试手动启动/重启任务处理器")
    print("   4. 检查是否有Python异常阻止任务处理")
    print("   5. 查看控制台输出中的错误信息")

def create_test_task():
    """创建一个测试任务来验证处理器"""
    print("\n🧪 创建测试任务验证处理器...")
    
    try:
        from task_manager import TaskManager
        
        task_manager = TaskManager()
        
        # 创建一个简单的测试任务
        test_tracking_number = f"TEST{int(time.time())}"
        task_id = task_manager.create_task(
            tracking_number=test_tracking_number,
            task_type="bill_of_lading",
            creator_id="system_test",
            creator_name="系统测试",
            task_stage="scraping",
            remarks="任务处理器测试任务"
        )
        
        print(f"   ✅ 创建测试任务: {task_id}")
        print(f"   📋 跟踪号: {test_tracking_number}")
        print("   等待10秒观察任务状态变化...")
        
        # 等待并检查状态变化
        for i in range(10):
            time.sleep(1)
            task = task_manager.get_task_by_id(task_id)
            if task:
                print(f"   第{i+1}秒: {task['status']}")
                if task['status'] != 'pending':
                    print("   ✅ 任务状态已变化，任务处理器工作正常")
                    return True
            else:
                print("   ❌ 无法获取任务信息")
                return False
        
        print("   ⚠️  任务状态未变化，可能任务处理器未运行")
        return False
        
    except ImportError as e:
        print(f"   ❌ 无法导入任务管理器: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 创建测试任务失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 任务处理器诊断工具")
    print("=" * 50)
    
    check_task_processor_working()
    check_task_processor_file()
    
    # 询问是否创建测试任务
    print("\n" + "=" * 50)
    response = input("是否创建测试任务验证处理器？(y/n): ").strip().lower()
    if response in ['y', 'yes', '是']:
        create_test_task()
    
    suggest_solutions()
    print("\n✅ 诊断完成")