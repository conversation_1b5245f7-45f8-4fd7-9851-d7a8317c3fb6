#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
承运人校验服务
基于现有的carrier_lookup.py实现API封装
"""

import re
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from api.models.user_schemas import (
    CarrierInfo, 
    ValidationResult, 
    CarrierValidationResponse, 
    BatchValidationResponse
)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.carrier_lookup import get_company_info

class CarrierValidationService:
    """承运人校验服务类"""
    
    @staticmethod
    def validate_tracking_number(tracking_number: str) -> ValidationResult:
        """
        校验单个提单号/箱号
        
        Args:
            tracking_number: 提单号/箱号
            
        Returns:
            校验结果
        """
        tracking_number = tracking_number.strip()
        
        if not tracking_number:
            return ValidationResult(
                tracking_number=tracking_number,
                is_valid=False,
                carrier_info=None,
                error_message="提单号不能为空"
            )
        
        if len(tracking_number) > 50:
            return ValidationResult(
                tracking_number=tracking_number,
                is_valid=False,
                carrier_info=None,
                error_message="提单号长度不能超过50个字符"
            )
        
        # 使用现有的承运人查找逻辑
        try:
            company_info = get_company_info(tracking_number)
            
            if company_info:
                # 提取承运人代码（从公司名称中提取）
                carrier_code = CarrierValidationService._extract_carrier_code(company_info["company"])
                
                carrier_info = CarrierInfo(
                    code=carrier_code,
                    name=company_info["company"],
                    full_name=company_info["company"],
                    url=company_info["international_site"] or company_info["chinese_site"] or "",
                    tracking_url=company_info.get("tracking_site")
                )
                
                return ValidationResult(
                    tracking_number=tracking_number,
                    is_valid=True,
                    carrier_info=carrier_info,
                    error_message=None
                )
            else:
                return ValidationResult(
                    tracking_number=tracking_number,
                    is_valid=False,
                    carrier_info=None,
                    error_message="未找到匹配的承运人"
                )
                
        except Exception as e:
            return ValidationResult(
                tracking_number=tracking_number,
                is_valid=False,
                carrier_info=None,
                error_message=f"校验出错: {str(e)}"
            )
    
    @staticmethod
    def batch_validate_tracking_numbers(
        tracking_numbers: List[str], 
        deduplicate: bool = True
    ) -> BatchValidationResponse:
        """
        批量校验提单号/箱号
        
        Args:
            tracking_numbers: 提单号/箱号列表
            deduplicate: 是否去重
            
        Returns:
            批量校验结果
        """
        try:
            # 预处理：去空格、去空值
            processed_numbers = [num.strip().upper() for num in tracking_numbers if num.strip()]
            
            # 去重处理（如果需要）
            if deduplicate:
                processed_numbers = list(dict.fromkeys(processed_numbers))  # 保持顺序的去重
            
            # 限制数量
            if len(processed_numbers) > 200:
                processed_numbers = processed_numbers[:200]
            
            results = []
            valid_count = 0
            invalid_count = 0
            carrier_stats = {}
            
            # 逐个校验
            for tracking_number in processed_numbers:
                result = CarrierValidationService.validate_tracking_number(tracking_number)
                results.append(result)
                
                if result.is_valid:
                    valid_count += 1
                    if result.carrier_info:
                        carrier_code = result.carrier_info.code
                        carrier_stats[carrier_code] = carrier_stats.get(carrier_code, 0) + 1
                else:
                    invalid_count += 1
            
            summary = {
                "total": len(results),
                "valid": valid_count,
                "invalid": invalid_count,
                "carriers": len(carrier_stats),
                "carrier_breakdown": carrier_stats
            }
            
            return BatchValidationResponse(
                success=True,
                results=results,
                summary=summary
            )
            
        except Exception as e:
            return BatchValidationResponse(
                success=False,
                results=[],
                summary={
                    "total": 0,
                    "valid": 0,
                    "invalid": 0,
                    "error": str(e)
                }
            )
    
    @staticmethod
    def parse_batch_input(text_input: str) -> List[str]:
        """
        解析批量输入文本（支持多种分隔符）
        
        Args:
            text_input: 输入文本
            
        Returns:
            解析后的提单号列表
        """
        if not text_input.strip():
            return []
        
        # 支持多种分隔符：换行符、制表符、逗号、分号、空格
        lines = re.split(r'\r?\n|\t|,|;|\s+', text_input)
        
        # 去空格、去空值、去重、限制数量
        tracking_numbers = []
        seen = set()
        
        for line in lines:
            line = line.strip().upper()
            if line and line not in seen:
                tracking_numbers.append(line)
                seen.add(line)
                
                # 限制数量
                if len(tracking_numbers) >= 200:
                    break
        
        return tracking_numbers
    
    @staticmethod
    def _extract_carrier_code(company_name: str) -> str:
        """
        从公司名称中提取承运人代码
        
        Args:
            company_name: 公司名称
            
        Returns:
            承运人代码
        """
        # 简单的代码提取逻辑
        code_mappings = {
            "MSC": "MSC",
            "Mediterranean Shipping": "MSC",
            "地中海航运": "MSC",
            "Maersk": "MAERSK",
            "马士基": "MAERSK",
            "CMA CGM": "CMA_CGM",
            "达飞海运": "CMA_CGM",
            "COSCO": "COSCO",
            "中远海运": "COSCO",
            "Hapag-Lloyd": "HAPAG_LLOYD",
            "赫伯罗特": "HAPAG_LLOYD",
            "ONE": "ONE",
            "Ocean Network Express": "ONE",
            "海洋网联": "ONE",
            "Evergreen": "EVERGREEN",
            "长荣海运": "EVERGREEN",
            "HMM": "HMM",
            "Hyundai": "HMM",
            "现代商船": "HMM",
            "Yang Ming": "YANG_MING",
            "阳明海运": "YANG_MING",
            "ZIM": "ZIM",
            "以星航运": "ZIM",
            "OOCL": "OOCL",
            "东方海外": "OOCL"
        }
        
        for keyword, code in code_mappings.items():
            if keyword in company_name:
                return code
        
        # 如果没有匹配到，尝试提取括号内的英文缩写
        import re
        match = re.search(r'\(([A-Z]+)\)', company_name)
        if match:
            return match.group(1)
        
        # 最后，提取第一个英文单词作为代码
        words = company_name.split()
        for word in words:
            if re.match(r'^[A-Z]+$', word) and len(word) >= 2:
                return word
        
        return "UNKNOWN"

# 获取服务实例的依赖注入函数
def get_carrier_validation_service() -> CarrierValidationService:
    """获取承运人校验服务实例"""
    return CarrierValidationService()