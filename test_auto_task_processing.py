#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动任务处理功能
验证任务处理器是否能够自动监控和处理任务队列
"""

import time
import sys
from datetime import datetime
from task_manager import TaskManager
from shipment_manager import ShipmentManager

def test_auto_task_processing():
    """
    测试自动任务处理功能
    """
    print("=" * 60)
    print("🧪 测试自动任务处理功能")
    print("=" * 60)
    
    # 初始化管理器
    task_manager = TaskManager()
    shipment_manager = ShipmentManager()
    
    print("\n📋 步骤1: 创建测试货运记录（会自动创建任务）")
    
    # 创建测试货运记录
    shipment_data = {
        'bill_of_lading': f'TEST{int(time.time())}',
        'container_number': f'TESTU{int(time.time())}123',
        'carrier_company': 'MSC',
        'departure_port': '上海港',
        'arrival_port': '洛杉矶港',
        'departure_date': '2024-01-15',
        'estimated_arrival_date': '2024-02-15',
        'cargo_description': '测试货物',
        'status': '排队中'
    }
    
    try:
        shipment_id = shipment_manager.create_shipment_record(**shipment_data)
        print(f"✅ 创建货运记录成功，ID: {shipment_id}")
        
        # 获取关联的任务
        tasks = task_manager.get_tasks_by_status('pending')
        if tasks:
            task_id = tasks[0]['id']
            print(f"✅ 自动创建任务成功，任务ID: {task_id}")
            
            print("\n📋 步骤2: 检查任务状态变化")
            print("⏳ 等待任务处理器自动处理任务...")
            print("💡 提示：请确保主应用程序(app.py)正在运行，任务处理器才会自动工作")
            
            # 监控任务状态变化
            start_time = time.time()
            timeout = 60  # 60秒超时
            
            while time.time() - start_time < timeout:
                task_info = task_manager.get_task_by_id(task_id)
                if task_info:
                    status = task_info['status']
                    print(f"📊 当前任务状态: {status} ({datetime.now().strftime('%H:%M:%S')})")
                    
                    if status == 'completed':
                        print("✅ 任务已完成！")
                        
                        # 检查货运记录状态是否已更新
                        shipment = shipment_manager.get_shipment_record(shipment_id)
                        if shipment:
                            print(f"📦 货运记录状态: {shipment['status']}")
                            if shipment['estimated_arrival_time']:
                                print(f"🕐 预计到港时间: {shipment['estimated_arrival_time']}")
                        break
                    elif status == 'processing':
                        print("🔄 任务正在处理中...")
                    
                time.sleep(5)  # 每5秒检查一次
            else:
                print("⏰ 等待超时，请检查任务处理器是否正在运行")
                print("💡 提示：运行 python app.py 启动主应用程序以激活任务处理器")
        else:
            print("❌ 未找到待处理任务")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_auto_task_processing()