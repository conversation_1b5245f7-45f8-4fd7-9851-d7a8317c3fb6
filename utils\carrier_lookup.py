import re
import os
from typing import Optional, Dict

# 配置选项：是否使用数据库（True）还是硬编码字典（False）
# 默认使用数据库，除非明确设置为false
USE_DATABASE = os.getenv('CARRIER_USE_DATABASE', 'true').lower() == 'true'

# 公司数据字典：键为公司名称，值包含国际站、中文站、追踪页面、SCAC前缀列表和提单号规则正则
# 注意：当 USE_DATABASE=True 时，此字典将被数据库查询替代
COMPANY_DATA = {
    "MSC (Mediterranean Shipping Company / 地中海航运)": {
        "international_site": "https://www.msc.com/",
        "chinese_site": "https://www.msccargo.cn/",
        "tracking_site": "https://www.msc.com/en/track-a-shipment",
        "scac_prefixes": ["MSCU", "MEDU"],
        "bl_patterns": [r"^(177|MEDU)\w*"]
    },
    "Maersk (马士基)": {
        "international_site": "https://www.maersk.com/",
        "chinese_site": "https://www.maersk.com/local-information/asia-pacific/china",
        "tracking_site": "https://www.maersk.com/tracking/",
        "scac_prefixes": ["MAEU"],
        "bl_patterns": [r"^\d{9}$", r"^SGHX\d{5}\w*"]
    },
    "CMA CGM (达飞海运)": {
        "international_site": "https://www.cma-cgm.com/",
        "chinese_site": "https://www.cma-cgm.com/local/china",
        "tracking_site": "https://www.cma-cgm.com/ebusiness/tracking",
        "scac_prefixes": ["CMDU"],
        "bl_patterns": [r"^(CN|WM|APLU|DR|CB|AC)\w*"]
    },
    "COSCO (中远海运)": {
        "international_site": "https://lines.coscoshipping.com/",
        "chinese_site": "https://www.coscoshipping.com/",
        "tracking_site": "https://elines.coscoshipping.com/ebusiness/cargotracking",
        "scac_prefixes": ["COSU"],
        "bl_patterns": [r"^COAU\d{10}\w*"]
    },
    "Hapag-Lloyd (赫伯罗特)": {
        "international_site": "https://www.hapag-lloyd.com/",
        "chinese_site": "https://www.hapag-lloyd.com/en/locations/china.html",
        "tracking_site": "https://www.hapag-lloyd.com/en/online-business/track/track-by-container-solution.html",
        "scac_prefixes": ["HLCU"],
        "bl_patterns": [r"^HLCU\w*"]
    },
    "ONE (Ocean Network Express / 海洋网联船务)": {
        "international_site": "https://www.one-line.com/",
        "chinese_site": "https://ch.one-line-services.com.cn/",
        "tracking_site": "https://ecomm.one-line.com/one-ecom/manage-shipment/cargo-tracking",
        "scac_prefixes": ["ONEY"],
        "bl_patterns": [r"^ONE\w*"]
    },
    "Evergreen (长荣海运)": {
        "international_site": "https://www.evergreen-marine.com/",
        "chinese_site": "https://www.evergreen-marine.com.cn/",
        "tracking_site": "https://www.shipmentlink.com/servlet/TDB1_CargoTracking.do",
        "scac_prefixes": ["EGLV"],
        "bl_patterns": [r"^EGLV\d{12}\w*"]
    },
    "HMM (Hyundai Merchant Marine / 现代商船)": {
        "international_site": "https://www.hmm21.com/",
        "chinese_site": "https://www.hmm21.com/cms/business/cn/index.jsp",
        "tracking_site": "https://www.hmm21.com/e-service/general/trackNTrace/TrackNTrace.do",
        "scac_prefixes": ["HDMU"],
        "bl_patterns": [r"^HDMU\w*", r"^(SZPE|NBOZ)\w*"]
    },
    "Yang Ming (阳明海运)": {
        "international_site": "https://www.yangming.com/",
        "chinese_site": "https://www.yangming.com.cn/",
        "tracking_site": "https://e-solution.yangming.com/e-service/track_trace/track_trace_cargo_tracking.aspx",
        "scac_prefixes": ["YMLU"],
        "bl_patterns": [r"^\w*(231|234|236)\w*"]
    },
    "ZIM (以星航运)": {
        "international_site": "https://www.zim.com/",
        "chinese_site": "https://www.zim.com/landing-pages/zim-wechat-qr",
        "tracking_site": "https://www.zim.com/tools/track-a-shipment",
        "scac_prefixes": ["ZIMU"],
        "bl_patterns": [r"^ZIMU\w*"]
    },
    "OOCL (东方海外货柜航运)": {
        "international_site": "https://www.oocl.com/",
        "chinese_site": "https://www.oocl.com/china/",
        "tracking_site": "https://www.oocl.com/eng/ourservices/eservices/cargotracking",
        "scac_prefixes": ["OOLU"],
        "bl_patterns": [r"^OOLU\d{10}\w*"]
    },
    "PIL (Pacific International Lines / 太平船务)": {
        "international_site": "https://www.pilship.com/",
        "chinese_site": None,
        "tracking_site": "https://www.pilship.com/en-track-trace-container-tracking-pil-pacific-international-lines/118.html",
        "scac_prefixes": ["PCIU"],
        "bl_patterns": [r"^[A-Z]{4}\d{8}\w*"]
    },
    "Wan Hai Lines (万海航运)": {
        "international_site": "https://www.wanhai.com/",
        "chinese_site": "https://www.wanhai.com/views/main.xhtml?lang=zh",
        "tracking_site": "https://www.wanhai.com/views/cargoTrack.xhtml",
        "scac_prefixes": ["WHLC", "WHLU"],
        "bl_patterns": [r"^\d{3}[A-Z]\d*\w*"]
    },
    "SM Line (韩国商船)": {
        "international_site": "https://smlines.com/",
        "chinese_site": None,
        "tracking_site": "https://esvc.smlines.com/smline/CUP_HOM_3301.do",
        "scac_prefixes": ["SMLU"],
        "bl_patterns": [r"^SMLU\w*"]
    },
    "CU Lines (中联航运)": {
        "international_site": "https://www.culines.com/",
        "chinese_site": "https://www.culines.com/cn/",
        "tracking_site": "https://www.culines.com/en/site/bill",
        "scac_prefixes": ["CULU"],
        "bl_patterns": [r"^CULU\w*"]
    },
    "Emirates Shipping Line (阿联酋航运)": {
        "international_site": "https://www.emiratesline.com/",
        "chinese_site": None,
        "tracking_site": "https://www.emiratesline.com/track/",
        "scac_prefixes": ["ESPU"],
        "bl_patterns": [r"^ESL\d*\w*"]
    },
    "Global Feeder Shipping LLC": {
        "international_site": "https://www.globalfeedershipping.com/",
        "chinese_site": None,
        "tracking_site": "https://global-feeder.com/",
        "scac_prefixes": ["GFAL"],
        "bl_patterns": [r"^GFAL\w*"]
    },
    "RCL (Regional Container Lines / 区域集装箱航运)": {
        "international_site": "https://www.rclgroup.com/",
        "chinese_site": None,
        "tracking_site": "https://eservice.rclgroup.com/CargoTracking/",
        "scac_prefixes": ["RCLU"],
        "bl_patterns": [r"^SHACW\d{8}\w*"]
    },
    "Tailwind Shipping Lines": {
        "international_site": "https://tailwind-shipping.com/",
        "chinese_site": None,
        "tracking_site": "https://tailwind-shipping.com/",
        "scac_prefixes": ["TWSU"],
        "bl_patterns": [r"^TWSU\w*"]
    },
    "Swire Shipping": {
        "international_site": "https://www.swireshipping.com/",
        "chinese_site": None,
        "tracking_site": "https://www.swireshipping.com/requestTrackShipment",
        "scac_prefixes": ["CHVW"],
        "bl_patterns": [r"^CHVW\w*"]
    },
    "MPC Container Ships": {
        "international_site": "https://www.mpc-container.com/",
        "chinese_site": None,
        "tracking_site": "http://mpconsol.com/Connect/ShipmentTracking.aspx",
        "scac_prefixes": ["MPCU"],
        "bl_patterns": [r"^MPCU\w*"]
    },
    "NYK Line (日本邮船)": {
        "international_site": "https://www.nyk.com/english/",
        "chinese_site": "https://www.nyk.com/china/",
        "tracking_site": "https://www.nykline.com/ecommerce/cargo_tracking/",
        "scac_prefixes": ["NYKU"],
        "bl_patterns": [r"^NYKU\w*"]
    },
    "Matson": {
        "international_site": "https://www.matson.com/",
        "chinese_site": None,
        "tracking_site": "https://www.matson.com/shipment-tracking.html",
        "scac_prefixes": ["MATS"],
        "bl_patterns": [r"^MATS\d{10}\w*"]
    },
    "Turkon Line": {
        "international_site": "https://www.turkon.com/en/",
        "chinese_site": None,
        "tracking_site": "https://myturkonline.turkon.com/tracking",
        "scac_prefixes": ["TRKU"],
        "bl_patterns": [r"^TRKU\w*"]
    },
    "DFDS": {
        "international_site": "https://www.dfds.com/",
        "chinese_site": None,
        "tracking_site": "https://www.dfds.com/en/freight-shipping/track-trace",
        "scac_prefixes": ["DFDS"],
        "bl_patterns": [r"^DFDS\w*"]
    },
    "Dole Ocean Cargo Express": {
        "international_site": "https://www.doleoceancargo.com/",
        "chinese_site": None,
        "tracking_site": "https://www.doleoceancargo.com/s/tracking-page?language=en_US",
        "scac_prefixes": ["DOLQ"],
        "bl_patterns": [r"^DOLQ\w*"]
    },
    "Sinotrans Ltd. (中外运)": {
        "international_site": "https://www.sinotrans.com/",
        "chinese_site": "https://www.sinotrans.com/",
        "tracking_site": "https://ebusiness.sinolines.com.cn/SnlEbusiness/EQUERY/TrackingCargoE.aspx",
        "scac_prefixes": ["12IH", "SINO"],
        "bl_patterns": [r"^SNL\dYMCL\d{6}\w*"]
    },
    "Kuehne + Nagel": {
        "international_site": "https://www.kn-portal.com/",
        "chinese_site": "https://cn.kuehne-nagel.com/",
        "tracking_site": "https://mykn.kuehne-nagel.com/public-tracking/",
        "scac_prefixes": ["KHNN"],
        "bl_patterns": [r"^KHNN\w*"]
    },
    "DHL Global Forwarding": {
        "international_site": "https://www.dhl.com/global-en/home/<USER>/global-forwarding.html",
        "chinese_site": "https://www.dhl.com/cn-en/home.html",
        "tracking_site": "https://www.dhl.com/global-en/home/<USER>",
        "scac_prefixes": ["DHC2"],
        "bl_patterns": [r"^DHC2\w*"]
    },
    # 添加豆包AI参考的公司，如果不在列表中
    "汉堡南美（HBS）": {
        "international_site": "https://www.hamburgsud.com/",
        "chinese_site": None,
        "tracking_site": "https://www.hamburgsud.com/en/ecommerce/tracking",
        "scac_prefixes": ["SUDU", "HBSU"],
        "bl_patterns": [r"^2[A-Z]\w*", r"^(SUDU|HBSU)\w*"]
    },
    "兴亚（HAL）": {
        "international_site": "https://www.heung-a.com/",
        "chinese_site": None,
        "tracking_site": "https://www.heung-a.com/cargo-tracking",
        "scac_prefixes": ["HASL"],
        "bl_patterns": [r"^HASL[A-Z]\d{11}\w*"]
    },
    "运达（IAL）": {
        "international_site": "https://www.ialcontainer.com/",
        "chinese_site": None,
        "tracking_site": "https://www.ialcontainer.com/track-trace/",
        "scac_prefixes": ["IALU"],
        "bl_patterns": [r"^A\d{2}[A-Z]{2}\d{5}\w*"]
    },
    "锦江（JJ）": {
        "international_site": "https://www.jjshipping.com/",
        "chinese_site": None,
        "tracking_site": "https://www.jjshipping.com/track-trace/",
        "scac_prefixes": ["JJSU"],
        "bl_patterns": [r"^JJ[A-Z]\d*\w*"]
    },
    "德翔（TSL）": {
        "international_site": "https://www.tsxlines.com/",
        "chinese_site": None,
        "tracking_site": "https://www.tsxlines.com/track-trace/",
        "scac_prefixes": ["TSLU"],
        "bl_patterns": [r"^\d{12}\w*", r"^(TSLU|TXZJ)\w*"]
    },
    "长锦（SKR）": {
        "international_site": "https://www.sckline.com/",
        "chinese_site": None,
        "tracking_site": "https://www.sckline.com/track-trace/",
        "scac_prefixes": ["SNKO"],
        "bl_patterns": [r"^SNKO\d{12}\w*"]
    },
    "金星（GSL）": {
        "international_site": "https://www.goldstarline.com/",
        "chinese_site": None,
        "tracking_site": "https://www.goldstarline.com/track-trace/",
        "scac_prefixes": ["GOSU"],
        "bl_patterns": [r"^GOSU[A-Z]{3}\d{7}\w*"]
    },
    "民生（MSL）": {
        "international_site": "https://www.minshengshipping.com/",
        "chinese_site": None,
        "tracking_site": "https://www.minshengshipping.com/track-trace/",
        "scac_prefixes": ["MSLU"],
        "bl_patterns": [r"^(CQ|SH)\w*"]
    },
    "海丰船务（SITC）": {
        "international_site": "https://www.sitc.com/",
        "chinese_site": None,
        "tracking_site": "https://www.sitc.com/track-trace/",
        "scac_prefixes": ["SITC"],
        "bl_patterns": [r"^SIT[A-Z]\d*\w*"]
    },
    "中外运集运（SNL）": {
        "international_site": "https://www.sinolines.com/",
        "chinese_site": None,
        "tracking_site": "https://ebusiness.sinolines.com.cn/SnlEbusiness/EQUERY/TrackingCargoE.aspx",
        "scac_prefixes": ["SNL"],
        "bl_patterns": [r"^SNL\dYMCL\d{6}\w*"]
    },
    "宏海箱运（RCL）": {
        "international_site": "https://www.rclgroup.com/",
        "chinese_site": None,
        "tracking_site": "https://eservice.rclgroup.com/CargoTracking/",
        "scac_prefixes": ["RCLU"],
        "bl_patterns": [r"^SHACW\d{8}\w*"]
    },
    # 可以继续添加其他公司如果需要
}

def get_company_info(input_str: str) -> Optional[Dict]:
    """
    根据输入的字符串（箱号或提单号）查找对应的公司信息
    
    Args:
        input_str: 输入的箱号或提单号
    
    Returns:
        dict: 包含公司信息的字典，如果未找到则返回None
    """
    if USE_DATABASE:
        # 使用数据库查询
        try:
            from .carrier_database import get_company_info as db_get_company_info
            return db_get_company_info(input_str)
        except (ImportError, FileNotFoundError) as e:
            print(f"警告: 数据库查询失败，回退到硬编码字典: {e}")
            # 回退到硬编码字典
            return _get_company_info_from_dict(input_str)
    else:
        # 使用硬编码字典
        return _get_company_info_from_dict(input_str)

def _get_company_info_from_dict(input_str: str) -> Optional[Dict]:
    """
    从硬编码字典中查找公司信息（原始实现）
    
    Args:
        input_str: 输入的箱号或提单号
    
    Returns:
        dict: 包含公司信息的字典，如果未找到则返回None
    """
    input_str = input_str.strip().upper()
    
    for company, data in COMPANY_DATA.items():
        # 检查SCAC前缀（箱号前缀）
        for prefix in data["scac_prefixes"]:
            if input_str.startswith(prefix):
                return {
                    "company": company,
                    "international_site": data["international_site"],
                    "chinese_site": data["chinese_site"],
                    "tracking_site": data["tracking_site"]
                }
        
        # 检查提单号规则（正则匹配）
        for pattern in data["bl_patterns"]:
            if re.match(pattern, input_str):
                return {
                    "company": company,
                    "international_site": data["international_site"],
                    "chinese_site": data["chinese_site"],
                    "tracking_site": data["tracking_site"]
                }
    
    return None  # 未匹配

# 示例使用
if __name__ == "__main__":
    test_input = "MEDUVS935363"  # 示例箱号或提单号
    result = get_company_info(test_input)
    if result:
        print(f"公司: {result['company']}")
        print(f"国际站: {result['international_site']}")
        print(f"中文站: {result['chinese_site']}")
        print(f"追踪页面: {result['tracking_site']}")
    else:
        print("未找到匹配的公司。")