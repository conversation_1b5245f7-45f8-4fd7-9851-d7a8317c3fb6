#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析执行器
专门负责AI数据分析任务
"""

import os
import time
import json
from datetime import datetime
from typing import Dict, Optional
import traceback

from ai.text_analyzer import TextAnalyzer
from utils.file_manager import get_file_manager


class AIAnalysisExecutor:
    """AI分析任务执行器"""
    
    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.text_analyzer = TextAnalyzer()
        print(f"[INIT] AI分析执行器初始化 (最大并发: {max_concurrent_tasks})")
    
    def execute_task(self, task: Dict) -> Dict:
        """
        执行AI分析任务
        
        Args:
            task: 任务信息字典
            
        Returns:
            Dict: 分析结果
        """
        task_id = task['id']
        tracking_number = task['tracking_number']
        raw_data_path = task.get('raw_data_path', '')
        parent_task_id = task.get('parent_task_id', '')
        
        print(f"[AI] 开始AI分析任务: {tracking_number}")
        print(f"[AI] 原始数据路径: {raw_data_path}")
        print(f"[AI] 父任务ID: {parent_task_id}")
        
        try:
            start_time = time.time()
            
            # 执行AI分析
            result = self._perform_ai_analysis(tracking_number, raw_data_path)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result and result.get('success'):
                print(f"[SUCCESS] AI分析完成: {tracking_number} (耗时: {duration:.2f}秒)")
                
                return {
                    'success': True,
                    'summary': f"{tracking_number} AI分析完成",
                    'data': result.get('data', {}),
                    'duration': duration
                }
            else:
                error_msg = result.get('error', 'AI分析失败') if result else 'AI分析失败'
                print(f"[ERROR] AI分析失败: {tracking_number} - {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'duration': duration
                }
        
        except Exception as e:
            error_msg = f"AI分析异常: {str(e)}"
            print(f"[ERROR] {error_msg}")
            traceback.print_exc()
            return {
                'success': False,
                'error': error_msg
            }
    
    def _perform_ai_analysis(self, tracking_number: str, raw_data_path: str) -> Dict:
        """
        执行AI分析
        
        Args:
            tracking_number: 跟踪号
            raw_data_path: 原始数据路径
            
        Returns:
            Dict: 分析结果
        """
        try:
            print(f"[AI] 开始分析原始数据: {raw_data_path}")
            
            # 检查原始数据路径（若不存在，尝试从文件管理器兜底获取最新路径）
            if not raw_data_path or not os.path.exists(raw_data_path):
                print(f"[WARN] 原始数据路径不存在: {raw_data_path}，尝试兜底查找...")
                try:
                    file_mgr = get_file_manager()
                    tracking_files = file_mgr.get_files_for_tracking(tracking_number)
                    if tracking_files:
                        raw_data_path = tracking_files[0]['folder_path']
                        print(f"[AI] 兜底使用最新数据文件夹: {raw_data_path}")
                    else:
                        print(f"[ERROR] 未找到任何数据文件夹: {tracking_number}")
                        return {
                            'success': False,
                            'error': f'原始数据路径不存在: {raw_data_path}'
                        }
                except Exception as e:
                    print(f"[ERROR] 兜底查找数据文件夹失败: {e}")
                    return {
                        'success': False,
                        'error': f'原始数据路径不存在且兜底失败: {raw_data_path}'
                    }

            # 寻找需要分析的HTML文件
            html_files = []
            screenshot_file = None

            for filename in os.listdir(raw_data_path):
                file_path = os.path.join(raw_data_path, filename)
                
                # 查找HTML或TXT文件用于AI分析
                if (filename.startswith('page_content_') and 
                    (filename.endswith('.html') or filename.endswith('.txt'))):
                    html_files.append(file_path)
                    if 'simplified' in filename:
                        print(f"[AI] 找到简化HTML文件: {filename}")
                    elif 'original' in filename:
                        print(f"[AI] 找到原始HTML文件: {filename}")
                
                # 记录截图文件位置（作为后备）
                elif filename == 'final_result.png':
                    screenshot_file = file_path
                    print(f"[AI] 找到截图文件（后备方案）: {filename}")
            
            if not html_files:
                print(f"[WARN] 未找到HTML内容文件进行分析，尝试使用AI结果文件兜底")
                # 兜底：如果之前抓取阶段已保存 ai_analysis_result_*.txt，直接解析该文件
                try:
                    ai_files = [f for f in os.listdir(raw_data_path) if f.startswith('ai_analysis_result_') and f.endswith('.txt')]
                    if ai_files:
                        ai_file_path = os.path.join(raw_data_path, sorted(ai_files)[-1])
                        with open(ai_file_path, 'r', encoding='utf-8') as f:
                            ai_text = f.read()
                        parsed_data = self._parse_ai_result(ai_text)
                        print(f"[AI] 兜底解析AI结果文件成功: {ai_file_path}")
                        return {
                            'success': True,
                            'data': {
                                'estimated_arrival_time': parsed_data.get('estimated_arrival_time'),
                                'estimated_arrival_port': parsed_data.get('estimated_arrival_port'),
                                'dates_data': parsed_data.get('dates_data', []),
                                'ai_result': ai_text,
                                'result_files': {
                                    'screenshot': screenshot_file,
                                    'ai_analysis': ai_file_path,
                                    'html_files': []
                                },
                                'storage_info': {
                                    'folder_path': raw_data_path,
                                    'analysis_completed': True
                                }
                            }
                        }
                except Exception as e:
                    print(f"[ERROR] 兜底解析AI结果文件失败: {e}")

                return {
                    'success': False,
                    'error': '未找到HTML内容文件进行分析'
                }

            print(f"[AI] 准备基于HTML内容进行AI分析 ({len(html_files)} 个文件)")

            # 准备分析数据
            analysis_data = {
                'tracking_number': tracking_number,
                'screenshot_path': screenshot_file or '',  # 截图作为后备方案
                'html_files': html_files,
                'data_folder': raw_data_path
            }
            
            # 调用AI分析
            print(f"[AI] 开始调用TextAnalyzer进行分析...")
            ai_result = self.text_analyzer.analyze_tracking_data(analysis_data)
            
            if ai_result and ai_result.get('success'):
                # 解析AI返回的结果
                parsed_data = self._parse_ai_result(ai_result.get('data', ''))
                
                # 生成AI分析结果文件
                result_file_path = os.path.join(raw_data_path, f'ai_analysis_result_{int(time.time())}.txt')
                with open(result_file_path, 'w', encoding='utf-8') as f:
                    f.write(ai_result.get('data', ''))
                
                print(f"[AI] AI分析结果已保存: {result_file_path}")
                
                return {
                    'success': True,
                    'data': {
                        'estimated_arrival_time': parsed_data.get('estimated_arrival_time'),
                        'estimated_arrival_port': parsed_data.get('estimated_arrival_port'),
                        'dates_data': parsed_data.get('dates_data', []),
                        'ai_result': ai_result.get('data', ''),
                        'result_files': {
                            'screenshot': screenshot_file,
                            'ai_analysis': result_file_path,
                            'html_files': html_files
                        },
                        'storage_info': {
                            'folder_path': raw_data_path,
                            'analysis_completed': True
                        }
                    }
                }
            else:
                error_msg = ai_result.get('error', 'AI分析返回失败') if ai_result else 'AI分析调用失败'
                return {
                    'success': False,
                    'error': error_msg
                }
        
        except Exception as e:
            return {
                'success': False,
                'error': f"AI分析执行异常: {str(e)}"
            }
    
    def _parse_ai_result(self, ai_result: str) -> Dict:
        """
        解析AI分析结果，提取关键信息
        
        Args:
            ai_result: AI分析原始文本结果
            
        Returns:
            Dict: 解析后的结构化数据
        """
        try:
            print(f"[AI] 开始解析AI结果，内容长度: {len(ai_result)}")
            
            data = None
            # 尝试从AI结果中提取JSON
            lower = ai_result.lower()
            start = lower.find('```json')
            if start != -1:
                end = lower.find('```', start + 7)
                if end != -1:
                    json_str = ai_result[start + 7:end].strip()
                    try:
                        data = json.loads(json_str)
                        print(f"[SUCCESS] JSON解析成功，keys: {list(data.keys())}")
                    except json.JSONDecodeError as je:
                        print(f"[ERROR] JSON解析失败: {je}")
            
            # 如果没找到JSON块，尝试查找任意大括号内容
            if data is None:
                i = ai_result.find('{')
                if i != -1:
                    depth = 0
                    j = i
                    while j < len(ai_result):
                        ch = ai_result[j]
                        if ch == '{':
                            depth += 1
                        elif ch == '}':
                            depth -= 1
                            if depth == 0:
                                try:
                                    candidate = ai_result[i:j + 1]
                                    data = json.loads(candidate)
                                    print(f"[SUCCESS] JSON解析成功（括号扫描），keys: {list(data.keys())}")
                                except Exception:
                                    pass
                                break
                        j += 1
            
            if data is None:
                print("[WARNING] 未能解析到有效JSON，返回空结果")
                return {}
            
            result = {
                'dates_data': [],
                'estimated_arrival_time': None,
                'estimated_arrival_port': None,
            }
            
            # 提取预计到港时间
            eta = data.get('estimated_arrival_time')
            if not eta:
                # 查找同义字段
                for k in ('eta', 'estimated_eta', 'estimated_arrival', 'arrival_eta', 'pod_eta', 'eta_date'):
                    if k in data and data[k]:
                        eta = data[k]
                        print(f"[DEBUG] 使用同义字段 '{k}' 作为 ETA")
                        break
            result['estimated_arrival_time'] = eta
            result['estimated_arrival_port'] = data.get('estimated_arrival_port') or data.get('pod') or data.get('port')
            
            print(f"[INFO] AI识别的预计到港时间: {result['estimated_arrival_time']}")
            print(f"[INFO] AI识别的预计到港港口: {result['estimated_arrival_port']}")
            
            # 提取时间节点数据
            dates_src = data.get('dates') or data.get('dates_data') or []
            if dates_src:
                print(f"[INFO] 找到时间节点数组，包含 {len(dates_src)} 个时间节点")
                for i, date_item in enumerate(dates_src):
                    print(f"  [INFO] 处理时间节点 {i+1}: {date_item.get('date')} - {date_item.get('type') or date_item.get('event_type')}")
                    # 转换为货运记录时间节点格式
                    date_record = {
                        'date': date_item.get('date'),
                        'location': date_item.get('location', ''),
                        'event': date_item.get('description') or date_item.get('event', ''),
                        'status': date_item.get('status', ''),
                        'vessel_info': date_item.get('vessel_info', ''),
                        'event_type': date_item.get('type') or date_item.get('event_type', ''),
                    }
                    result['dates_data'].append(date_record)
            else:
                print(f"[WARNING] JSON数据中未找到dates/dates_data字段")
            
            # 如果ETA缺失，尝试从日期节点推导
            if not result['estimated_arrival_time'] and result['dates_data']:
                derived = self._derive_eta_from_dates(result['dates_data'])
                if derived:
                    result['estimated_arrival_time'] = derived
                    print(f"[INFO] 从时间节点推导ETA: {derived}")
            
            print(f"[SUCCESS] 解析AI结果成功，提取到 {len(result['dates_data'])} 个时间节点")
            if result['estimated_arrival_time']:
                print(f"📅 预计到港时间: {result['estimated_arrival_time']}")
            
            return result
        
        except Exception as e:
            print(f"[ERROR] 解析AI结果失败: {e}")
            import traceback
            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")
            return {}
    
    def _derive_eta_from_dates(self, dates):
        """从时间节点中推导ETA"""
        try:
            if not dates:
                return None
            
            def norm(s):
                return (s or '').strip().lower()
            
            # 查找arrival + estimated的组合
            for d in dates:
                et = norm(d.get('event_type') or d.get('type'))
                st = norm(d.get('status'))
                if ('arrival' in et or 'eta' in et) and (st in ('estimated', 'estimate', '预计', '預計', 'eta')):
                    if d.get('date'):
                        return d['date']
            
            # 查找描述中包含关键词的
            keywords = ('eta', 'estimated time of arrival', '到港', '抵达', '抵達')
            for d in dates:
                desc = norm(d.get('event') or d.get('description'))
                st = norm(d.get('status'))
                if any(k in desc for k in keywords) and (st in ('estimated', 'estimate', '预计', '預計', 'eta')):
                    if d.get('date'):
                        return d['date']
            
            # 任意arrival相关的
            for d in dates:
                et = norm(d.get('event_type') or d.get('type'))
                desc = norm(d.get('event') or d.get('description'))
                if ('arrival' in et) or any(k in desc for k in ('到港', '抵达', '抵達')):
                    if d.get('date'):
                        return d['date']
            
            return None
        except Exception:
            return None
    
    def stop(self):
        """停止执行器"""
        print("[STOP] AI分析执行器已停止")


if __name__ == "__main__":
    # 测试代码
    executor = AIAnalysisExecutor()
    
    # 模拟任务
    test_task = {
        'id': 'test-ai-001',
        'tracking_number': 'MEDUJ0618622', 
        'task_type': 'bill_of_lading',
        'task_stage': 'ai_analysis',
        'task_name': 'MSC-MEDUJ0618622提单号AI分析任务',
        'parent_task_id': 'test-scraping-001',
        'raw_data_path': 'files/2025-08/MEDUJ0618622_20250821_160503'
    }
    
    print("🧪 测试AI分析执行器...")
    result = executor.execute_task(test_task)
    print(f"📋 测试结果: {json.dumps(result, indent=2, ensure_ascii=False)}")