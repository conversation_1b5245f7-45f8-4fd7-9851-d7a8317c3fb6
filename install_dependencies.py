#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Container Helper 项目依赖安装脚本
一次性安装所有需要的Python包和浏览器
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n[INFO] {description}")
    print(f"[CMD] {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(f"[OUTPUT] {result.stdout}")
        print(f"[SUCCESS] {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} 失败")
        print(f"[ERROR] 错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"[ERROR] 执行命令时出错: {e}")
        return False

def install_dependencies():
    """安装所有依赖"""
    print("=" * 60)
    print("Container Helper 项目依赖安装")
    print("=" * 60)
    
    # 更新pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "更新pip")
    
    # 核心GUI框架
    dependencies = [
        # GUI框架
        ("PySide6", "现代化Qt桌面应用框架"),
        
        # 网页自动化
        ("playwright", "自动化浏览器操作"),
        
        # AI和机器学习
        ("openai", "OpenAI兼容的AI客户端"),
        
        # 网页解析
        ("beautifulsoup4", "HTML解析库"),
        ("lxml", "XML/HTML解析器"),
        
        # 数据处理
        ("pandas", "数据分析和处理"),
        ("openpyxl", "Excel文件处理"),
        
        # 网络请求
        ("requests", "HTTP请求库"),
        ("urllib3", "HTTP客户端库"),
        
        # 图像处理
        ("Pillow", "图像处理库"),
        
        # 日期时间处理
        ("python-dateutil", "日期时间工具"),
        
        # JSON和配置
        ("pyyaml", "YAML配置文件支持"),
        
        # 日志和调试
        ("colorama", "控制台颜色输出"),
        
        # 可选的Google Cloud依赖（如果需要）
        ("google-cloud-aiplatform", "Google Cloud AI Platform（可选）"),
        ("langchain-google-vertexai", "LangChain Google Vertex AI（可选）"),
    ]
    
    print(f"\n[INFO] 准备安装 {len(dependencies)} 个Python包...")
    
    failed_packages = []
    
    for package, description in dependencies:
        success = run_command(
            f"{sys.executable} -m pip install -U {package}",
            f"安装 {package} - {description}"
        )
        if not success:
            failed_packages.append((package, description))
    
    # 安装Playwright浏览器
    print(f"\n[INFO] 安装Playwright浏览器...")
    playwright_success = run_command(
        f"{sys.executable} -m playwright install chromium",
        "安装Chromium浏览器"
    )
    
    # 安装结果总结
    print("\n" + "=" * 60)
    print("安装结果总结")
    print("=" * 60)
    
    if failed_packages:
        print(f"[WARNING] {len(failed_packages)} 个包安装失败:")
        for package, description in failed_packages:
            print(f"  - {package}: {description}")
        print(f"\n[INFO] 成功安装: {len(dependencies) - len(failed_packages)}/{len(dependencies)} 个包")
    else:
        print(f"[SUCCESS] 所有 {len(dependencies)} 个包都安装成功！")
    
    if not playwright_success:
        print(f"[WARNING] Playwright浏览器安装失败")
    else:
        print(f"[SUCCESS] Playwright浏览器安装成功")
    
    # 验证关键依赖
    print(f"\n[INFO] 验证关键依赖...")
    critical_imports = [
        ("PySide6", "PySide6.QtWidgets"),
        ("playwright", "playwright.sync_api"),
        ("openai", "openai"),
        ("bs4", "bs4"),
        ("sqlite3", "sqlite3"),  # 内置模块
    ]
    
    for package_name, import_name in critical_imports:
        try:
            __import__(import_name)
            print(f"[SUCCESS] {package_name} 导入成功")
        except ImportError as e:
            print(f"[ERROR] {package_name} 导入失败: {e}")
    
    print(f"\n[INFO] 依赖安装完成！")
    print(f"[INFO] 你现在可以运行以下命令来测试项目:")
    print(f"[INFO]   python app.py                    # 启动桌面应用")
    print(f"[INFO]   python task_executor.py          # 测试网页自动化")
    print(f"[INFO]   python debug_simple.py          # 测试数据库功能")

def main():
    """主函数"""
    try:
        install_dependencies()
    except KeyboardInterrupt:
        print(f"\n[INFO] 用户中断安装")
    except Exception as e:
        print(f"\n[ERROR] 安装过程中出现错误: {e}")
        
if __name__ == "__main__":
    main()