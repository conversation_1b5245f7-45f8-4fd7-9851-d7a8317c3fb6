#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的导入链条测试脚本
"""

import sys
import traceback

def test_import_chain():
    """测试完整的导入链条"""
    print("🔍 开始完整导入链条测试...")
    
    # 测试各个层级的导入
    tests = [
        # 第一层：基础模块
        ("shipment_manager", "ShipmentManager"),
        ("task_manager", "TaskManager"),
        ("db_logger", "log_ai_call_simple"),
        
        # 第二层：AI模块
        ("ai.client", "get_ai_client"),
        ("ai.prompt_config", "get_text_analyzer_prompt"),
        
        # 第三层：新的分析器
        ("ai.text_analyzer", "TextAnalyzer"),
        
        # 第四层：执行器
        ("scraping_executor", "ScrapingExecutor"),
        ("ai_analysis_executor", "AIAnalysisExecutor"),
        
        # 第五层：定时任务处理器
        ("scheduled_task_processor", "ScheduledTaskProcessor"),
        
        # 第六层：传统处理器
        ("task_processor", "TaskProcessor"),
    ]
    
    failed_imports = []
    success_count = 0
    
    for module, item in tests:
        try:
            print(f"  测试: {module}.{item}")
            exec(f"from {module} import {item}")
            print(f"  ✅ {module}.{item}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module}.{item}: {e}")
            failed_imports.append((module, item, str(e)))
            # 打印详细错误信息
            traceback.print_exc()
            print("-" * 50)
    
    print(f"\n📊 测试结果: {success_count}/{len(tests)} 成功")
    
    if failed_imports:
        print(f"\n❌ 发现 {len(failed_imports)} 个导入问题:")
        for module, item, error in failed_imports:
            print(f"   • {module}.{item}: {error}")
        return False
    else:
        print("\n✅ 所有导入测试通过!")
        return True

def test_scheduled_processor_creation():
    """测试定时任务处理器的创建"""
    print("\n🔧 测试定时任务处理器创建...")
    
    try:
        from scheduled_task_processor import ScheduledTaskProcessor
        
        # 创建一个简单的回调函数
        def dummy_callback(task_id, stage, result_data):
            print(f"回调测试: {task_id}, {stage}")
        
        # 尝试创建处理器
        processor = ScheduledTaskProcessor(
            scraping_interval=30,
            ai_interval=30,
            status_update_interval=60,
            max_scraping_tasks=1,
            max_ai_tasks=1,
            completion_callback=dummy_callback
        )
        
        print("✅ 定时任务处理器创建成功")
        
        # 获取状态
        status = processor.get_status()
        print(f"✅ 状态获取成功: running={status.get('running', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 定时任务处理器创建失败: {e}")
        traceback.print_exc()
        return False

def test_app_basic_imports():
    """测试应用程序基础导入"""
    print("\n📱 测试应用程序基础导入...")
    
    try:
        print("  测试PySide6...")
        from PySide6.QtWidgets import QApplication
        print("  ✅ PySide6导入成功")
        
        print("  测试应用程序核心模块...")
        import os
        import json
        from db.connection_manager import get_pool_stats
        print("  ✅ 应用程序核心模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用程序基础导入失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Container Helper 完整导入链条诊断")
    print("=" * 60)
    
    # 测试基础导入链条
    chain_success = test_import_chain()
    
    if chain_success:
        # 测试处理器创建
        processor_success = test_scheduled_processor_creation()
        
        if processor_success:
            # 测试应用程序导入
            app_success = test_app_basic_imports()
            
            if app_success:
                print(f"\n🎉 所有测试通过! 应用程序应该可以正常启动!")
            else:
                print(f"\n⚠️  基础导入成功但应用程序导入失败")
        else:
            print(f"\n⚠️  导入成功但处理器创建失败")
    else:
        print(f"\n❌ 导入链条测试失败，无法继续")
    
    print("\n💡 如果所有测试通过，请运行: python app.py")