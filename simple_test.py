#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的导入测试
"""

print("开始测试导入...")

try:
    print("1. 测试basic模块...")
    import sys
    import os
    print("✅ basic模块导入成功")
    
    print("2. 测试scheduled_task_processor...")
    from scheduled_task_processor import ScheduledTaskProcessor
    print("✅ scheduled_task_processor导入成功")
    
    print("3. 测试主app模块...")
    # 暂时跳过GUI相关的导入测试
    import importlib.util
    spec = importlib.util.spec_from_file_location("app_test", "app.py")
    
    print("✅ 所有测试通过!")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()