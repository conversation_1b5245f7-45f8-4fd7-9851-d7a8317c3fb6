// Service Worker for 船期查询工作台
const CACHE_NAME = 'shipment-query-v2.1.1';
const STATIC_CACHE = 'static-v4';
const DYNAMIC_CACHE = 'dynamic-v4';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/css/styles.css',
  '/css/components.css',
  '/manifest.json',
  'https://unpkg.com/feather-icons@4.29.0/dist/feather.min.js'
];

// 安装事件 - 缓存静态资源
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Failed to cache static assets', error);
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// 拦截网络请求
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 只处理同源请求和CDN资源
  if (url.origin === location.origin || url.hostname === 'unpkg.com') {
    event.respondWith(
      cacheFirst(request)
    );
  }
});

// 缓存优先策略
async function cacheFirst(request) {
  try {
    // 先从缓存中查找
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      console.log('Service Worker: Serving from cache', request.url);
      return cachedResponse;
    }
    
    // 缓存中没有，从网络获取
    console.log('Service Worker: Fetching from network', request.url);
    const networkResponse = await fetch(request);
    
    // 如果是成功的响应，缓存它
    if (networkResponse.status === 200) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Fetch failed', error);
    
    // 网络失败时，返回离线页面或默认响应
    if (request.destination === 'document') {
      return caches.match('/index.html');
    }
    
    // 对于其他资源，返回一个基本的错误响应
    return new Response('离线状态下无法加载此资源', {
      status: 503,
      statusText: 'Service Unavailable',
      headers: new Headers({
        'Content-Type': 'text/plain; charset=utf-8'
      })
    });
  }
}

// 监听消息事件
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'GET_VERSION':
      event.ports[0].postMessage({
        type: 'VERSION',
        version: CACHE_NAME
      });
      break;
      
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          success: true
        });
      }).catch((error) => {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          success: false,
          error: error.message
        });
      });
      break;
      
    default:
      console.log('Service Worker: Unknown message type', type);
  }
});

// 清理所有缓存
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  return Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}

// 后台同步事件（如果支持）
if ('sync' in self.registration) {
  self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync', event.tag);
    
    if (event.tag === 'background-sync') {
      event.waitUntil(
        // 这里可以执行后台同步任务
        Promise.resolve()
      );
    }
  });
}

// 推送通知事件（如果支持）
if ('push' in self.registration) {
  self.addEventListener('push', (event) => {
    console.log('Service Worker: Push received');
    
    const options = {
      body: event.data ? event.data.text() : '您有新的船期查询结果',
      icon: '/manifest.json',
      badge: '/manifest.json',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: 1
      },
      actions: [
        {
          action: 'explore',
          title: '查看详情',
          icon: '/manifest.json'
        },
        {
          action: 'close',
          title: '关闭',
          icon: '/manifest.json'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification('船期查询工作台', options)
    );
  });
  
  // 处理通知点击事件
  self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
      event.waitUntil(
        clients.openWindow('/#results')
      );
    }
  });
}

console.log('Service Worker: Script loaded');