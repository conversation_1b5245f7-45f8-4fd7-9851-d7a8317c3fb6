#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务创建工具
用于批量创建提单号/箱号查询任务
"""

import json
import csv
from datetime import datetime
from typing import List, Dict
import argparse

from task_manager import TaskManager, create_bill_of_lading_task, create_container_task
from utils.carrier_lookup import get_company_info

def create_tasks_from_list(tracking_numbers: List[str], 
                          task_type: str = "bill_of_lading",
                          creator_id: str = "system",
                          creator_name: str = "系统管理员",
                          auto_detect_carrier: bool = True) -> List[str]:
    """
    从序列号列表批量创建任务
    
    Args:
        tracking_numbers: 序列号列表
        task_type: 任务类型 ('bill_of_lading' 或 'container')
        creator_id: 创建人ID
        creator_name: 创建人姓名
        auto_detect_carrier: 是否自动检测承运人
        
    Returns:
        List[str]: 创建的任务ID列表
    """
    task_ids = []
    
    print(f"🚀 开始批量创建任务...")
    print(f"📋 任务类型: {'提单号' if task_type == 'bill_of_lading' else '箱号'}查询")
    print(f"📊 任务数量: {len(tracking_numbers)}")
    print("=" * 50)
    
    for i, tracking_number in enumerate(tracking_numbers, 1):
        try:
            carrier = None
            if auto_detect_carrier:
                try:
                    carrier_info = get_company_info(tracking_number)
                    carrier = carrier_info.get('company') if carrier_info else None
                except:
                    pass  # 忽略承运人检测失败
            
            if task_type == "bill_of_lading":
                task_id = create_bill_of_lading_task(
                    bl_number=tracking_number,
                    creator_id=creator_id,
                    creator_name=creator_name,
                    carrier=carrier
                )
            else:
                task_id = create_container_task(
                    container_number=tracking_number,
                    creator_id=creator_id,
                    creator_name=creator_name,
                    carrier=carrier
                )
            
            task_ids.append(task_id)
            print(f"✅ [{i}/{len(tracking_numbers)}] {tracking_number} -> {task_id[:8]}...")
            
        except Exception as e:
            print(f"❌ [{i}/{len(tracking_numbers)}] {tracking_number} 创建失败: {e}")
            continue
    
    print("=" * 50)
    print(f"🎉 批量创建完成: {len(task_ids)}/{len(tracking_numbers)} 个任务创建成功")
    return task_ids

def create_tasks_from_csv(csv_file: str, 
                         tracking_number_column: str = "tracking_number",
                         task_type_column: str = "task_type",
                         creator_id_column: str = "creator_id",
                         creator_name_column: str = "creator_name") -> List[str]:
    """
    从CSV文件批量创建任务
    
    Args:
        csv_file: CSV文件路径
        tracking_number_column: 序列号列名
        task_type_column: 任务类型列名
        creator_id_column: 创建人ID列名
        creator_name_column: 创建人姓名列名
        
    Returns:
        List[str]: 创建的任务ID列表
    """
    task_ids = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            print(f"📁 从CSV文件创建任务: {csv_file}")
            print(f"📋 CSV列名: {list(reader.fieldnames)}")
            
            tasks_data = []
            for row in reader:
                tracking_number = row.get(tracking_number_column, '').strip()
                if not tracking_number:
                    continue
                
                task_type = row.get(task_type_column, 'bill_of_lading').strip()
                creator_id = row.get(creator_id_column, 'csv_import').strip()
                creator_name = row.get(creator_name_column, 'CSV导入').strip()
                
                # 自动检测承运人
                carrier = None
                try:
                    carrier_info = get_company_info(tracking_number)
                    carrier = carrier_info.get('company') if carrier_info else None
                except:
                    pass
                
                tasks_data.append({
                    'tracking_number': tracking_number,
                    'task_type': task_type,
                    'creator_id': creator_id,
                    'creator_name': creator_name,
                    'carrier': carrier
                })
            
            # 批量创建任务
            manager = TaskManager()
            task_ids = manager.batch_create_tasks(tasks_data)
            
    except Exception as e:
        print(f"❌ 从CSV文件创建任务失败: {e}")
    
    return task_ids

def interactive_create_tasks():
    """
    交互式创建任务
    """
    print("🎯 交互式任务创建工具")
    print("=" * 30)
    
    # 获取任务类型
    print("\n📋 请选择任务类型:")
    print("1. 提单号查询 (bill_of_lading)")
    print("2. 箱号查询 (container)")
    
    while True:
        choice = input("请输入选择 (1/2): ").strip()
        if choice == "1":
            task_type = "bill_of_lading"
            type_name = "提单号"
            break
        elif choice == "2":
            task_type = "container"
            type_name = "箱号"
            break
        else:
            print("❌ 无效选择，请重新输入")
    
    # 获取创建人信息
    creator_id = input("\n👤 请输入创建人ID (默认: interactive_user): ").strip() or "interactive_user"
    creator_name = input("👤 请输入创建人姓名 (默认: 交互用户): ").strip() or "交互用户"
    
    # 获取序列号
    print(f"\n📝 请输入{type_name}列表 (每行一个，输入空行结束):")
    tracking_numbers = []
    
    while True:
        number = input(f"{type_name} #{len(tracking_numbers)+1}: ").strip()
        if not number:
            break
        tracking_numbers.append(number)
    
    if not tracking_numbers:
        print("❌ 未输入任何序列号")
        return
    
    print(f"\n📊 将创建 {len(tracking_numbers)} 个{type_name}查询任务")
    confirm = input("确认创建? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        task_ids = create_tasks_from_list(
            tracking_numbers=tracking_numbers,
            task_type=task_type,
            creator_id=creator_id,
            creator_name=creator_name
        )
        
        if task_ids:
            print(f"\n🎉 成功创建 {len(task_ids)} 个任务!")
            print("\n📋 任务ID列表:")
            for i, task_id in enumerate(task_ids, 1):
                print(f"  {i}. {task_id}")
    else:
        print("❌ 已取消创建")

def show_task_queue_status():
    """
    显示任务队列状态
    """
    manager = TaskManager()
    
    print("📊 任务队列状态")
    print("=" * 30)
    
    # 获取统计信息
    stats = manager.get_task_statistics()
    
    print(f"📈 总任务数: {stats.get('total_tasks', 0)}")
    print(f"📅 今日创建: {stats.get('today_created', 0)}")
    print(f"✅ 今日完成: {stats.get('today_completed', 0)}")
    
    print("\n📋 状态分布:")
    status_names = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'cancelled': '已取消'
    }
    
    for status, count in stats.get('status_distribution', {}).items():
        status_name = status_names.get(status, status)
        print(f"  {status_name}: {count}")
    
    # 显示待处理任务
    pending_tasks = manager.get_pending_tasks(limit=10)
    if pending_tasks:
        print(f"\n⏳ 待处理任务 (前10个):")
        for i, task in enumerate(pending_tasks, 1):
            print(f"  {i}. {task['task_name']} (优先级: {task['priority']})")
    else:
        print("\n✨ 暂无待处理任务")

def main():
    parser = argparse.ArgumentParser(description="任务创建工具")
    parser.add_argument('--mode', choices=['interactive', 'csv', 'list', 'status'], 
                       default='interactive', help='运行模式')
    parser.add_argument('--csv-file', help='CSV文件路径')
    parser.add_argument('--tracking-numbers', nargs='+', help='序列号列表')
    parser.add_argument('--task-type', choices=['bill_of_lading', 'container'], 
                       default='bill_of_lading', help='任务类型')
    parser.add_argument('--creator-id', default='cli_user', help='创建人ID')
    parser.add_argument('--creator-name', default='命令行用户', help='创建人姓名')
    
    args = parser.parse_args()
    
    if args.mode == 'interactive':
        interactive_create_tasks()
    elif args.mode == 'csv':
        if not args.csv_file:
            print("❌ CSV模式需要指定 --csv-file 参数")
            return
        create_tasks_from_csv(args.csv_file)
    elif args.mode == 'list':
        if not args.tracking_numbers:
            print("❌ 列表模式需要指定 --tracking-numbers 参数")
            return
        create_tasks_from_list(
            tracking_numbers=args.tracking_numbers,
            task_type=args.task_type,
            creator_id=args.creator_id,
            creator_name=args.creator_name
        )
    elif args.mode == 'status':
        show_task_queue_status()

if __name__ == "__main__":
    main()