"""
Browser Pool Manager

This module manages a pool of Selenium WebDriver instances to be reused by scraping tasks.
This avoids the overhead of creating a new browser instance for every task.
"""
import queue
import threading
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from config import Config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class BrowserPool:
    """
    Manages a pool of reusable Selenium WebDriver instances.
    """
    def __init__(self, pool_size=Config.BROWSER_POOL_SIZE):
        """
        Initializes the browser pool.
        :param pool_size: The number of browsers to keep in the pool.
        """
        if pool_size <= 0:
            raise ValueError("Pool size must be greater than 0.")
        
        self.pool_size = pool_size
        self._pool = queue.Queue(maxsize=pool_size)
        self._lock = threading.Lock()
        self._initialized = False
        self.initialize_pool()

    def initialize_pool(self):
        """
        Fills the pool with new browser instances.
        """
        with self._lock:
            if self._initialized:
                return
            
            logging.info(f"Initializing browser pool with {self.pool_size} instances...")
            for _ in range(self.pool_size):
                try:
                    options = webdriver.ChromeOptions()
                    if Config.HEADLESS_BROWSER:
                        options.add_argument('--headless')
                        options.add_argument('--disable-gpu')
                    
                    # Add other common options
                    options.add_argument('--no-sandbox')
                    options.add_argument('--disable-dev-shm-usage')
                    options.add_argument("start-maximized")
                    options.add_argument("disable-infobars")
                    options.add_argument("--disable-extensions")

                    service = ChromeService(ChromeDriverManager().install())
                    driver = webdriver.Chrome(service=service, options=options)
                    self._pool.put(driver)
                except Exception as e:
                    logging.error(f"Failed to create a browser instance: {e}")
            
            if not self._pool.empty():
                self._initialized = True
                logging.info("Browser pool initialized successfully.")
            else:
                logging.error("Browser pool initialization failed. No browsers were created.")


    def get_browser(self):
        """
        Gets a browser instance from the pool. Blocks until one is available.
        """
        if not self._initialized:
            logging.warning("Browser pool not initialized. Call initialize_pool() first.")
            # Or decide to auto-initialize
            self.initialize_pool()
            if not self._initialized:
                raise Exception("Failed to get browser: pool is not initialized.")

        logging.info("Requesting browser from pool...")
        browser = self._pool.get()
        logging.info("Browser acquired from pool.")
        return browser

    def release_browser(self, browser):
        """
        Releases a browser instance back to the pool.
        """
        if browser:
            try:
                # Reset the browser state for the next user
                browser.delete_all_cookies()
                browser.get('about:blank')
            except Exception as e:
                logging.warning(f"Could not reset browser state: {e}. Recreating instance.")
                # If browser is in a bad state, close it and create a new one
                try:
                    browser.quit()
                except Exception as quit_e:
                    logging.error(f"Error quitting problematic browser: {quit_e}")
                
                # Create a new one to keep the pool full
                try:
                    options = webdriver.ChromeOptions()
                    if Config.HEADLESS_BROWSER:
                        options.add_argument('--headless')
                    options.add_argument('--no-sandbox')
                    service = ChromeService(ChromeDriverManager().install())
                    browser = webdriver.Chrome(service=service, options=options)
                except Exception as create_e:
                    logging.error(f"Failed to create replacement browser: {create_e}")
                    # If creation fails, we can't put anything back. The pool size will decrease.
                    # A more robust implementation might have a mechanism to restore pool size.
                    return 

            self._pool.put(browser)
            logging.info("Browser released back to pool.")

    def close_all(self):
        """
        Closes all browser instances in the pool.
        """
        logging.info("Closing all browsers in the pool...")
        with self._lock:
            while not self._pool.empty():
                browser = self._pool.get_nowait()
                try:
                    browser.quit()
                except Exception as e:
                    logging.error(f"Error closing a browser: {e}")
            self._initialized = False
        logging.info("Browser pool closed.")

# A global instance of the browser pool to be used across the application
browser_pool = BrowserPool()

# Ensure browsers are closed on application exit
import atexit
atexit.register(browser_pool.close_all)
