#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试 handle_task_completion 方法
模拟AI返回结果，验证 shipment_dates 插入是否正常
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from shipment_manager import ShipmentManager
from task_manager import TaskManager

def test_handle_task_completion():
    """直接测试 handle_task_completion 方法"""
    print("=== 直接测试 handle_task_completion 方法 ===")
    
    try:
        shipment_manager = ShipmentManager()
        task_manager = TaskManager()
        
        # 1. 获取一个有关联货运记录的pending任务
        pending_tasks = task_manager.get_pending_tasks(limit=5)
        
        if not pending_tasks:
            print("[ERROR] 没有待处理的任务")
            return False
        
        # 找一个有货运记录ID的任务
        test_task = None
        for task in pending_tasks:
            if '货运记录ID:' in task.get('remarks', ''):
                test_task = task
                break
        
        if not test_task:
            print("[ERROR] 没有找到有关联货运记录的任务")
            return False
        
        task_id = test_task['id']
        record_id = test_task['remarks'].split('货运记录ID:')[1].split(',')[0].strip()
        
        print(f"[INFO] 选择任务: {test_task['task_name']}")
        print(f"[INFO] 任务ID: {task_id}")
        print(f"[INFO] 关联货运记录ID: {record_id}")
        
        # 2. 构造模拟的AI结果数据
        mock_result_data = {
            'estimated_arrival_time': '2025-08-15T10:00:00',
            'estimated_arrival_port': '洛杉矶港',
            'dates_data': [
                {
                    'date': '2025-08-08 09:00:00',
                    'location': '深圳盐田港',
                    'event': '装船完成',
                    'status': '已完成',
                    'event_type': 'departure',
                    'vessel_info': {'name': 'MSC TEST VESSEL', 'voyage': 'TEST123'}
                },
                {
                    'date': '2025-08-12 14:30:00',
                    'location': '新加坡港',
                    'event': '中转停靠',
                    'status': '已完成',
                    'event_type': 'transit',
                    'vessel_info': {'name': 'MSC TEST VESSEL', 'voyage': 'TEST123'}
                },
                {
                    'date': '2025-08-15 10:00:00',
                    'location': '洛杉矶港',
                    'event': '预计到港',
                    'status': '预计',
                    'event_type': 'arrival',
                    'vessel_info': {'name': 'MSC TEST VESSEL', 'voyage': 'TEST123'}
                }
            ],
            'ai_result': '模拟AI分析结果',
            'result_files': {}
        }
        
        print(f"[INFO] 构造模拟数据: {len(mock_result_data['dates_data'])} 条时间节点")
        for i, date_item in enumerate(mock_result_data['dates_data']):
            print(f"  {i+1}. {date_item['date']} @ {date_item['location']}: {date_item['event']}")
        
        # 3. 检查处理前的状态
        print(f"\n[BEFORE] 处理前状态检查...")
        import sqlite3
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (record_id,))
        before_count = cursor.fetchone()[0]
        print(f"[BEFORE] 货运记录 {record_id} 的dates数量: {before_count}")
        
        cursor.execute("SELECT status, updated_at FROM shipment_records WHERE id = ?", (record_id,))
        record_info = cursor.fetchone()
        if record_info:
            print(f"[BEFORE] 货运记录状态: {record_info[0]}, 更新时间: {record_info[1]}")
        
        conn.close()
        
        # 4. 调用 handle_task_completion
        print(f"\n[PROCESS] 调用 handle_task_completion...")
        success = shipment_manager.handle_task_completion(task_id, mock_result_data)
        
        if success:
            print(f"[SUCCESS] handle_task_completion 执行成功!")
            
            # 5. 检查处理后的状态
            print(f"\n[AFTER] 处理后状态检查...")
            conn = sqlite3.connect('db/shipment_records.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (record_id,))
            after_count = cursor.fetchone()[0]
            print(f"[AFTER] 货运记录 {record_id} 的dates数量: {after_count}")
            
            cursor.execute("SELECT status, updated_at, estimated_arrival_time FROM shipment_records WHERE id = ?", (record_id,))
            updated_info = cursor.fetchone()
            if updated_info:
                print(f"[AFTER] 货运记录状态: {updated_info[0]}")
                print(f"[AFTER] 更新时间: {updated_info[1]}")
                print(f"[AFTER] 预计到港时间: {updated_info[2]}")
            
            # 检查新插入的dates详情
            if after_count > before_count:
                print(f"\n[DETAILS] 新插入的dates记录详情:")
                cursor.execute("""
                    SELECT date, location, description, created_at 
                    FROM shipment_dates 
                    WHERE shipment_id = ? 
                    ORDER BY id DESC 
                    LIMIT ?
                """, (record_id, after_count - before_count))
                
                new_dates = cursor.fetchall()
                for date_record in new_dates:
                    print(f"  - {date_record[0]} @ {date_record[1]}: {date_record[2]}")
                    print(f"    创建时间: {date_record[3]}")
                    
                    if '+08:00' in str(date_record[3]):
                        print(f"    [OK] 正确使用东八区时间")
                    else:
                        print(f"    [WARNING] 时间格式问题: {date_record[3]}")
            
            conn.close()
            
            # 检查任务状态
            updated_task = task_manager.get_task_by_id(task_id)
            print(f"\n[TASK] 任务状态: {updated_task.get('status', 'Unknown')}")
            
            return True
        else:
            print(f"[ERROR] handle_task_completion 执行失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("直接测试 handle_task_completion 方法")
    print("=" * 70)
    
    success = test_handle_task_completion()
    
    if success:
        print("\n" + "=" * 70)
        print("[PERFECT] handle_task_completion 方法工作正常!")
        print("问题不在业务逻辑，而在于:")
        print("1. TaskProcessor 没有运行")
        print("2. 或者AI没有返回有效的dates_data")
        print("3. 或者任务执行器有问题")
    else:
        print("\n" + "=" * 70)
        print("[ERROR] handle_task_completion 方法有问题")
        print("需要修复业务逻辑")
    
    print("=" * 70)