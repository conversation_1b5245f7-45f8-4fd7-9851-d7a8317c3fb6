# 数据库文件夹

这个文件夹包含了项目的所有数据库文件和相关的初始化脚本。

## 数据库文件

- `ai_call_logs.db` - AI调用日志数据库
- `shipment_records.db` - 货运记录数据库
- `task_queue.db` - 任务队列数据库

## 初始化脚本

- `init_database.py` - 初始化AI调用日志数据库
- `init_shipment_database.py` - 初始化货运记录数据库
- `init_task_queue.py` - 初始化任务队列数据库

## 使用说明

在首次运行项目之前，请先运行相应的初始化脚本来创建数据库表结构：

```bash
# 进入db文件夹
cd db

# 初始化各个数据库
python init_database.py
python init_shipment_database.py
python init_task_queue.py
```

所有的数据库文件都存储在这个文件夹中，便于统一管理和备份。