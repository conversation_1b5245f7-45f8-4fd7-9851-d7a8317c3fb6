#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI调用日志数据库操作类
用于记录和查询AI调用的详细信息
"""

import sqlite3
import os
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
import json

# 定义东八区时区
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time() -> datetime:
    """获取东八区（北京时间）当前时间"""
    return datetime.now(BEIJING_TZ)

def get_beijing_time_str() -> str:
    """获取东八区时间的ISO格式字符串"""
    return get_beijing_time().isoformat()

class AICallLogger:
    """AI调用日志记录器"""
    
    def __init__(self, db_path: str = 'db/ai_call_logs.db'):
        self.db_path = db_path
        self._ensure_database_exists()
    
    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        print(f"[DB_LOG] 正在连接AI日志数据库: {self.db_path}")
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        print(f"[DB_LOG] AI日志数据库连接成功: {self.db_path}")
        return conn
    
    def _ensure_database_exists(self):
        """确保数据库文件存在"""
        if not os.path.exists(self.db_path):
            print(f"警告: 数据库文件 {self.db_path} 不存在，请先运行 db/init_database.py 创建数据库")
    
    def log_ai_call(
        self,
        model: str,
        content_type: str,
        caller_id: str,
        request_time: datetime,
        response_time: datetime,
        prompt_tokens: int,
        completion_tokens: int,
        total_tokens: int,
        business_id: Optional[str] = None,
        business_type: Optional[str] = None,
        request_content_length: Optional[int] = None,
        response_content_length: Optional[int] = None,
        status: str = 'success',
        error_message: Optional[str] = None,
        cost_amount: Optional[float] = None,
        currency: str = 'USD',
        remarks: Optional[str] = None
    ) -> int:
        """记录AI调用日志
        
        Returns:
            int: 插入记录的ID
        """
        duration_seconds = (response_time - request_time).total_seconds()
        
        # 确保使用东八区时间
        beijing_time = get_beijing_time_str()
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            insert_sql = """
            INSERT INTO ai_call_logs (
                model, content_type, caller_id, request_time, response_time, 
                duration_seconds, prompt_tokens, completion_tokens, total_tokens,
                business_id, business_type, request_content_length, response_content_length,
                status, error_message, cost_amount, currency, remarks, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                model, content_type, caller_id, 
                request_time.isoformat(), response_time.isoformat(),
                duration_seconds, prompt_tokens, completion_tokens, total_tokens,
                business_id, business_type, request_content_length, response_content_length,
                status, error_message, cost_amount, currency, remarks,
                beijing_time, beijing_time  # 使用东八区时间作为创建和更新时间
            )
            
            print(f"[DB_LOG] 执行SQL - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] SQL语句: {insert_sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            print(f"[DB_LOG] 使用东八区时间: {beijing_time}")
            
            cursor.execute(insert_sql, params)
            record_id = cursor.lastrowid
            print(f"[DB_LOG] 执行结果: 创建AI调用日志ID {record_id}")
            
            conn.commit()
            print(f"[DB_LOG] 事务提交成功 - 库:ai_call_logs.db 表:ai_call_logs")
            
            print(f"AI调用日志已记录 (ID: {record_id})")
            print(f"- 模型: {model}")
            print(f"- 内容类型: {content_type}")
            print(f"- 耗时: {duration_seconds:.3f}秒")
            print(f"- Tokens: {prompt_tokens} + {completion_tokens} = {total_tokens}")
            print(f"- 记录时间(东八区): {beijing_time}")
            
            return record_id
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:ai_call_logs.db")
            raise
        finally:
            conn.close()
            print(f"[DB_LOG] AI日志数据库连接已关闭: {self.db_path}")
    
    def get_call_stats(self, caller_id: Optional[str] = None, days: int = 30) -> Dict[str, Any]:
        """获取调用统计信息
        
        Args:
            caller_id: 调用方ID，None表示所有调用方
            days: 统计最近多少天的数据
        
        Returns:
            Dict: 统计信息
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            where_clause = "WHERE request_time >= datetime('now', '-{} days')".format(days)
            if caller_id:
                where_clause += f" AND caller_id = '{caller_id}'"
            
            print(f"[DB_LOG] 执行SQL - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] 查询AI统计信息 - 调用方: {caller_id or 'ALL'}, 天数: {days}")
        
            # 基本统计
            stats_sql = f"""
            SELECT 
                COUNT(*) as total_calls,
                SUM(total_tokens) as total_tokens,
                SUM(prompt_tokens) as total_prompt_tokens,
                SUM(completion_tokens) as total_completion_tokens,
                AVG(duration_seconds) as avg_duration,
                SUM(cost_amount) as total_cost,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
                COUNT(CASE WHEN status = 'error' THEN 1 END) as error_calls
            FROM ai_call_logs {where_clause}
            """
            
            print(f"[DB_LOG] SQL语句(基本统计): {stats_sql.strip()}")
            cursor.execute(stats_sql)
            basic_stats = cursor.fetchone()
            print(f"[DB_LOG] 基本统计查询完成")
        
            # 按模型统计
            model_stats_sql = f"""
            SELECT 
                model,
                COUNT(*) as calls,
                SUM(total_tokens) as tokens,
                SUM(cost_amount) as cost
            FROM ai_call_logs {where_clause}
            GROUP BY model
            ORDER BY calls DESC
            """
            
            print(f"[DB_LOG] SQL语句(按模型统计): {model_stats_sql.strip()}")
            cursor.execute(model_stats_sql)
            model_stats = cursor.fetchall()
            print(f"[DB_LOG] 模型统计查询完成")
        
            # 按内容类型统计
            content_type_stats_sql = f"""
            SELECT 
                content_type,
                COUNT(*) as calls,
                SUM(total_tokens) as tokens
            FROM ai_call_logs {where_clause}
            GROUP BY content_type
            ORDER BY calls DESC
            """
            
            print(f"[DB_LOG] SQL语句(按内容类型统计): {content_type_stats_sql.strip()}")
            cursor.execute(content_type_stats_sql)
            content_type_stats = cursor.fetchall()
            print(f"[DB_LOG] 内容类型统计查询完成")
            
            print(f"[DB_LOG] AI统计查询成功 - 库:ai_call_logs.db 表:ai_call_logs")
            
            return {
                'basic': {
                    'total_calls': basic_stats[0] or 0,
                    'total_tokens': basic_stats[1] or 0,
                    'total_prompt_tokens': basic_stats[2] or 0,
                    'total_completion_tokens': basic_stats[3] or 0,
                    'avg_duration': round(basic_stats[4] or 0, 3),
                    'total_cost': basic_stats[5] or 0,
                    'success_calls': basic_stats[6] or 0,
                    'error_calls': basic_stats[7] or 0,
                    'success_rate': round((basic_stats[6] or 0) / max(basic_stats[0] or 1, 1) * 100, 2)
                },
                'by_model': [{
                    'model': row[0],
                    'calls': row[1],
                    'tokens': row[2],
                    'cost': row[3]
                } for row in model_stats],
                'by_content_type': [{
                    'content_type': row[0],
                    'calls': row[1],
                    'tokens': row[2]
                } for row in content_type_stats]
            }
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            raise
        finally:
            conn.close()
            print(f"[DB_LOG] AI日志数据库连接已关闭: {self.db_path}")
    
    def get_recent_calls(self, limit: int = 10, caller_id: Optional[str] = None) -> list:
        """获取最近的调用记录"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            where_clause = ""
            if caller_id:
                where_clause = f"WHERE caller_id = '{caller_id}'"
            
            sql = f"""
            SELECT 
                id, model, content_type, caller_id, request_time, 
                duration_seconds, total_tokens, status, business_id
            FROM ai_call_logs {where_clause}
            ORDER BY request_time DESC
            LIMIT {limit}
            """
            
            print(f"[DB_LOG] 执行SQL - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] SQL语句(最近调用): {sql.strip()}")
            print(f"[DB_LOG] 查询参数: limit={limit}, caller_id={caller_id}")
            
            cursor.execute(sql)
            records = cursor.fetchall()
            print(f"[DB_LOG] 查询结果: 返回 {len(records)} 条AI调用记录")
            print(f"[DB_LOG] AI调用查询成功 - 库:ai_call_logs.db 表:ai_call_logs")
            
            return [{
                'id': row[0],
                'model': row[1],
                'content_type': row[2],
                'caller_id': row[3],
                'request_time': row[4],
                'duration_seconds': row[5],
                'total_tokens': row[6],
                'status': row[7],
                'business_id': row[8]
            } for row in records]
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            raise
        finally:
            conn.close()
            print(f"[DB_LOG] AI日志数据库连接已关闭: {self.db_path}")
    
    def get_distinct_models(self):
        """获取数据库中所有不重复的模型名称"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            sql = """
            SELECT DISTINCT model
            FROM ai_call_logs
            WHERE model IS NOT NULL AND model != ''
            ORDER BY model ASC
            """
            
            print(f"[DB_LOG] 执行SQL - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] SQL语句(获取不重复模型): {sql.strip()}")
            
            cursor.execute(sql)
            records = cursor.fetchall()
            models = [row[0] for row in records]
            
            print(f"[DB_LOG] 查询结果: 返回 {len(models)} 个不重复模型")
            print(f"[DB_LOG] 模型列表: {models}")
            
            return models
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            return []
        finally:
            conn.close()
            print(f"[DB_LOG] AI日志数据库连接已关闭: {self.db_path}")

    def get_all_logs(self, start_date=None, end_date=None, model_name=None, limit=1000):
        """获取所有日志记录
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            model_name: 模型名称过滤
            limit: 限制返回数量
        
        Returns:
            List[Dict]: 日志记录列表
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            where_conditions = []
            params = []
            
            if start_date:
                where_conditions.append("DATE(request_time) >= ?")
                params.append(start_date.strftime('%Y-%m-%d'))
            
            if end_date:
                where_conditions.append("DATE(request_time) <= ?")
                params.append(end_date.strftime('%Y-%m-%d'))
            
            if model_name:
                where_conditions.append("model = ?")
                params.append(model_name)
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            sql = f"""
            SELECT 
                id, model, content_type, caller_id, request_time, response_time,
                duration_seconds, prompt_tokens, completion_tokens, total_tokens,
                business_id, business_type, status, error_message, remarks,
                created_at
            FROM ai_call_logs {where_clause}
            ORDER BY request_time DESC
            LIMIT ?
            """
            
            params.append(limit)
            
            print(f"[DB_LOG] 执行SQL - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] SQL语句(获取所有日志): {sql.strip()}")
            print(f"[DB_LOG] 查询参数: {params}")
            
            cursor.execute(sql, params)
            records = cursor.fetchall()
            print(f"[DB_LOG] 查询结果: 返回 {len(records)} 条AI调用记录")
            
            result = []
            for row in records:
                # 计算图片数量（简单判断）
                image_count = 1 if row[2] == 'vision' else 0
                
                result.append({
                    'id': row[0],
                    'model_name': row[1],
                    'content_type': row[2],
                    'caller_id': row[3],
                    'timestamp': row[4],
                    'response_time': row[5],
                    'duration_seconds': row[6],
                    'input_tokens': row[7],  # prompt_tokens from database
                    'output_tokens': row[8],  # completion_tokens from database
                    'prompt_tokens': row[7],  # 添加prompt_tokens字段
                    'completion_tokens': row[8],  # 添加completion_tokens字段
                    'total_tokens': row[9],
                    'business_id': row[10],
                    'business_type': row[11],
                    'success': row[12] == 'success',
                    'error_message': row[13],
                    'prompt': row[14] or '',
                    'response': '',  # 暂时为空，可以后续扩展
                    'image_count': image_count,
                    'created_at': row[15]
                })
            
            return result
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:ai_call_logs.db 表:ai_call_logs")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            raise
        finally:
            conn.close()
            print(f"[DB_LOG] AI日志数据库连接已关闭: {self.db_path}")

# 全局日志记录器实例
logger = AICallLogger()

def log_ai_call_simple(
    model: str,
    request_time: datetime,
    response_time: datetime,
    prompt_tokens: int,
    completion_tokens: int,
    total_tokens: int,
    business_id: Optional[str] = None,
    caller_id: str = 'system',
    content_type: str = 'text',
    status: str = 'success',
    error_message: Optional[str] = None
) -> int:
    """简化的AI调用日志记录函数"""
    return logger.log_ai_call(
        model=model,
        content_type=content_type,
        caller_id=caller_id,
        request_time=request_time,
        response_time=response_time,
        prompt_tokens=prompt_tokens,
        completion_tokens=completion_tokens,
        total_tokens=total_tokens,
        business_id=business_id,
        business_type='logistics_tracking',
        status=status,
        error_message=error_message
    )