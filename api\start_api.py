#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务启动脚本
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """启动API服务"""
    
    # 检查数据库文件是否存在
    db_files = [
        "db/task_queue.db",
        "db/carriers.db",
        "db/shipment_records.db",
        "db/ai_config.db"
    ]
    
    missing_files = []
    for db_file in db_files:
        if not os.path.exists(db_file):
            missing_files.append(db_file)
    
    if missing_files:
        print("错误: 以下数据库文件不存在:")
        for file in missing_files:
            print(f"  - {file}")
        print("\n请先运行数据库初始化脚本:")
        print("  python db/init_database.py")
        print("  python db/init_carrier_database.py")
        print("  python db/init_task_queue.py")
        print("  python db/init_ai_config.py")
        sys.exit(1)
    
    # 配置参数
    config = {
        "app": "api.main:app",
        "host": "127.0.0.1",  # 改为localhost，避免0.0.0.0在Windows下的访问问题
        "port": 8080,
        "reload": True,
        "log_level": "info",
        "access_log": True,
        "workers": 1  # 开发环境使用单进程
    }
    
    # 从环境变量读取配置
    config["host"] = os.getenv("API_HOST", config["host"])
    config["port"] = int(os.getenv("API_PORT", config["port"]))
    config["log_level"] = os.getenv("LOG_LEVEL", config["log_level"]).lower()
    config["reload"] = os.getenv("API_RELOAD", "true").lower() == "true"
    
    print(f"启动船期查询API服务...")
    print(f"地址: http://{config['host']}:{config['port']}")
    print(f"API文档: http://{config['host']}:{config['port']}/docs")
    print(f"日志级别: {config['log_level']}")
    print(f"自动重载: {config['reload']}")
    print("\n按 Ctrl+C 停止服务")
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动服务失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()