<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>E船期查询 - 登录</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/lucide@latest"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    html.dark { color-scheme: dark; }
    html { font-size: 12px; }
    body { font-family: 'Inter','Helvetica Neue','Arial','sans-serif'; }
    .glass-morphism{background:rgba(255,255,255,.1);backdrop-filter:blur(20px);-webkit-backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,.2);box-shadow:0 8px 32px 0 rgba(31,38,135,.37)}
    .animate-shimmer{animation:shimmer 2s ease-in-out infinite}
    @keyframes shimmer{0%{transform:translateX(-100%)}100%{transform:translateX(100%)}}
    /* 海浪涟漪效果 */
    .wave-container {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .wave {
      position: absolute;
      bottom: -50px;
      left: -50%;
      width: 200%;
      height: 150px;
      background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
      border-radius: 50%;
      animation: wave-flow 12s ease-in-out infinite;
    }

    .wave1 {
      animation-delay: 0s;
      opacity: 0.6;
      animation-duration: 12s;
    }

    .wave2 {
      animation-delay: -4s;
      opacity: 0.4;
      background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.2), transparent);
      animation-duration: 15s;
    }

    .wave3 {
      animation-delay: -8s;
      opacity: 0.3;
      background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.15), transparent);
      animation-duration: 18s;
    }

    @keyframes wave-flow {
      0% {
        transform: translateX(-100%) translateY(0px) scaleY(1);
      }
      25% {
        transform: translateX(-25%) translateY(-10px) scaleY(1.1);
      }
      50% {
        transform: translateX(50%) translateY(0px) scaleY(1);
      }
      75% {
        transform: translateX(125%) translateY(-5px) scaleY(0.9);
      }
      100% {
        transform: translateX(200%) translateY(0px) scaleY(1);
      }
    }

    /* 涟漪效果 */
    .wave-container::before {
      content: '';
      position: absolute;
      top: 20%;
      left: 30%;
      width: 100px;
      height: 100px;
      border: 2px solid rgba(59, 130, 246, 0.3);
      border-radius: 50%;
      animation: ripple 4s ease-out infinite;
    }

    .wave-container::after {
      content: '';
      position: absolute;
      top: 60%;
      right: 25%;
      width: 80px;
      height: 80px;
      border: 2px solid rgba(6, 182, 212, 0.2);
      border-radius: 50%;
      animation: ripple 6s ease-out infinite 2s;
    }

    @keyframes ripple {
      0% {
        transform: scale(0);
        opacity: 1;
      }
      100% {
        transform: scale(4);
        opacity: 0;
      }
    }

    /* 雪花效果 */
    .snowflakes {
      pointer-events: none;
    }

    .snowflake {
      position: absolute;
      color: rgba(255, 255, 255, 0.8);
      font-size: 1rem;
      animation: snowfall 8s linear infinite;
    }

    .snowflake:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 8s; }
    .snowflake:nth-child(2) { left: 20%; animation-delay: 1s; animation-duration: 9s; }
    .snowflake:nth-child(3) { left: 30%; animation-delay: 2s; animation-duration: 7s; }
    .snowflake:nth-child(4) { left: 40%; animation-delay: 3s; animation-duration: 10s; }
    .snowflake:nth-child(5) { left: 50%; animation-delay: 4s; animation-duration: 8s; }
    .snowflake:nth-child(6) { left: 60%; animation-delay: 5s; animation-duration: 9s; }
    .snowflake:nth-child(7) { left: 70%; animation-delay: 6s; animation-duration: 7s; }
    .snowflake:nth-child(8) { left: 80%; animation-delay: 7s; animation-duration: 8s; }
    .snowflake:nth-child(9) { left: 90%; animation-delay: 8s; animation-duration: 9s; }
    .snowflake:nth-child(10) { left: 15%; animation-delay: 9s; animation-duration: 10s; }

    @keyframes snowfall {
      0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
      }
    }

    /* 船只环绕动画 */
    .ship-orbit {
      pointer-events: none;
    }

    .ship {
      position: absolute;
      font-size: 1.5rem;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }

    .ship1 {
      animation: orbit1 20s linear infinite;
    }

    .ship2 {
      animation: orbit2 25s linear infinite;
    }

    .ship3 {
      animation: orbit3 30s linear infinite;
    }

    @keyframes orbit1 {
      0% {
        transform: translate(50vw, 20vh) rotate(0deg) translateX(200px) rotate(0deg);
      }
      100% {
        transform: translate(50vw, 20vh) rotate(360deg) translateX(200px) rotate(-360deg);
      }
    }

    @keyframes orbit2 {
      0% {
        transform: translate(50vw, 80vh) rotate(0deg) translateX(250px) rotate(0deg);
      }
      100% {
        transform: translate(50vw, 80vh) rotate(-360deg) translateX(250px) rotate(360deg);
      }
    }

    @keyframes orbit3 {
      0% {
        transform: translate(50vw, 50vh) rotate(0deg) translateX(300px) rotate(0deg);
      }
      100% {
        transform: translate(50vw, 50vh) rotate(360deg) translateX(300px) rotate(-360deg);
      }
    }

    /* 毛玻璃效果增强 */
    .glass-morphism::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
      border-radius: inherit;
      pointer-events: none;
    }

    /* 闪光效果增强 */
    @keyframes shimmer-slow {
      0% {
        transform: translateX(-100%);
      }
      100% {
        transform: translateX(100%);
      }
    }

    .animate-shimmer-slow {
      animation: shimmer-slow 3s ease-in-out infinite;
    }

  </style>
</head>
<body class="bg-slate-900 text-slate-200 min-h-screen flex items-center justify-center">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-b from-blue-900 via-blue-800 to-blue-900"></div>
    <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"%23ffffff\" stroke-width=\"0.5\" opacity=\"0.3\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/><circle cx=\"20\" cy=\"30\" r=\"2\" fill=\"%23ffffff\" opacity=\"0.4\"/><circle cx=\"70\" cy=\"60\" r=\"1.5\" fill=\"%23ffffff\" opacity=\"0.3\"/><circle cx=\"40\" cy=\"80\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.5\"/><path d=\"M10,20 Q30,10 50,25 T90,30\" stroke=\"%23ffffff\" stroke-width=\"1\" fill=\"none\" opacity=\"0.2\"/><path d=\"M5,70 Q25,60 45,75 T85,80\" stroke=\"%23ffffff\" stroke-width=\"0.8\" fill=\"none\" opacity=\"0.15\"/></svg></div>

    <!-- 波浪动画背景 -->
    <div class="absolute inset-0">
      <div class="wave-container">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
      </div>
    </div>

    <!-- 雪花效果 -->
    <div class="snowflakes absolute inset-0">
      <div class="snowflake">❄</div>
      <div class="snowflake">❅</div>
      <div class="snowflake">❆</div>
      <div class="snowflake">❄</div>
      <div class="snowflake">❅</div>
      <div class="snowflake">❆</div>
      <div class="snowflake">❄</div>
      <div class="snowflake">❅</div>
      <div class="snowflake">❆</div>
      <div class="snowflake">❄</div>
    </div>

    <!-- 船只环绕动画 -->
    <div class="ship-orbit absolute inset-0">
      <div class="ship ship1">🚢</div>
      <div class="ship ship2">⛵</div>
      <div class="ship ship3">🛥️</div>
    </div>
  </div>

  <!-- 登录卡片 -->
  <div class="relative z-10 bg-white/10 glass-morphism rounded-2xl shadow-2xl p-8 w-full max-w-md mx-4 border border-white/30">
    <div class="text-center mb-8">
      <div class="w-20 h-20 bg-gradient-to-br from-blue-500 via-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
        <i data-lucide="anchor" class="h-10 w-10 text-white relative z-10"></i>
      </div>
      <h2 class="text-3xl font-bold text-white mb-2 drop-shadow-lg">E船期查询系统</h2>
      <p class="text-blue-100 drop-shadow">🌊 请输入邀请码以访问系统 ⚓</p>
    </div>

    <form id="auth-form" class="space-y-6">
      <div>
        <label for="invite-code" class="block text-sm font-medium text-white mb-2 flex items-center">
          <i data-lucide="key" class="h-4 w-4 mr-2 text-cyan-300"></i>
          邀请码
        </label>
        <input type="text" id="invite-code" name="invite-code" class="w-full px-4 py-3 border border-white/30 rounded-lg focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 bg-white/10 text-white placeholder-blue-200 transition-all duration-200 backdrop-blur-sm" placeholder="请输入邀请码" required />
      </div>
      <button type="submit" id="auth-submit" class="w-full bg-gradient-to-r from-blue-500 via-cyan-500 to-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:via-cyan-600 hover:to-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02] shadow-lg relative overflow-hidden">
        <span class="relative z-10 flex items-center justify-center">
          <i data-lucide="ship" class="h-5 w-5 mr-2"></i>
          启航进入系统
        </span>
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer-slow"></div>
      </button>
      <div id="auth-error" class="hidden text-red-200 text-sm text-center bg-red-500/20 p-3 rounded-lg backdrop-blur-sm border border-red-400/30">⚠️ 邀请码无效，请检查后重试</div>
    </form>

    <div class="mt-6 text-center text-xs text-blue-200">
      <p class="flex items-center justify-center">
        <i data-lucide="waves" class="h-3 w-3 mr-1"></i>
        系统版本 <span id="login-version-text">—</span> | 仅限授权用户访问
        <i data-lucide="waves" class="h-3 w-3 ml-1"></i>
      </p>
    </div>
  </div>

  <script>
    lucide.createIcons();

    // 版本与 API
    const APP_VERSION = (window.__APP_VERSION__ || '2025.08.28-01');
    const API_BASE_URL = `${location.origin}/api/v1`;

    // 调试开关
    const DEBUG_LOGIN = (new URLSearchParams(location.search).get('debug') === '1') || (localStorage.getItem('debug_login') === '1');
    const loginDebug = { enabled: DEBUG_LOGIN, log(...args){ if(this.enabled){ try{ console.log('[LOGINDBG]',...args);}catch{}} } };

    // 已登录则尝试校验后跳转
    (async function precheck(){
      const raw = localStorage.getItem('auth_data');
      if(!raw) return;
      try {
        const data = JSON.parse(raw);
        if(!data.access_token) { localStorage.removeItem('auth_data'); return; }
        const resp = await fetch(`${API_BASE_URL}/auth/profile`, { headers: { 'Authorization': `Bearer ${data.access_token}` }});
        if(resp.ok){
          loginDebug.log('precheck.ok');
          toIndex();
        } else {
          loginDebug.log('precheck.invalid', resp.status);
          localStorage.removeItem('auth_data');
        }
      } catch(e){ loginDebug.log('precheck.error', e && e.message); localStorage.removeItem('auth_data'); }
    })();

    function toIndex(){
      const url = new URL('index.html', location.href);
      const qs = new URLSearchParams(location.search);
      if(DEBUG_LOGIN) qs.set('debug','1');
      if(!qs.has('v')) qs.set('v', APP_VERSION);
      url.search = qs.toString();
      location.href = url.toString();
    }

    // 渲染版本
    (function(){ try{ document.getElementById('login-version-text').textContent = APP_VERSION; }catch{} })();

    // 表单提交
    document.getElementById('auth-form')?.addEventListener('submit', async function(e){
      e.preventDefault();
      const inviteCode = document.getElementById('invite-code').value.trim();
      const errorDiv = document.getElementById('auth-error');
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalBtnText = submitBtn.innerHTML;
      submitBtn.innerHTML = '<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mx-auto"></div>';
      submitBtn.disabled = true;

      try {
        loginDebug.log('login.request.start', { url: `${API_BASE_URL}/auth/login` });
        const resp = await fetch(`${API_BASE_URL}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ invite_code: inviteCode })
        });
        loginDebug.log('login.response.status', resp.status);
        const result = await resp.json().catch(()=>({ success:false, message:'Invalid JSON' }));

        if(result && result.success && result.access_token && result.user_info){
          const authData = { access_token: result.access_token, user_info: result.user_info, expires_in: result.expires_in, timestamp: Date.now() };
          localStorage.setItem('auth_data', JSON.stringify(authData));
          loginDebug.log('login.success', { user: result.user_info && result.user_info.user_id });
          errorDiv.classList.add('hidden');
          toIndex();
        } else {
          loginDebug.log('login.failed', result);
          errorDiv.textContent = (result && result.message) || '邀请码无效，请检查后重试';
          errorDiv.classList.remove('hidden');
          document.getElementById('invite-code').focus();
        }
      } catch(err){
        loginDebug.log('login.error', err && err.message);
        errorDiv.textContent = '网络错误，请检查网络连接后重试';
        errorDiv.classList.remove('hidden');
      } finally {
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
      }
    });
  </script>
</body>
</html>