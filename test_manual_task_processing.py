#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动运行一个任务来测试完整的业务流程
验证 TaskProcessor -> AI分析 -> add_shipment_dates 的完整流程
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from task_processor import TaskProcessor
from shipment_manager import ShipmentManager
from task_manager import TaskManager

def test_complete_workflow():
    """测试完整的业务流程"""
    print("=== 测试完整业务流程 ===")
    
    try:
        # 1. 创建ShipmentManager和回调函数
        shipment_manager = ShipmentManager()
        
        def completion_callback(task_id: str, result_data: dict = None):
            """任务完成回调函数"""
            print(f"\n[CALLBACK] 收到任务完成通知: {task_id}")
            if result_data:
                print(f"[CALLBACK] 结果数据键: {list(result_data.keys())}")
                if 'dates_data' in result_data:
                    print(f"[CALLBACK] dates_data 长度: {len(result_data['dates_data'])}")
                    for i, date_item in enumerate(result_data['dates_data']):
                        print(f"  [CALLBACK] 日期项 {i+1}: {date_item.get('date')} @ {date_item.get('location')}")
            
            # 调用shipment_manager的处理方法
            success = shipment_manager.handle_task_completion(task_id, result_data)
            print(f"[CALLBACK] 处理结果: {'成功' if success else '失败'}")
            return success
        
        # 2. 创建TaskProcessor
        task_processor = TaskProcessor(completion_callback=completion_callback)
        
        # 3. 获取一个pending任务进行测试
        task_manager = TaskManager()
        pending_tasks = task_manager.get_pending_tasks(limit=1)
        
        if not pending_tasks:
            print("[ERROR] 没有待处理的任务")
            return False
        
        test_task = pending_tasks[0]
        print(f"\n[INFO] 选择测试任务: {test_task['task_name']}")
        print(f"[INFO] 任务ID: {test_task['id']}")
        print(f"[INFO] 跟踪号: {test_task['tracking_number']}")
        
        # 4. 手动处理单个任务
        print(f"\n[INFO] 开始手动处理任务...")
        success = task_processor.process_single_task(test_task)
        
        if success:
            print(f"\n[SUCCESS] 任务处理成功!")
            
            # 5. 验证结果
            print(f"\n[VERIFY] 验证处理结果...")
            
            # 检查任务状态
            updated_task = task_manager.get_task_by_id(test_task['id'])
            print(f"[VERIFY] 任务状态: {updated_task.get('status', 'Unknown')}")
            
            # 检查货运记录
            if '货运记录ID:' in updated_task.get('remarks', ''):
                record_id = updated_task['remarks'].split('货运记录ID:')[1].split(',')[0].strip()
                print(f"[VERIFY] 关联的货运记录ID: {record_id}")
                
                # 检查shipment_dates
                import sqlite3
                conn = sqlite3.connect('db/shipment_records.db')
                cursor = conn.cursor()
                
                cursor.execute("SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?", (record_id,))
                dates_count = cursor.fetchone()[0]
                print(f"[VERIFY] 该记录的shipment_dates数量: {dates_count}")
                
                if dates_count > 0:
                    cursor.execute("""
                        SELECT date, location, description, created_at 
                        FROM shipment_dates 
                        WHERE shipment_id = ? 
                        ORDER BY date
                    """, (record_id,))
                    
                    dates_records = cursor.fetchall()
                    print(f"[VERIFY] shipment_dates详情:")
                    for date_record in dates_records:
                        print(f"  - {date_record[0]} @ {date_record[1]}: {date_record[2]}")
                        print(f"    创建时间: {date_record[3]}")
                        
                        if '+08:00' in str(date_record[3]):
                            print(f"    [OK] 使用东八区时间")
                        else:
                            print(f"    [WARNING] 时间格式问题: {date_record[3]}")
                
                conn.close()
            
            return True
        else:
            print(f"\n[ERROR] 任务处理失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("手动测试完整业务流程")
    print("=" * 60)
    
    success = test_complete_workflow()
    
    if success:
        print("\n" + "=" * 60)
        print("[EXCELLENT] 完整业务流程测试成功!")
        print("现在 shipment_records 和 shipment_dates 都应该有正确的数据")
    else:
        print("\n" + "=" * 60)
        print("[ERROR] 完整业务流程测试失败")
        print("需要进一步调试问题")
    
    print("=" * 60)