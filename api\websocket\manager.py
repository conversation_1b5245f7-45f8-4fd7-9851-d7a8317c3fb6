#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket管理器
处理WebSocket连接和消息广播
"""

import json
import asyncio
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect, FastAPI
from fastapi.websockets import WebSocketState

from api.models.schemas import WebSocketMessage, TaskStatusUpdate
from api.utils.logger import api_logger

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接: {connection_id: websocket}
        self.active_connections: Dict[str, WebSocket] = {}
        # 任务订阅: {task_id: set(connection_ids)}
        self.task_subscriptions: Dict[str, Set[str]] = {}
        # 连接任务映射: {connection_id: set(task_ids)}
        self.connection_tasks: Dict[str, Set[str]] = {}
        # 系统连接: set(connection_ids)
        self.system_connections: Set[str] = set()
    
    async def connect(self, websocket: WebSocket, connection_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        api_logger.info(f"WebSocket连接建立: {connection_id}")
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        # 清理任务订阅
        if connection_id in self.connection_tasks:
            for task_id in self.connection_tasks[connection_id]:
                if task_id in self.task_subscriptions:
                    self.task_subscriptions[task_id].discard(connection_id)
                    if not self.task_subscriptions[task_id]:
                        del self.task_subscriptions[task_id]
            del self.connection_tasks[connection_id]
        
        # 清理系统连接
        self.system_connections.discard(connection_id)
        
        api_logger.info(f"WebSocket连接断开: {connection_id}")
    
    async def send_personal_message(self, message: dict, connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(json.dumps(message, ensure_ascii=False))
                else:
                    self.disconnect(connection_id)
            except Exception as e:
                api_logger.error(f"发送个人消息失败: {connection_id} - {str(e)}")
                self.disconnect(connection_id)
    
    async def broadcast_to_task(self, message: dict, task_id: str):
        """向订阅特定任务的连接广播消息"""
        if task_id in self.task_subscriptions:
            disconnected = []
            for connection_id in self.task_subscriptions[task_id].copy():
                try:
                    await self.send_personal_message(message, connection_id)
                except:
                    disconnected.append(connection_id)
            
            # 清理断开的连接
            for connection_id in disconnected:
                self.disconnect(connection_id)
    
    async def broadcast_to_all(self, message: dict):
        """向所有连接广播消息"""
        disconnected = []
        for connection_id in list(self.active_connections.keys()):
            try:
                await self.send_personal_message(message, connection_id)
            except:
                disconnected.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)
    
    async def broadcast_to_system(self, message: dict):
        """向系统连接广播消息"""
        disconnected = []
        for connection_id in self.system_connections.copy():
            try:
                await self.send_personal_message(message, connection_id)
            except:
                disconnected.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)
    
    def subscribe_to_task(self, connection_id: str, task_id: str):
        """订阅任务更新"""
        if task_id not in self.task_subscriptions:
            self.task_subscriptions[task_id] = set()
        self.task_subscriptions[task_id].add(connection_id)
        
        if connection_id not in self.connection_tasks:
            self.connection_tasks[connection_id] = set()
        self.connection_tasks[connection_id].add(task_id)
        
        api_logger.info(f"连接 {connection_id} 订阅任务 {task_id}")
    
    def subscribe_to_system(self, connection_id: str):
        """订阅系统消息"""
        self.system_connections.add(connection_id)
        api_logger.info(f"连接 {connection_id} 订阅系统消息")
    
    async def start_heartbeat(self):
        """启动心跳检测"""
        while True:
            await asyncio.sleep(30)  # 每30秒发送一次心跳
            heartbeat_message = {
                "type": "heartbeat",
                "timestamp": datetime.now().isoformat()
            }
            await self.broadcast_to_all(heartbeat_message)
    
    async def disconnect_all(self):
        """断开所有连接"""
        for connection_id in list(self.active_connections.keys()):
            websocket = self.active_connections[connection_id]
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.close()
            except:
                pass
            self.disconnect(connection_id)

class WebSocketManager:
    """WebSocket管理器"""
    
    def __init__(self):
        self.connection_manager = ConnectionManager()
        self._heartbeat_task = None
    
    def setup_routes(self, app: FastAPI):
        """设置WebSocket路由"""
        
        @app.websocket("/ws/task/{task_id}")
        async def websocket_task_endpoint(websocket: WebSocket, task_id: str):
            """任务状态订阅WebSocket端点"""
            connection_id = f"task_{task_id}_{id(websocket)}"
            await self.connection_manager.connect(websocket, connection_id)
            
            # 自动订阅任务
            self.connection_manager.subscribe_to_task(connection_id, task_id)
            
            try:
                while True:
                    data = await websocket.receive_text()
                    await self._handle_message(data, connection_id)
            except WebSocketDisconnect:
                self.connection_manager.disconnect(connection_id)
        
        @app.websocket("/ws/system")
        async def websocket_system_endpoint(websocket: WebSocket):
            """系统级WebSocket端点"""
            connection_id = f"system_{id(websocket)}"
            await self.connection_manager.connect(websocket, connection_id)
            
            # 自动订阅系统消息
            self.connection_manager.subscribe_to_system(connection_id)
            
            try:
                while True:
                    data = await websocket.receive_text()
                    await self._handle_message(data, connection_id)
            except WebSocketDisconnect:
                self.connection_manager.disconnect(connection_id)
        
        # 启动心跳检测
        if not self._heartbeat_task:
            self._heartbeat_task = asyncio.create_task(
                self.connection_manager.start_heartbeat()
            )
    
    async def _handle_message(self, data: str, connection_id: str):
        """处理客户端消息"""
        try:
            message = json.loads(data)
            message_type = message.get("type")
            
            if message_type == "ping":
                # 响应ping消息
                await self.connection_manager.send_personal_message(
                    {"type": "pong", "timestamp": datetime.now().isoformat()},
                    connection_id
                )
            elif message_type == "subscribe_task":
                # 订阅额外任务
                task_id = message.get("task_id")
                if task_id:
                    self.connection_manager.subscribe_to_task(connection_id, task_id)
            
        except json.JSONDecodeError:
            api_logger.error(f"无效的JSON消息: {data}")
        except Exception as e:
            api_logger.error(f"处理WebSocket消息异常: {str(e)}")
    
    async def notify_task_status_update(self, task_update: TaskStatusUpdate):
        """通知任务状态更新"""
        message = {
            "type": "task_status_update",
            "data": task_update.dict(),
            "timestamp": datetime.now().isoformat()
        }
        
        await self.connection_manager.broadcast_to_task(message, task_update.task_id)
        api_logger.info(f"任务状态更新通知已发送: {task_update.task_id} - {task_update.status}")
    
    async def notify_task_progress(self, task_id: str, progress: int, message: str = None):
        """通知任务进度更新"""
        notification = {
            "type": "task_progress",
            "data": {
                "task_id": task_id,
                "progress": progress,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await self.connection_manager.broadcast_to_task(notification, task_id)
        api_logger.info(f"任务进度更新通知已发送: {task_id} - {progress}%")
    
    async def notify_task_completed(self, task_id: str, result: dict):
        """通知任务完成"""
        notification = {
            "type": "task_completed",
            "data": {
                "task_id": task_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await self.connection_manager.broadcast_to_task(notification, task_id)
        api_logger.info(f"任务完成通知已发送: {task_id}")
    
    async def notify_task_failed(self, task_id: str, error_message: str):
        """通知任务失败"""
        notification = {
            "type": "task_failed",
            "data": {
                "task_id": task_id,
                "error_message": error_message,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await self.connection_manager.broadcast_to_task(notification, task_id)
        api_logger.info(f"任务失败通知已发送: {task_id}")
    
    async def notify_system_message(self, message: str, level: str = "info", data: dict = None):
        """发送系统消息"""
        notification = {
            "type": "system_message",
            "data": {
                "message": message,
                "level": level,
                "data": data or {},
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await self.connection_manager.broadcast_to_system(notification)
        api_logger.info(f"系统消息已发送: {message}")
    
    async def disconnect_all(self):
        """断开所有连接"""
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
        await self.connection_manager.disconnect_all()