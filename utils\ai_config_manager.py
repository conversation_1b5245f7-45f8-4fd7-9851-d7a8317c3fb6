#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI配置管理器
提供AI服务提供商和模型配置的数据访问接口
"""

import sqlite3
import os
from typing import List, Dict, Optional, Any
from datetime import datetime

class AIConfigManager:
    """AI配置管理器"""
    
    def __init__(self):
        self.db_path = os.path.join(os.path.dirname(__file__), '..', 'db', 'ai_config.db')
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_all_providers(self) -> List[Dict[str, Any]]:
        """获取所有AI服务提供商"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM ai_providers 
                ORDER BY is_active DESC, name ASC
            ''')
            return [dict(row) for row in cursor.fetchall()]
    
    def get_active_providers(self) -> List[Dict[str, Any]]:
        """获取活跃的AI服务提供商"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM ai_providers 
                WHERE is_active = 1
                ORDER BY name ASC
            ''')
            return [dict(row) for row in cursor.fetchall()]
    
    def get_provider_by_id(self, provider_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取服务提供商"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM ai_providers WHERE id = ?', (provider_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def add_provider(self, name: str, api_key: str, base_url: str, 
                    support_text: bool = False, support_image: bool = False,
                    support_audio: bool = False, support_video: bool = False,
                    text_model: str = '', image_model: str = '',
                    audio_model: str = '', video_model: str = '',
                    remark: str = '') -> int:
        """添加AI服务提供商"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO ai_providers 
                (name, api_key, base_url, support_text, support_image, support_audio, support_video,
                 text_model, image_model, audio_model, video_model, remark)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, api_key, base_url, support_text, support_image, support_audio, support_video,
                  text_model, image_model, audio_model, video_model, remark))
            conn.commit()
            return cursor.lastrowid
    
    def update_provider(self, provider_id: int, name: str, api_key: str, base_url: str,
                       support_text: bool = False, support_image: bool = False,
                       support_audio: bool = False, support_video: bool = False,
                       text_model: str = '', image_model: str = '',
                       audio_model: str = '', video_model: str = '',
                       remark: str = '', is_active: bool = True) -> bool:
        """更新AI服务提供商"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE ai_providers SET
                name = ?, api_key = ?, base_url = ?, support_text = ?, support_image = ?,
                support_audio = ?, support_video = ?, text_model = ?, image_model = ?,
                audio_model = ?, video_model = ?, remark = ?, is_active = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (name, api_key, base_url, support_text, support_image, support_audio, support_video,
                  text_model, image_model, audio_model, video_model, remark, is_active, provider_id))
            conn.commit()
            return cursor.rowcount > 0
    
    def delete_provider(self, provider_id: int) -> bool:
        """删除AI服务提供商"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # 先检查是否有关联的启用模型
            cursor.execute('SELECT COUNT(*) FROM active_models WHERE provider_id = ?', (provider_id,))
            if cursor.fetchone()[0] > 0:
                # 如果有关联，则只是标记为不活跃
                cursor.execute('UPDATE ai_providers SET is_active = 0 WHERE id = ?', (provider_id,))
            else:
                # 如果没有关联，则直接删除
                cursor.execute('DELETE FROM ai_providers WHERE id = ?', (provider_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def get_active_models(self) -> Dict[str, Dict[str, Any]]:
        """获取当前启用的模型配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT am.model_type, am.is_enabled, am.provider_id,
                       ap.name as provider_name, ap.api_key, ap.base_url,
                       CASE am.model_type
                           WHEN 'text' THEN ap.text_model
                           WHEN 'image' THEN ap.image_model
                           WHEN 'audio' THEN ap.audio_model
                           WHEN 'video' THEN ap.video_model
                       END as model_name
                FROM active_models am
                LEFT JOIN ai_providers ap ON am.provider_id = ap.id
                ORDER BY am.model_type
            ''')
            
            result = {}
            for row in cursor.fetchall():
                result[row['model_type']] = {
                    'is_enabled': bool(row['is_enabled']),
                    'provider_id': row['provider_id'],
                    'provider_name': row['provider_name'],
                    'api_key': row['api_key'],
                    'base_url': row['base_url'],
                    'model_name': row['model_name']
                }
            return result
    
    def update_active_model(self, model_type: str, provider_id: Optional[int] = None, 
                           is_enabled: bool = True) -> bool:
        """更新启用的模型配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # 使用 INSERT OR REPLACE 确保记录存在
            cursor.execute('''
                INSERT OR REPLACE INTO active_models 
                (model_type, provider_id, is_enabled, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (model_type, provider_id, is_enabled))
            conn.commit()
            return True
    
    def get_providers_by_support_type(self, model_type: str) -> List[Dict[str, Any]]:
        """根据支持的模型类型获取服务提供商"""
        support_field = f'support_{model_type}'
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(f'''
                SELECT * FROM ai_providers 
                WHERE is_active = 1 AND {support_field} = 1
                ORDER BY name ASC
            ''')
            return [dict(row) for row in cursor.fetchall()]
    
    def add_single_model(self, provider_name: str, model_name: str, model_type: str,
                        api_key: str, base_url: str, remark: str = '', is_active: bool = True) -> bool:
        """添加单个AI模型"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查是否已存在相同的服务商
            cursor.execute('SELECT id FROM ai_providers WHERE name = ?', (provider_name,))
            existing_provider = cursor.fetchone()
            
            if existing_provider:
                provider_id = existing_provider['id']
                # 更新现有服务商的模型信息
                model_field = f'{model_type}_model'
                support_field = f'support_{model_type}'
                cursor.execute(f'''
                    UPDATE ai_providers SET
                    {model_field} = ?, {support_field} = 1, api_key = ?, base_url = ?,
                    remark = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (model_name, api_key, base_url, remark, is_active, provider_id))
            else:
                # 创建新的服务商
                model_field = f'{model_type}_model'
                support_field = f'support_{model_type}'
                cursor.execute(f'''
                    INSERT INTO ai_providers 
                    (name, api_key, base_url, {support_field}, {model_field}, remark, is_active)
                    VALUES (?, ?, ?, 1, ?, ?, ?)
                ''', (provider_name, api_key, base_url, model_name, remark, is_active))
                provider_id = cursor.lastrowid
            
            conn.commit()
            return True
    
    def update_single_model(self, old_model_data: Dict[str, Any], provider_name: str, 
                           model_name: str, model_type: str, api_key: str, base_url: str,
                           remark: str = '', is_active: bool = True) -> bool:
        """更新单个AI模型"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 找到对应的服务商
            cursor.execute('SELECT * FROM ai_providers WHERE id = ?', (old_model_data['provider_id'],))
            provider = cursor.fetchone()
            
            if provider:
                # 更新服务商的模型信息
                model_field = f'{model_type}_model'
                support_field = f'support_{model_type}'
                cursor.execute(f'''
                    UPDATE ai_providers SET
                    name = ?, {model_field} = ?, {support_field} = 1, api_key = ?, base_url = ?,
                    remark = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (provider_name, model_name, api_key, base_url, remark, is_active, old_model_data['provider_id']))
                
                conn.commit()
                return cursor.rowcount > 0
            
            return False