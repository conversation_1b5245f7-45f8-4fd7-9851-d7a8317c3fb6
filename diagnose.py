#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用启动诊断脚本
"""

import sys
import traceback

def test_imports():
    print("🔍 开始导入测试...")
    
    imports_to_test = [
        ('shipment_manager', 'ShipmentManager'),
        ('task_manager', 'TaskManager'),
        ('scheduled_task_processor', 'ScheduledTaskProcessor'),
        ('scraping_executor', 'ScrapingExecutor'),
        ('ai_analysis_executor', 'AIAnalysisExecutor'),
        ('task_processor', 'TaskProcessor'),
    ]
    
    failed_imports = []
    
    for module, class_name in imports_to_test:
        try:
            exec(f"from {module} import {class_name}")
            print(f"✅ {module}.{class_name}")
        except Exception as e:
            print(f"❌ {module}.{class_name}: {e}")
            failed_imports.append((module, class_name, str(e)))
    
    if failed_imports:
        print(f"\n⚠️  发现 {len(failed_imports)} 个导入问题:")
        for module, class_name, error in failed_imports:
            print(f"   {module}.{class_name}: {error}")
        return False
    else:
        print(f"\n✅ 所有导入测试通过!")
        return True

def test_app_startup():
    print("\n🚀 测试应用程序启动...")
    
    try:
        print("导入PySide6...")
        from PySide6.QtWidgets import QApplication
        
        print("导入应用程序模块...")
        import app
        
        print("创建QApplication...")
        # 检查是否已有实例
        app_instance = QApplication.instance()
        if app_instance is None:
            app_instance = QApplication(sys.argv)
        
        print("创建主窗口...")
        window = app.ShipmentRecordApp()
        
        print("✅ 应用程序初始化成功!")
        return True
        
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        print("错误详情:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("📊 Container Helper 启动诊断")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试应用启动
        app_success = test_app_startup()
        
        if app_success:
            print(f"\n🎉 诊断完成: 应用程序可以正常启动!")
        else:
            print(f"\n⚠️  诊断完成: 导入正常但应用启动失败")
    else:
        print(f"\n❌ 诊断完成: 存在导入问题，无法继续测试应用启动")