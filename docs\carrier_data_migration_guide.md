# 船公司数据管理升级指南

## 📋 概述

本指南介绍如何将硬编码的 `COMPANY_DATA` 字典迁移到数据库管理系统，提升数据维护的便利性和系统的可扩展性。

## 🎯 升级优势

### 当前问题（硬编码字典）
- ❌ 数据维护困难，需要修改代码
- ❌ 无法动态添加/删除船公司
- ❌ 数据变更需要重启应用
- ❌ 无法进行数据统计和分析
- ❌ 多人协作时容易产生冲突

### 升级后优势（数据库管理）
- ✅ 数据与代码分离，维护简单
- ✅ 支持动态增删改查
- ✅ 实时数据更新，无需重启
- ✅ 丰富的数据统计功能
- ✅ 支持数据备份和恢复
- ✅ 可扩展的管理界面

## 🚀 迁移步骤

### 步骤 1: 初始化数据库

```bash
# 进入项目目录
cd d:\container-helper

# 创建船公司数据库
python db/init_carrier_database.py
```

### 步骤 2: 迁移现有数据

```bash
# 将 COMPANY_DATA 迁移到数据库
python db/migrate_carrier_data.py
```

### 步骤 3: 启用数据库模式

#### 方法 A: 环境变量（推荐）
```bash
# Windows PowerShell
$env:CARRIER_USE_DATABASE = "true"

# Windows CMD
set CARRIER_USE_DATABASE=true

# Linux/Mac
export CARRIER_USE_DATABASE=true
```

#### 方法 B: 修改代码
在 `utils/carrier_lookup.py` 中直接修改：
```python
USE_DATABASE = True  # 改为 True
```

### 步骤 4: 测试功能

```bash
# 测试数据库查询功能
python utils/carrier_database.py

# 测试兼容性
python utils/carrier_lookup.py
```

## 📊 数据库结构

### 主要表结构

#### 1. carriers（船公司主表）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键 |
| company_name | VARCHAR(200) | 公司名称 |
| company_code | VARCHAR(20) | 公司代码 |
| international_site | VARCHAR(500) | 国际站网址 |
| chinese_site | VARCHAR(500) | 中文站网址 |
| tracking_site | VARCHAR(500) | 追踪网址 |
| is_active | BOOLEAN | 是否启用 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 2. carrier_scac_prefixes（SCAC前缀表）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键 |
| carrier_id | INTEGER | 船公司ID |
| scac_prefix | VARCHAR(10) | SCAC前缀 |
| created_at | DATETIME | 创建时间 |

#### 3. carrier_bl_patterns（提单号规则表）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键 |
| carrier_id | INTEGER | 船公司ID |
| pattern | VARCHAR(200) | 正则表达式 |
| description | VARCHAR(500) | 规则描述 |
| created_at | DATETIME | 创建时间 |

## 🔧 API 使用示例

### 基础查询（兼容原接口）
```python
from utils.carrier_lookup import get_company_info

# 查询船公司信息
result = get_company_info("MEDUVS935363")
if result:
    print(f"公司: {result['company']}")
    print(f"追踪网站: {result['tracking_site']}")
```

### 高级数据库操作
```python
from utils.carrier_database import CarrierDatabase

db = CarrierDatabase()

# 获取所有船公司
carriers = db.get_all_carriers()

# 搜索船公司
results = db.search_carriers("MSC")

# 添加新船公司
carrier_id = db.add_carrier(
    company_name="新船公司",
    company_code="NEW",
    international_site="https://example.com",
    chinese_site="https://example.cn",
    tracking_site="https://example.com/track",
    scac_prefixes=["NEWU"],
    bl_patterns=[r"^NEW\w*"]
)

# 更新船公司信息
db.update_carrier(carrier_id, company_name="更新后的公司名")

# 获取统计信息
stats = db.get_statistics()
print(f"活跃船公司数: {stats['active_carriers']}")
```

## 🔄 回退方案

如果需要回退到硬编码字典模式：

### 方法 1: 环境变量
```bash
# 设置为 false 或删除环境变量
$env:CARRIER_USE_DATABASE = "false"
```

### 方法 2: 修改代码
```python
# 在 utils/carrier_lookup.py 中
USE_DATABASE = False
```

## 📈 性能优化

### 缓存机制
- 数据库查询结果会被缓存，提升查询性能
- 缓存在数据更新时自动清除

### 连接池
- 使用 SQLite 的轻量级连接管理
- 支持并发查询

## 🛠️ 维护工具

### 数据备份
```bash
# 备份数据库
cp db/carriers.db db/carriers_backup_$(date +%Y%m%d).db
```

### 数据恢复
```bash
# 恢复数据库
cp db/carriers_backup_20250804.db db/carriers.db
```

### 数据验证
```bash
# 验证数据完整性
python utils/carrier_database.py
```

## 🎨 未来扩展

### 管理界面
- 可开发 Web 管理界面
- 支持可视化的船公司数据管理
- 提供数据导入/导出功能

### API 接口
- 可提供 REST API 接口
- 支持外部系统集成
- 实现数据同步功能

### 高级功能
- 数据版本控制
- 审计日志
- 权限管理
- 多语言支持

## ❓ 常见问题

### Q: 迁移后原有代码是否需要修改？
A: 不需要。我们保持了完全的向后兼容性，原有的 `get_company_info()` 函数接口不变。

### Q: 数据库文件丢失怎么办？
A: 系统会自动回退到硬编码字典模式，确保功能正常运行。

### Q: 如何添加新的船公司？
A: 使用 `CarrierDatabase.add_carrier()` 方法，或者开发管理界面进行可视化操作。

### Q: 性能是否会受影响？
A: 由于使用了缓存机制，查询性能与硬编码字典基本相当，甚至在某些场景下更优。

## 📞 技术支持

如果在迁移过程中遇到问题，请：
1. 检查数据库文件是否正确创建
2. 确认环境变量设置正确
3. 查看控制台错误信息
4. 尝试回退到硬编码模式进行对比测试

---

**注意**: 建议在生产环境使用前，先在测试环境完成完整的迁移和测试流程。