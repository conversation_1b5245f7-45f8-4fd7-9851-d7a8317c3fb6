<!DOCTYPE html>
<html>
 <body>
  <div class="msc-main">
   <div class="msc-flow-tracking separator--bottom-medium">
    <div class="grid-container">
     <div class="grid-x no-print">
      <div class="cell small-12">
       <div class="msc-flow-tracking__heading">
        <h1>
         Tracking
        </h1>
       </div>
      </div>
     </div>
     <div class="grid-x">
      <div class="cell small-12">
       <div class="msc-flow-tracking__print">
        <div class="msc-flow-tracking__print-header">
         <h2>
          Tracking
         </h2>
         <p>
          https://www.msc.com/en/track-a-shipment
         </p>
        </div>
       </div>
      </div>
     </div>
     <div class="grid-x">
      <div class="cell small-12">
       <div class="msc-flow-tracking__wrapper">
        <div>
         <div class="msc-flow-tracking__results">
          <div>
           <div class="msc-flow-tracking__result">
            <div class="msc-flow-tracking__subheading">
             <h3>
              <span x-text="title">
               BILL OF LADING:
              </span>
              <span x-text="trackingNumber">
               MEDUJ0618622
              </span>
              <span class="msc-flow-tracking__subtitle-info">
               1 Bill of Lading found
              </span>
             </h3>
            </div>
            <div class="msc-flow-tracking__details">
             <ul>
              <li>
               <div>
                <span class="msc-flow-tracking__details-heading">
                 Container Number
                </span>
                <span class="msc-flow-tracking__details-value" x-text="getContainerNumber()">
                 MSDU9017283
                </span>
               </div>
               <div>
                <span class="msc-flow-tracking__details-heading">
                 Bill of Lading:
                </span>
                <span class="msc-flow-tracking__details-value" x-text="result.BillOfLadingNumber">
                 MEDUJ0618622
                </span>
                <span class="msc-flow-tracking__details-subtitle">
                 <span>
                  (
                 </span>
                 <span x-text="result.NumberOfContainers">
                  1
                 </span>
                 <span>
                  Container
                 </span>
                 <span>
                  Containers
                 </span>
                 <span>
                  )
                 </span>
                </span>
               </div>
              </li>
              <li>
               <span class="msc-flow-tracking__details-heading">
                Shipped From
               </span>
               <span class="msc-flow-tracking__details-value" x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.ShippedFrom)">
                Heredia,  CR
               </span>
              </li>
              <li>
               <span class="msc-flow-tracking__details-heading">
                Port of Load
               </span>
               <span class="msc-flow-tracking__details-value">
                <span x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.PortOfLoad)">
                 Moin,  CR
                </span>
                <span>
                 (
                </span>
                <span x-text="result.GeneralTrackingInfo.PortOfLoadLocationCode">
                </span>
                <span>
                 )
                </span>
               </span>
              </li>
              <li>
               <span class="msc-flow-tracking__details-heading">
                Port of Discharge
               </span>
               <span class="msc-flow-tracking__details-value">
                <span x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.PortOfDischarge)">
                 Shanghai,  CN
                </span>
                <span>
                 (
                </span>
                <span x-text="result.GeneralTrackingInfo.PortOfDischargeLocationCode">
                </span>
                <span>
                 )
                </span>
               </span>
              </li>
              <li>
               <span class="msc-flow-tracking__details-heading">
                Shipped To
               </span>
               <span class="msc-flow-tracking__details-value" x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.ShippedTo)">
                Shanghai,  CN
               </span>
              </li>
              <li>
               <span class="msc-flow-tracking__details-heading">
                Transhipment
               </span>
               <span class="msc-flow-tracking__details-value" x-text="convertToSentenceCaseLocation(item)">
                Cristobal,  PA
               </span>
               <span class="msc-flow-tracking__details-value" x-text="convertToSentenceCaseLocation(item)">
                Rodman,  PA
               </span>
              </li>
              <li>
               <span class="msc-flow-tracking__details-heading">
                Price Calculation Date*
               </span>
               <span class="msc-flow-tracking__details-value" x-text="result.GeneralTrackingInfo.PriceCalculationDate">
                20/07/2025
               </span>
              </li>
             </ul>
            </div>
            <div class="msc-flow-tracking__containers">
             <p>
              * Price calculation date is indicative. Please contact your local MSC office to verify this information.
             </p>
             <h4>
              Containers
             </h4>
             <div>
              <div class="msc-flow-tracking__container">
               <div class="msc-flow-tracking__bar open">
                <div class="msc-flow-tracking__content">
                 <div class="msc-flow-tracking__cell msc-flow-tracking__cell--one msc-flow-tracking__cell--first">
                  <div class="msc-flow-tracking__cell-flex">
                   <div class="msc-flow-tracking__data">
                    <div>
                     <span class="data-heading">
                      Container
                     </span>
                     <span class="data-value" x-text="container.ContainerNumber">
                      MSDU9017283
                     </span>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                  <div class="msc-flow-tracking__cell-flex">
                   <div class="msc-flow-tracking__data">
                    <div>
                     <span class="data-heading">
                      Type
                     </span>
                     <span class="data-value" x-text="container.ContainerType">
                      40' HIGH CUBE REEFER
                     </span>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three msc-flow-tracking__cell--delivered">
                  <div class="msc-flow-tracking__cell-flex">
                   <div class="msc-flow-tracking__data">
                    <div>
                     <span class="data-heading">
                      Latest move
                     </span>
                     <span class="data-value" x-text="container.LatestMove">
                      RODMAN, PA
                     </span>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four">
                  <div class="msc-flow-tracking__cell-flex">
                   <div class="msc-flow-tracking__data">
                    <div>
                     <span class="data-heading">
                      POD ETA
                     </span>
                     <span class="data-value" x-text="container.PodEtaDate">
                      31/08/2025
                     </span>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
               </div>
               <div class="msc-flow-tracking__tracking">
                <div class="msc-flow-tracking__header show-for-large">
                 <div class="msc-flow-tracking__cell--two">
                  <span class="data-heading">
                   Date
                  </span>
                 </div>
                 <div class="msc-flow-tracking__cell--three">
                  <span class="data-heading">
                   Location
                  </span>
                 </div>
                 <div class="msc-flow-tracking__cell--four">
                  <span class="data-heading">
                   Description
                  </span>
                 </div>
                 <div class="msc-flow-tracking__cell--five">
                  <span class="data-heading data-heading--break">
                   Empty/Laden/Vessel/Voyage
                  </span>
                 </div>
                 <div class="msc-flow-tracking__cell--six">
                  <span class="data-heading">
                   Equipment handling facility name
                  </span>
                 </div>
                </div>
                <div class="msc-flow-tracking__steps">
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--end">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       31/08/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Shanghai,  CN
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Estimated Time of Arrival
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         MSC UBERTY VIII GO535N
                        </span>
                       </span>
                       <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                        <div class="msc-flow-tracking__tooltip">
                         <div class="msc-flow-tracking__tooltip-box vessel">
                          <div class="msc-flow-tracking__tooltip-box__info">
                           <ul>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Flag
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.FlagName">
                               LIBERIA
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               IMO
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.IMO">
                               9337444
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Built
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.Built">
                               2008
                              </span>
                             </div>
                            </li>
                           </ul>
                          </div>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Shanghai shengdong international container terminal co ltd
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              SMDG
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg">
                              SHENG
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--yellow">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       30/07/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Rodman,  PA
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Full Transshipment Loaded
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         MSC UBERTY VIII XA530A
                        </span>
                       </span>
                       <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                        <div class="msc-flow-tracking__tooltip">
                         <div class="msc-flow-tracking__tooltip-box vessel">
                          <div class="msc-flow-tracking__tooltip-box__info">
                           <ul>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Flag
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.FlagName">
                               LIBERIA
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               IMO
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.IMO">
                               9337444
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Built
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.Built">
                               2008
                              </span>
                             </div>
                            </li>
                           </ul>
                          </div>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Panama singapur international terminal
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              SMDG
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg">
                              PPIT
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--yellow">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       28/07/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Rodman,  PA
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Full Transshipment Positioned In
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         LADEN
                        </span>
                       </span>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Panama singapur international terminal
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              SMDG
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg">
                              PPIT
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--yellow">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       28/07/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Colon,  PA
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Full Transshipment Positioned Out
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         LADEN
                        </span>
                       </span>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Colon container terminal
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              SMDG
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg">
                              CCT
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--yellow">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       25/07/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Colon,  PA
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Full Transshipment Discharged
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         MSC PASSION III PH528R
                        </span>
                       </span>
                       <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                        <div class="msc-flow-tracking__tooltip">
                         <div class="msc-flow-tracking__tooltip-box vessel">
                          <div class="msc-flow-tracking__tooltip-box__info">
                           <ul>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Flag
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.FlagName">
                               LIBERIA
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               IMO
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.IMO">
                               9399765
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Built
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.Built">
                               2008
                              </span>
                             </div>
                            </li>
                           </ul>
                          </div>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Colon container terminal
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              SMDG
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg">
                              CCT
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--yellow">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       20/07/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Moin,  CR
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Export Loaded on Vessel
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         MSC PASSION III PH528R
                        </span>
                       </span>
                       <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                        <div class="msc-flow-tracking__tooltip">
                         <div class="msc-flow-tracking__tooltip-box vessel">
                          <div class="msc-flow-tracking__tooltip-box__info">
                           <ul>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Flag
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.FlagName">
                               LIBERIA
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               IMO
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.IMO">
                               9399765
                              </span>
                             </div>
                            </li>
                            <li>
                             <div class="tooltip-row">
                              <span class="data-tooltip-title">
                               Built
                              </span>
                              <span class="data-tooltip-value" x-text="event.Vessel.Built">
                               2008
                              </span>
                             </div>
                            </li>
                           </ul>
                          </div>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Apm terminal moin
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              SMDG
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg">
                              APMT
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--yellow">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       19/07/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Moin,  CR
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Export received at CY
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         LADEN
                        </span>
                       </span>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Apm terminal moin
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              SMDG
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg">
                              APMT
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="msc-flow-tracking__port">
                  <div class="msc-flow-tracking__step msc-flow-tracking__step--start">
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Date">
                       15/07/2025
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)">
                       Puerto limon,  CR
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <span class="data-value" x-text="event.Description">
                       Empty to Shipper
                      </span>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div>
                       <span class="data-value">
                        <span x-text="eventDetailsLabel(event.Detail)">
                         EMPTY
                        </span>
                       </span>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                    <div class="msc-flow-tracking__cell-flex">
                     <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                      <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                       <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)">
                        Medlog costa rica
                       </span>
                       <div class="msc-flow-tracking__tooltip">
                        <div class="msc-flow-tracking__tooltip-box">
                         <div class="msc-flow-tracking__tooltip-box__info">
                          <ul>
                           <li>
                            <div class="tooltip-row">
                             <span class="data-tooltip-title">
                              BIC
                             </span>
                             <span class="data-tooltip-value" x-text="event.EquipmentHandling.Bic">
                              COPNMRZFH
                             </span>
                            </div>
                           </li>
                          </ul>
                         </div>
                        </div>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
         <div class="msc-flow-tracking-result__footer">
          <p>
           Tracking results provided by MSC on 04.08.2025 at 11:29 Central Europe Standard Time
          </p>
          <p>
           *
           <u>
            Show all
           </u>
           Intermediate Port Calls.
           <br/>
           If you have any questions regarding the results of your shipment tracking results, please contact your local MSC team at the number below. By using the shipment tracking service you agree to the
           <a href="/en/terms-and-conditions">
            terms of use
           </a>
           of msc.com.
          </p>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div>
    <div class="msc-tracking-subscription-banner__trigger separator--bottom-medium">
     <div class="msc-tracking-subscription-banner__trigger-text bg-primary">
      <div class="msc-content-text">
       <h2>
        Subscribe to Track &amp; Trace Notifications
       </h2>
       <div class="msc-content-description">
        <p>
         Get cargo updates in your inbox about the status of your cargo. Just search for your shipment and click "Notify me" below the results.
        </p>
       </div>
      </div>
      <div class="msc-tracking-subscription-banner__trigger-cta">
       <span class="msc-link-arrow-simple">
        Subscribe now!
       </span>
      </div>
     </div>
    </div>
   </div>
   <div class="msc-solution separator--top-big separator--bottom-small">
    <div class="grid-container">
     <div class="grid-x">
      <div class="cell small-12 medium-6 medium-offset-3 msc-ocean-solution__headings">
       <h2>
        Did you know?
       </h2>
       <div class="msc-bodytext">
        MSC also offers Tracking data via DCSA compliant APIs
       </div>
      </div>
     </div>
     <div class="grid-x">
      <div class="cell small-12">
       <div class="msc-solution__container msc-solution__container--small-grid msc-solution__container--layout-8x1-center">
        <div class="msc-solution__card">
         <img alt="" src="https://msc-p-001.sitecorecontenthub.cloud/api/public/content/6406c4ebd0b442feb9a2ceda5f2a57db?v=4076f49e"/>
         <img alt=""/>
         <div class="msc-solution__card-content">
          <div class="msc-solution__card-content-title">
           Learn more on our Direct Integrations page
          </div>
          <div class="msc-solution__card-content-text">
           <a href="/en/solutions/digital-solutions/direct-integrations">
            <span class="">
             Read more
            </span>
           </a>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
  <div>
   <div class="otFlat bottom ot-wo-title vertical-align-content">
    <div>
     <div class="ot-sdk-container">
      <div class="ot-sdk-row">
       <div class="ot-sdk-eight ot-sdk-columns">
        <div>
         <div>
          本网站使用 Cookie 来增强用户体验，并分析我们网站的性能和流量。我们还会与合作的社交媒体、广告商和分析商分享与您使用我们网站相关的信息。
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="otPcCenter ot-hide ot-fade-in">
    <div>
     <div class="ot-pc-scrollbar">
      <h2>
       不得出售我的个人信息
      </h2>
      <div>
       当您访问我们的网站时，我们会将 Cookie 存储在您的浏览器上以收集信息。收集的信息可能与您、您的偏好或您的设备相关，并且主要用于使网站按您的预期工作，以及提供更个性化的 Web 体验。但是，您可以选择阻止某些类型的 Cookie，因为它可能会影响您的网站体验和我们能够提供的服务。单击不同类别标题以了解更多信息并根据您的偏好更改我们的默认设置。您不能选择退出第一方绝对必要的 Cookie，因为它们的部署是为了确保我们的网站正常运行（例如提示 Cookie 横幅和记住您的设置、登录您的帐户、在您注销时重定向您等）。有关已使用的第一方和第三方 Cookie 的更多信息，请访问此链接。
       <br/>
       <a href="https://cookiepedia.co.uk/giving-consent-to-cookies">
        更多信息
       </a>
      </div>
      <section>
       <h3>
        管理许可偏好
       </h3>
       <div class="ot-accordion-layout ot-cat-item ot-vs-config">
        <div class="ot-acc-hdr ot-always-active-group">
         <h4>
          绝对必要的 Cookie
         </h4>
         <div class="ot-always-active">
          始终处于活动状态
         </div>
        </div>
        <div class="ot-acc-grpcntr ot-acc-txt">
         <p>
          网站运行离不开这些 Cookie 且您不能在系统中将其关闭。通常仅根据您所做出的操作（即服务请求）来设置这些 Cookie，如设置隐私偏好、登录或填充表格。您可以将您的浏览器设置为阻止或向您提示这些 Cookie，但可能会导致某些网站功能无法工作。
         </p>
        </div>
       </div>
       <div class="ot-accordion-layout ot-cat-item ot-vs-config">
        <div class="ot-acc-hdr">
         <h4>
          Sale of Personal Data
         </h4>
         <div class="ot-tgl">
          <input data-optanongroupid="BG1"/>
          <label>
           <span class="ot-label-txt">
            Sale of Personal Data
           </span>
          </label>
         </div>
        </div>
        <div class="ot-acc-grpcntr ot-acc-txt">
         <p>
          Under the California Consumer Privacy Act, you have the right to opt-out of the sale of your personal information to third parties. These cookies collect information for analytics and to personalize your experience with targeted ads. You may exercise your right to opt out of the sale of personal information by using this toggle switch.
                If you opt out we will not be able to offer you personalised ads and will not hand over your personal information to any third parties. Additionally, you may contact our legal department for further clarification about your rights as a California consumer by using this Exercise My Rights link. If you have enabled privacy controls on your browser (such as a plugin), we have to take that as a valid request to opt-out. Therefore we would not be able to track your activity through the web. This may affect our ability to personalize ads according to your preferences.
         </p>
         <div class="ot-subgrp-cntr">
          <ul>
           <li data-optanongroupid="C0002">
            <h5>
             性能 Cookie
            </h5>
            <div class="ot-tgl-cntr ot-subgrp-tgl">
             <div class="ot-tgl ot-hide-tgl">
              <input data-optanongroupid="C0002"/>
              <label>
               <span class="ot-label-txt">
                Switch Label
               </span>
              </label>
              <span class="ot-label-status">
               label
              </span>
             </div>
            </div>
            <p>
             使用 Cookie，我们可以计算访问量和流量来源，以便衡量和提高我们网站的性能。Cookie 有助于我们了解哪些页面最受欢迎、哪些最不受欢迎，并查看访问者如何浏览网站。这些 Cookie 收集的所有信息都聚合在一起，因此是匿名处理方式。如果您不允许使用这些 Cookie，我们将不知道您何时访问了我们的网站。
            </p>
           </li>
          </ul>
         </div>
         <div class="ot-subgrp-cntr">
          <ul>
           <li data-optanongroupid="C0004">
            <h5>
             定向 Cookie
            </h5>
            <div class="ot-tgl-cntr ot-subgrp-tgl">
             <div class="ot-tgl ot-hide-tgl">
              <input data-optanongroupid="C0004"/>
              <label>
               <span class="ot-label-txt">
                Switch Label
               </span>
              </label>
              <span class="ot-label-status">
               label
              </span>
             </div>
            </div>
            <p>
             这些 Cookie 由广告合作伙伴通过我们的网站进行设置。这些公司可能利用 Cookie 构建您的兴趣分布图并向您展示其他网站上的相关广告。它们只需识别您的浏览器和设备便可发挥作用。如果您不允许使用这些 Cookie，您将不能体验不同网站上的定向广告。
            </p>
           </li>
          </ul>
         </div>
        </div>
       </div>
      </section>
     </div>
     <section>
      <div>
       <div>
        <h3>
         Cookie 列表
        </h3>
       </div>
       <div class="ot-lst-subhdr">
        <section>
         <div>
          <div class="ot-fltr-scrlcnt ot-pc-scrollbar">
           <div class="ot-fltr-opts">
            <div class="ot-fltr-opt">
             <div class="ot-chkbox">
              <input/>
              <label>
               <span class="ot-label-txt">
                checkbox label
               </span>
              </label>
              <span class="ot-label-status">
               label
              </span>
             </div>
            </div>
           </div>
          </div>
         </div>
        </section>
       </div>
      </div>
      <section>
       <div>
        <div class="ot-sel-all">
         <div class="ot-sel-all-hdr">
          <span class="ot-consent-hdr">
           Consent
          </span>
          <span class="ot-li-hdr">
           Leg.Interest
          </span>
         </div>
         <div class="ot-sel-all-chkbox">
          <div class="ot-chkbox">
           <input/>
           <label>
            <span class="ot-label-txt">
             checkbox label
            </span>
           </label>
           <span class="ot-label-status">
            label
           </span>
          </div>
          <div class="ot-chkbox">
           <input/>
           <label>
            <span class="ot-label-txt">
             checkbox label
            </span>
           </label>
           <span class="ot-label-status">
            label
           </span>
          </div>
          <div class="ot-chkbox">
           <input/>
           <label>
            <span class="ot-label-txt">
             checkbox label
            </span>
           </label>
           <span class="ot-label-status">
            label
           </span>
          </div>
         </div>
        </div>
       </div>
      </section>
     </section>
     <span class="ot-scrn-rdr">
      您的隐私 [“对话已关闭”]
     </span>
    </div>
   </div>
  </div>
 </body>
</html>