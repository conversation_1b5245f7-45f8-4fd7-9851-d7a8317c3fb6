#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定时任务处理器的状态更新回调机制
"""

import time
import sqlite3
from shipment_manager import ShipmentManager
from task_manager import TaskManager
from scheduled_task_processor import ScheduledTaskProcessor

def test_callback_mechanism():
    """测试定时任务处理器的状态更新回调"""
    print("开始测试定时任务处理器的状态更新回调...")
    
    # 初始化管理器
    shipment_manager = ShipmentManager()
    task_manager = TaskManager()
    
    # 清理旧测试数据
    print("\n清理旧测试数据...")
    try:
        # 清理货运记录
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute("DELETE FROM shipment_records WHERE bill_of_lading LIKE 'CALLBACK%'")
        conn.commit()
        conn.close()
        print("已清理旧货运记录")
        
        # 清理任务队列
        conn = sqlite3.connect('db/task_queue.db') 
        cursor = conn.cursor()
        cursor.execute("DELETE FROM task_queue WHERE tracking_number LIKE 'CALLBACK%'")
        conn.commit()
        conn.close()
        print("已清理旧任务记录")
    except Exception as e:
        print(f"清理数据时出错: {e}")
    
    # 定义回调处理函数
    def mock_completion_callback(task_id, task_stage, result_data):
        """模拟任务完成回调函数"""
        print(f"\n[CALLBACK] 收到任务完成回调:")
        print(f"[CALLBACK]   任务ID: {task_id}")
        print(f"[CALLBACK]   任务阶段: {task_stage}")
        print(f"[CALLBACK]   结果数据: {result_data}")
        
        # 模拟状态更新处理
        if task_stage == 'status_update' and result_data:
            print("[CALLBACK] 检测到状态更新请求")
            if 'shipment_status_update' in result_data:
                update_info = result_data['shipment_status_update']
                record_id = update_info.get('record_id')
                new_status = update_info.get('new_status')
                print(f"[CALLBACK]   记录ID: {record_id}")
                print(f"[CALLBACK]   新状态: {new_status}")
                
                # 实际更新状态
                if record_id and new_status:
                    success = shipment_manager.update_shipment_status(record_id, new_status, 'callback_test')
                    print(f"[CALLBACK]   状态更新结果: {'成功' if success else '失败'}")
                    return success
                else:
                    print("[CALLBACK]   缺少必要参数")
                    return False
            else:
                print("[CALLBACK]   无状态更新数据")
                return False
        else:
            print(f"[CALLBACK] 其他阶段回调: {task_stage}")
            return True
    
    # 创建测试记录
    print("\n创建测试货运记录...")
    test_bill = "CALLBACK001"
    record_id = shipment_manager.create_shipment_record(
        bill_of_lading=test_bill,
        carrier_company="回调测试船公司",
        created_by="回调机制测试"
    )
    print(f"测试记录已创建，ID: {record_id}")
    
    # 检查初始状态
    records = shipment_manager.search_shipment_records(bill_of_lading=test_bill)
    if records:
        record = records[0]
        print(f"初始状态: {record['status']}")
    else:
        print("ERROR: 未找到刚创建的记录")
        return
    
    # 创建定时任务处理器并设置回调
    print("\n创建定时任务处理器...")
    processor = ScheduledTaskProcessor(
        scraping_interval=5,
        ai_interval=3,
        status_update_interval=10,
        max_scraping_tasks=1,
        max_ai_tasks=1,
        completion_callback=mock_completion_callback
    )
    
    # 测试 _update_shipment_status_from_task 方法
    print("\n测试 _update_shipment_status_from_task 方法...")
    
    # 查找刚创建的任务
    time.sleep(1)
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute("""
        SELECT id, task_name, tracking_number, remarks 
        FROM task_queue 
        WHERE tracking_number = ? 
        ORDER BY created_at DESC LIMIT 1
    """, (test_bill,))
    task_data = cursor.fetchone()
    conn.close()
    
    if task_data:
        # 正确映射字段
        task_dict = {
            'id': task_data[0],
            'task_name': task_data[1],
            'tracking_number': task_data[2],
            'remarks': task_data[3] or ''
        }
        
        print(f"找到任务: {task_dict['task_name']}")
        print(f"任务备注: {task_dict['remarks']}")
        
        # 直接测试状态更新方法
        print("\n测试状态更新方法...")
        processor._update_shipment_status_from_task(task_dict, "处理中")

        # 检查状态是否更新
        time.sleep(1)
        records = shipment_manager.search_shipment_records(bill_of_lading=test_bill)
        if records:
            record = records[0]
            print(f"更新后状态: {record['status']}")
        
    else:
        print("ERROR: 未找到相关任务")
    
    print("\n测试完成")

if __name__ == "__main__":
    test_callback_mechanism()