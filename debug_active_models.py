#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试active_models表的数据
"""

import sqlite3
import os
from utils.ai_config_manager import AIConfigManager

def debug_active_models():
    """调试active_models表的数据"""
    print("=== 调试AI模型配置 ===")
    
    # 直接查看数据库
    db_path = os.path.join('db', 'ai_config.db')
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("\n1. 查看active_models表:")
    cursor.execute('SELECT * FROM active_models')
    active_models = cursor.fetchall()
    if active_models:
        for model in active_models:
            print(f"  - 模型类型: {model['model_type']}, 启用: {model['is_enabled']}, 提供商ID: {model['provider_id']}")
    else:
        print("  ❌ active_models表为空")
    
    print("\n2. 查看ai_providers表:")
    cursor.execute('SELECT id, name, is_active, support_text, text_model FROM ai_providers')
    providers = cursor.fetchall()
    if providers:
        for provider in providers:
            print(f"  - ID: {provider['id']}, 名称: {provider['name']}, 激活: {provider['is_active']}, 支持文本: {provider['support_text']}, 文本模型: {provider['text_model']}")
    else:
        print("  ❌ ai_providers表为空")
    
    conn.close()
    
    print("\n3. 使用AIConfigManager获取active_models:")
    try:
        config_manager = AIConfigManager()
        active_models = config_manager.get_active_models()
        print(f"  获取到的active_models: {active_models}")
        
        if 'text' in active_models:
            text_model = active_models['text']
            print(f"  text模型配置: {text_model}")
        else:
            print("  ❌ 没有找到text模型配置")
    except Exception as e:
        print(f"  ❌ 获取active_models失败: {e}")

if __name__ == '__main__':
    debug_active_models()