<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E船期查询</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        html.dark {
            color-scheme: dark;
        }
        body {
            font-family: 'Inter', 'Helvetica Neue', 'Arial', 'sans-serif';
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .lucide {
            width: 18px;
            height: 18px;
        }
        
        /* 粒子背景Canvas样式 */
        #particle-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }
        
        /* 确保内容在粒子背景之上 */
        main > * {
            position: relative;
            z-index: 1;
        }

        /* 卡片辉光效果 */
        .card-glow {
            position: relative;
            transition: all 0.3s ease;
        }
        .card-glow:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 1rem; /* 16px */
            border: 1px solid transparent;
            background: linear-gradient(120deg, #818cf8, #3b82f6) border-box;
            -webkit-mask: 
                linear-gradient(#fff 0 0) padding-box, 
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .card-glow:hover:before {
            opacity: 0.6;
        }

    </style>
</head>
<body class="bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-300 transition-colors duration-300">

    <div class="flex h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm border-r border-slate-200/80 dark:border-slate-700/60 flex flex-col transition-all duration-300">
            <div class="px-6 py-4 border-b border-slate-200/80 dark:border-slate-700/60 flex items-center space-x-2">
                <i data-lucide="ship-wheel" class="text-blue-500 h-7 w-7"></i>
                <h1 class="text-xl font-bold text-slate-900 dark:text-slate-100">E船期查询</h1>
            </div>
            <div class="p-4">
                <button class="group w-full bg-gradient-to-r from-blue-600 to-blue-500 text-white font-bold py-2.5 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/40 hover:-translate-y-0.5 transform">
                    <i data-lucide="plus" class="mr-2 h-5 w-5 group-hover:rotate-90 transition-transform duration-300"></i>
                    <span>新建查询</span>
                </button>
            </div>
            <nav class="flex-1 px-4 space-y-6">
                <!-- 查询管理 -->
                <div>
                    <h3 class="mb-2 text-xs font-semibold text-slate-500 uppercase tracking-wider">查询管理</h3>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center px-3 py-2 text-sm font-semibold text-blue-600 dark:text-blue-400 border-l-4 border-blue-600 dark:border-blue-400 bg-blue-100 dark:bg-blue-500/10 transition-colors duration-200">
                                <i data-lucide="inbox" class="mr-3"></i>
                                <span>全部</span>
                                <span class="ml-auto bg-blue-200 dark:bg-blue-500/20 text-blue-800 dark:text-blue-300 text-xs font-medium px-2 py-0.5 rounded-full">8</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200">
                                <i data-lucide="loader-2" class="mr-3"></i>
                                <span>进行中</span>
                                <span class="ml-auto bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs font-medium px-2 py-0.5 rounded-full">2</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200">
                                <i data-lucide="check-circle" class="mr-3"></i>
                                <span>已完成</span>
                                <span class="ml-auto bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs font-medium px-2 py-0.5 rounded-full">5</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200">
                                <i data-lucide="x-circle" class="mr-3"></i>
                                <span>查询失败</span>
                                <span class="ml-auto bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs font-medium px-2 py-0.5 rounded-full">1</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- 数据分析 -->
                <div>
                    <h3 class="mb-2 text-xs font-semibold text-slate-500 uppercase tracking-wider">数据分析</h3>
                    <ul class="space-y-1">
                        <li><a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200"><i data-lucide="bar-chart-3" class="mr-3"></i><span>统计报表</span></a></li>
                        <li><a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200"><i data-lucide="trending-up" class="mr-3"></i><span>趋势分析</span></a></li>
                        <li><a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200"><i data-lucide="map" class="mr-3"></i><span>航线地图</span></a></li>
                    </ul>
                </div>
                <!-- 工具 -->
                <div>
                    <h3 class="mb-2 text-xs font-semibold text-slate-500 uppercase tracking-wider">工具</h3>
                     <ul class="space-y-1">
                        <li><a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200"><i data-lucide="download" class="mr-3"></i><span>导出数据</span></a></li>
                        <li><a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200"><i data-lucide="bell" class="mr-3"></i><span>通知设置</span></a></li>
                        <li><a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200"><i data-lucide="settings" class="mr-3"></i><span>系统设置</span></a></li>
                    </ul>
                </div>
            </nav>
            <!-- User Profile & Theme Toggle Section -->
            <div class="mt-auto p-2 border-t border-slate-200/80 dark:border-slate-700/60">
                <div class="flex items-center justify-between w-full">
                    <a href="#" class="flex-1 flex items-center p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700/50 transition-colors duration-200 min-w-0">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 text-white rounded-full flex items-center justify-center font-bold text-sm shadow-md flex-shrink-0">
                            M
                        </div>
                        <div class="ml-3 min-w-0">
                            <p class="text-sm font-semibold text-slate-800 dark:text-slate-200 truncate">马经理</p>
                            <p class="text-xs text-slate-500 dark:text-slate-400 truncate">高级用户</p>
                        </div>
                    </a>
                    <div class="flex items-center flex-shrink-0">
                        <button id="theme-toggle" class="p-2 rounded-full text-slate-500 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors duration-200" title="切换主题">
                            <i id="theme-toggle-dark-icon" data-lucide="moon" class="h-5 w-5"></i>
                            <i id="theme-toggle-light-icon" data-lucide="sun" class="h-5 w-5 hidden"></i>
                        </button>
                        <button class="p-2 rounded-full text-slate-500 dark:text-slate-400 hover:bg-red-100 dark:hover:bg-red-500/10 hover:text-red-600 dark:hover:text-red-400 transition-colors duration-200" title="退出登录">
                            <i data-lucide="log-out" class="h-5 w-5"></i>
                        </button>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col bg-slate-100 dark:bg-slate-900">
            <!-- Page content -->
            <main class="flex-1 p-6 lg:p-8 overflow-y-auto relative flex flex-col">
                <canvas id="particle-canvas"></canvas>
                <!-- Search Card -->
                <div class="card-glow bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm p-4 rounded-2xl shadow-2xl shadow-black/10 dark:shadow-black/50 border border-slate-200/80 dark:border-slate-700/60 transition-all duration-300 hover:-translate-y-1 flex-shrink-0">
                    <div class="flex flex-wrap items-end gap-4">
                        <div class="flex-grow min-w-[200px] lg:max-w-xs">
                            <label for="search-input" class="sr-only">提单号 / 箱号</label>
                            <input type="text" id="search-input" placeholder="提单号/箱号..." class="w-full px-4 py-2 bg-white/50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-900 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                        </div>
                        <div class="flex-grow min-w-[150px]">
                             <label for="status-filter" class="sr-only">状态</label>
                             <select id="status-filter" class="w-full px-4 py-2 bg-white/50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-900 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                                 <option value="all">全部状态</option>
                                 <option value="已完成">已完成</option>
                                 <option value="查询中">查询中</option>
                                 <option value="查询失败">查询失败</option>
                             </select>
                        </div>
                        <div class="flex-grow min-w-[150px]">
                             <label for="eta-filter" class="sr-only">预计到港时间</label>
                             <input type="date" id="eta-filter" class="w-full px-4 py-2 bg-white/50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-900 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                        </div>
                        <div class="flex items-center gap-2">
                            <button id="search-button" class="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/40 transform hover:-translate-y-0.5 active:scale-95 flex items-center justify-center">
                                <i data-lucide="search" class="mr-2 h-4 w-4"></i>
                                <span>搜索</span>
                            </button>
                            <button id="reset-button" class="bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 text-slate-800 dark:text-slate-200 font-bold py-2 px-6 rounded-lg transition-all duration-300 transform active:scale-95 flex items-center justify-center">
                                <i data-lucide="rotate-ccw" class="mr-2 h-4 w-4"></i>
                                <span>重置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Query Tasks List -->
                <div class="card-glow mt-8 bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm rounded-2xl shadow-2xl shadow-black/10 dark:shadow-black/50 border border-slate-200/80 dark:border-slate-700/60 transition-all duration-300 hover:-translate-y-1 flex flex-col flex-grow min-h-0">
                    <div class="px-6 py-4 flex-shrink-0 flex flex-col sm:flex-row justify-between items-start sm:items-center border-b border-slate-200/80 dark:border-slate-700/60">
                        <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100">查询任务列表</h3>
                        <div class="flex items-center space-x-2 mt-2 sm:mt-0">
                            <button class="flex items-center text-sm font-medium text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 px-3 py-1.5 rounded-md transition-colors">
                                <i data-lucide="refresh-cw" class="mr-2 h-4 w-4"></i>
                                刷新
                            </button>
                            <button class="flex items-center text-sm font-medium text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 px-3 py-1.5 rounded-md transition-colors">
                                <i data-lucide="upload" class="mr-2 h-4 w-4"></i>
                                导出
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-auto">
                        <table class="w-full text-sm text-left text-slate-600 dark:text-slate-400">
                            <thead class="sticky top-0 bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">提单号/箱号</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">船公司</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">预计到港时间</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">物流节点</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">凭证截图</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">创建时间</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">更新时间</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-slate-200/80 dark:divide-slate-700/60">
                                <!-- Table rows will be generated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        lucide.createIcons();

        // --- Elements ---
        const themeToggleBtn = document.getElementById('theme-toggle');
        const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
        const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');
        const tableBody = document.querySelector('tbody');
        const searchInput = document.getElementById('search-input');
        const statusFilter = document.getElementById('status-filter');
        const etaFilter = document.getElementById('eta-filter');
        const searchButton = document.getElementById('search-button');
        const resetButton = document.getElementById('reset-button');

        // --- Table Data ---
        const tableData = [
            { trackingNumber: 'MEDUX0610822', company: 'MSC(Mediterranean Shipping Company / 地中海航运)', status: '已完成', eta: '2025-08-31', nodes: '10条', voucher: true, createdTime: '2025-08-11', updatedTime: '2025-08-11' },
            { trackingNumber: 'MEDUX0610823', company: 'MSC(Mediterranean Shipping Company / 地中海航运)', status: '查询中', eta: '2025-08-31', nodes: '11条', voucher: true, createdTime: '2025-08-07', updatedTime: '2025-08-07' },
            { trackingNumber: 'COSCO482199A', company: 'COSCO (中远海运)', status: '查询失败', eta: '2025-09-01', nodes: '8条', voucher: true, createdTime: '2025-08-06', updatedTime: '2025-08-06' },
            { trackingNumber: 'MAEU511092B', company: 'Maersk (马士基)', status: '已完成', eta: '2025-08-28', nodes: '12条', voucher: true, createdTime: '2025-08-06', updatedTime: '2025-08-06' },
            { trackingNumber: 'HAPU1234567', company: 'Hapag-Lloyd (赫伯罗特)', status: '已完成', eta: '2025-09-05', nodes: '9条', voucher: true, createdTime: '2025-08-06', updatedTime: '2025-08-06' },
            { trackingNumber: 'CMDU9876543', company: 'CMA CGM (达飞海运)', status: '已完成', eta: '2025-09-10', nodes: '15条', voucher: true, createdTime: '2025-08-05', updatedTime: '2025-08-05' },
            { trackingNumber: 'EVER1122334', company: 'Evergreen (长荣海运)', status: '查询中', eta: '2025-09-12', nodes: '2条', voucher: true, createdTime: '2025-08-05', updatedTime: '2025-08-05' },
        ];

        // --- Functions ---
        function getStatusBadge(status) {
            const isDark = document.documentElement.classList.contains('dark');
            switch (status) {
                case '已完成':
                    return isDark
                        ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-500/10 text-green-300"><svg class="w-2 h-2 mr-1.5 text-green-400" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>已完成</span>`
                        : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><svg class="w-2 h-2 mr-1.5 text-green-500" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>已完成</span>`;
                case '查询中':
                     return isDark
                        ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-300"><svg class="w-2 h-2 mr-1.5 text-yellow-400" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询中...</span>`
                        : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"><svg class="w-2 h-2 mr-1.5 text-yellow-500" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询中...</span>`;
                case '查询失败':
                    return isDark
                        ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500/10 text-red-300"><svg class="w-2 h-2 mr-1.5 text-red-400" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询失败</span>`
                        : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"><svg class="w-2 h-2 mr-1.5 text-red-500" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询失败</span>`;
            }
        }

        function renderTable() {
            const isDark = document.documentElement.classList.contains('dark');
            const searchTerm = searchInput.value.trim().toLowerCase();
            const statusTerm = statusFilter.value;
            const etaTerm = etaFilter.value;
            
            let filteredData = tableData;

            if (searchTerm) {
                filteredData = filteredData.filter(row => row.trackingNumber.toLowerCase().includes(searchTerm));
            }
            if (statusTerm && statusTerm !== 'all') {
                filteredData = filteredData.filter(row => row.status === statusTerm);
            }
            if (etaTerm) {
                filteredData = filteredData.filter(row => row.eta === etaTerm);
            }

            tableBody.innerHTML = ''; // Clear existing table rows

            const rowClass = isDark ? 'hover:bg-slate-700/50' : 'bg-white hover:bg-slate-50';
            const mainTextColor = isDark ? 'text-slate-300' : 'text-slate-700';
            const subTextColor = isDark ? 'text-slate-500' : 'text-slate-500';
            const monoTextColor = isDark ? 'text-slate-200' : 'text-slate-900';
            const linkColor = isDark ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800';

            filteredData.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.className = `${rowClass} transition-colors duration-200`;
                
                tr.innerHTML = `
                    <td class="px-6 py-4 ${mainTextColor}">${index + 1}</td>
                    <td class="px-6 py-4 font-mono ${monoTextColor}">${row.trackingNumber}</td>
                    <td class="px-6 py-4 ${mainTextColor}">${row.company}</td>
                    <td class="px-6 py-4">${getStatusBadge(row.status)}</td>
                    <td class="px-6 py-4 ${mainTextColor}">${row.eta}</td>
                    <td class="px-6 py-4 ${mainTextColor}">${row.nodes}</td>
                    <td class="px-6 py-4"><a href="#" class="${linkColor} font-medium">查看截图</a></td>
                    <td class="px-6 py-4 ${subTextColor}">${row.createdTime}</td>
                    <td class="px-6 py-4 ${subTextColor}">${row.updatedTime}</td>
                `;
                tableBody.appendChild(tr);
            });
        }

        function setTheme(isDark) {
            if (isDark) {
                document.documentElement.classList.add('dark');
                themeToggleLightIcon.classList.remove('hidden');
                themeToggleDarkIcon.classList.add('hidden');
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.classList.remove('dark');
                themeToggleLightIcon.classList.add('hidden');
                themeToggleDarkIcon.classList.remove('hidden');
                localStorage.setItem('theme', 'light');
            }
            renderTable(); // Re-render table on theme change
        }

        // --- Event Listeners ---
        themeToggleBtn.addEventListener('click', () => {
            setTheme(!document.documentElement.classList.contains('dark'));
        });
        
        searchButton.addEventListener('click', renderTable);
        searchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                renderTable();
            }
        });
        resetButton.addEventListener('click', () => {
            searchInput.value = '';
            statusFilter.value = 'all';
            etaFilter.value = '';
            renderTable();
        });


        // --- Initial Load ---
        const initialThemeIsDark = localStorage.getItem('theme') === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches);
        setTheme(initialThemeIsDark);
        

        // --- Constellation Particle Animation ---
        const canvas = document.getElementById('particle-canvas');
        const ctx = canvas.getContext('2d');
        let particles = [];
        const particleOptions = {
            light: {
                particleColor: "rgba(100, 116, 139, 0.5)", // slate-500
                lineColor: "rgba(100, 116, 139, 0.2)", // slate-500
            },
            dark: {
                particleColor: "rgba(148, 163, 184, 0.5)", // slate-400
                lineColor: "rgba(148, 163, 184, 0.1)", // slate-400
            },
            particleAmount: 80,
            defaultRadius: 2.5,
            variantRadius: 1.5,
            defaultSpeed: 0.3,
            linkRadius: 220,
        };

        function getCurrentThemeColors() {
            return document.documentElement.classList.contains('dark') ? particleOptions.dark : particleOptions.light;
        }

        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        class Particle {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.radius = particleOptions.defaultRadius + Math.random() * particleOptions.variantRadius;
                this.speedX = (Math.random() - 0.5) * particleOptions.defaultSpeed;
                this.speedY = (Math.random() - 0.5) * particleOptions.defaultSpeed;
            }

            draw() {
                const colors = getCurrentThemeColors();
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = colors.particleColor;
                ctx.fill();
                ctx.closePath();
            }

            update() {
                this.x += this.speedX;
                this.y += this.speedY;

                if (this.x < 0 || this.x > canvas.width) this.speedX *= -1;
                if (this.y < 0 || this.y > canvas.height) this.speedY *= -1;
            }
        }

        function createParticles() {
            particles = [];
            for (let i = 0; i < particleOptions.particleAmount; i++) {
                particles.push(new Particle());
            }
        }

        function linkParticles() {
            const colors = getCurrentThemeColors();
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < particleOptions.linkRadius) {
                        const opacity = 1 - (distance / particleOptions.linkRadius);
                        ctx.strokeStyle = colors.lineColor.replace(/,\s*\d*\.?\d*\)/, `, ${opacity * 0.5})`);
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();
                        ctx.closePath();
                    }
                }
            }
        }

        function animateParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });
            linkParticles();
            requestAnimationFrame(animateParticles);
        }
        
        createParticles();
        animateParticles();

    </script>
</body>
</html>
