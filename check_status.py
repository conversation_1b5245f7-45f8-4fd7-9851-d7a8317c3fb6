#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有货运记录的当前状态
"""

import sqlite3
from datetime import datetime

def check_all_shipment_status():
    """检查所有货运记录的状态"""
    print("=== 检查所有货运记录状态 ===\n")
    
    # 检查所有货运记录
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    
    # 按状态分组统计
    cursor.execute("""
        SELECT status, COUNT(*) as count
        FROM shipment_records 
        GROUP BY status
        ORDER BY count DESC
    """)
    
    status_summary = cursor.fetchall()
    print("状态统计:")
    for status, count in status_summary:
        print(f"  {status}: {count} 条记录")
    print()
    
    # 显示最近的记录详情
    cursor.execute("""
        SELECT id, bill_of_lading, container_number, carrier_company, status, 
               created_at, updated_at, remarks
        FROM shipment_records 
        ORDER BY updated_at DESC 
        LIMIT 10
    """)
    
    recent_records = cursor.fetchall()
    
    print("最近更新的 10 条记录:")
    for record in recent_records:
        record_id, bill, container, carrier, status, created, updated, remarks = record
        tracking_number = bill or container or "无"
        print(f"  ID: {record_id}")
        print(f"  跟踪号: {tracking_number}")
        print(f"  船公司: {carrier or '无'}")
        print(f"  当前状态: {status}")
        print(f"  创建时间: {created}")
        print(f"  更新时间: {updated}")
        if remarks and 'ID:' in remarks:
            task_id = remarks.split('ID:')[1].strip() if 'ID:' in remarks else '无'
            print(f"  关联任务: {task_id}")
        print()
    
    conn.close()
    
    # 检查对应的任务状态
    print("=== 检查对应任务状态 ===\n")
    task_conn = sqlite3.connect('db/task_queue.db')
    task_cursor = task_conn.cursor()
    
    # 按任务状态和阶段分组
    task_cursor.execute("""
        SELECT task_stage, status, COUNT(*) as count
        FROM task_queue 
        GROUP BY task_stage, status
        ORDER BY task_stage, count DESC
    """)
    
    task_summary = task_cursor.fetchall()
    print("任务状态统计:")
    for stage, status, count in task_summary:
        print(f"  {stage} - {status}: {count} 个任务")
    print()
    
    # 显示最近的任务
    task_cursor.execute("""
        SELECT id, tracking_number, task_stage, status, created_at, 
               started_at, completed_at, error_message
        FROM task_queue 
        ORDER BY created_at DESC 
        LIMIT 10
    """)
    
    recent_tasks = task_cursor.fetchall()
    
    print("最近的 10 个任务:")
    for task in recent_tasks:
        task_id, tracking, stage, status, created, started, completed, error = task
        print(f"  任务ID: {task_id}")
        print(f"  跟踪号: {tracking}")
        print(f"  阶段: {stage}")
        print(f"  状态: {status}")
        print(f"  创建: {created}")
        print(f"  开始: {started or '未开始'}")
        print(f"  完成: {completed or '未完成'}")
        if error:
            print(f"  错误: {error}")
        print()
    
    task_conn.close()

if __name__ == "__main__":
    check_all_shipment_status()