#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
船公司数据库查询模块
提供基于数据库的船公司信息查询功能，替代硬编码的 COMPANY_DATA 字典
"""

import sqlite3
import re
import os
from typing import Dict, List, Optional, Tuple
from functools import lru_cache

class CarrierDatabase:
    """船公司数据库管理类"""
    
    def __init__(self, db_path: str = None):
        """初始化数据库连接"""
        if db_path is None:
            # 默认数据库路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(os.path.dirname(current_dir), 'db', 'carriers.db')
        
        self.db_path = db_path
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """确保数据库文件存在"""
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(
                f"船公司数据库不存在: {self.db_path}\n"
                "请先运行 db/init_carrier_database.py 和 db/migrate_carrier_data.py"
            )
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn
    
    @lru_cache(maxsize=128)
    def get_all_carriers(self) -> List[Dict]:
        """获取所有活跃的船公司信息（带缓存）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.id, c.company_name, c.company_code, c.international_site, 
                   c.chinese_site, c.tracking_site, c.input_element_id, c.search_button_id
            FROM carriers c
            WHERE c.is_active = 1
            ORDER BY c.company_name
        """)
        
        carriers = []
        for row in cursor.fetchall():
            carrier = {
                'id': row['id'],
                'company_name': row['company_name'],
                'company_code': row['company_code'],
                'international_site': row['international_site'],
                'chinese_site': row['chinese_site'],
                'tracking_site': row['tracking_site'],
                'input_element_id': row['input_element_id'],
                'search_button_id': row['search_button_id'],
                'scac_prefixes': self._get_scac_prefixes(row['id']),
                'bl_patterns': self._get_bl_patterns(row['id'])
            }
            carriers.append(carrier)
        
        conn.close()
        return carriers
    
    def _get_scac_prefixes(self, carrier_id: int) -> List[str]:
        """获取指定船公司的SCAC前缀列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT scac_prefix FROM carrier_scac_prefixes 
            WHERE carrier_id = ? 
            ORDER BY scac_prefix
        """, (carrier_id,))
        
        prefixes = [row['scac_prefix'] for row in cursor.fetchall()]
        conn.close()
        return prefixes
    
    def _get_bl_patterns(self, carrier_id: int) -> List[str]:
        """获取指定船公司的提单号规则列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT pattern FROM carrier_bl_patterns 
            WHERE carrier_id = ? 
            ORDER BY id
        """, (carrier_id,))
        
        patterns = [row['pattern'] for row in cursor.fetchall()]
        conn.close()
        return patterns
    
    def get_company_info(self, input_str: str) -> Optional[Dict]:
        """根据输入字符串查找匹配的船公司信息"""
        input_str = input_str.strip().upper()
        
        # 获取所有船公司数据
        carriers = self.get_all_carriers()
        
        for carrier in carriers:
            # 检查SCAC前缀匹配
            for prefix in carrier['scac_prefixes']:
                if input_str.startswith(prefix.upper()):
                    return self._format_company_result(carrier)
            
            # 检查提单号规则匹配
            for pattern in carrier['bl_patterns']:
                try:
                    if re.match(pattern, input_str):
                        return self._format_company_result(carrier)
                except re.error:
                    # 忽略无效的正则表达式
                    continue
        
        return None
    
    def _format_company_result(self, carrier: Dict) -> Dict:
        """格式化查询结果"""
        return {
            'company': carrier['company_name'],
            'company_code': carrier['company_code'],
            'international_site': carrier['international_site'],
            'chinese_site': carrier['chinese_site'],
            'tracking_site': carrier['tracking_site'],
            'input_element_id': carrier.get('input_element_id'),
            'search_button_id': carrier.get('search_button_id')
        }
    
    def search_carriers(self, keyword: str) -> List[Dict]:
        """按关键词搜索船公司"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.id, c.company_name, c.company_code, c.international_site, 
                   c.chinese_site, c.tracking_site, c.input_element_id, c.search_button_id
            FROM carriers c
            WHERE c.is_active = 1 
            AND (c.company_name LIKE ? OR c.company_code LIKE ?)
            ORDER BY c.company_name
        """, (f'%{keyword}%', f'%{keyword}%'))
        
        results = []
        for row in cursor.fetchall():
            carrier = {
                'id': row['id'],
                'company_name': row['company_name'],
                'company_code': row['company_code'],
                'international_site': row['international_site'],
                'chinese_site': row['chinese_site'],
                'tracking_site': row['tracking_site'],
                'input_element_id': row['input_element_id'],
                'search_button_id': row['search_button_id'],
                'scac_prefixes': self._get_scac_prefixes(row['id']),
                'bl_patterns': self._get_bl_patterns(row['id'])
            }
            results.append(carrier)
        
        conn.close()
        return results
    
    def add_carrier(self, company_name: str, company_code: str, 
                   international_site: str, chinese_site: str, 
                   tracking_site: str, input_element_id: str = None,
                   search_button_id: str = None, scac_prefixes: List[str] = None, 
                   bl_patterns: List[str] = None) -> int:
        """添加新的船公司"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 插入船公司主记录
            cursor.execute("""
                INSERT INTO carriers (company_name, company_code, international_site, 
                                    chinese_site, tracking_site, input_element_id, search_button_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (company_name, company_code, international_site, chinese_site, tracking_site, input_element_id, search_button_id))
            
            carrier_id = cursor.lastrowid
            
            # 插入SCAC前缀
            if scac_prefixes:
                for scac in scac_prefixes:
                    cursor.execute("""
                        INSERT INTO carrier_scac_prefixes (carrier_id, scac_prefix)
                        VALUES (?, ?)
                    """, (carrier_id, scac))
            
            # 插入提单号规则
            if bl_patterns:
                for pattern in bl_patterns:
                    cursor.execute("""
                        INSERT INTO carrier_bl_patterns (carrier_id, pattern)
                        VALUES (?, ?)
                """, (carrier_id, pattern))
            
            conn.commit()
            
            # 清除缓存
            self.get_all_carriers.cache_clear()
            
            return carrier_id
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def update_carrier(self, carrier_id: int, **kwargs) -> bool:
        """更新船公司信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 更新主表信息
            update_fields = []
            update_values = []
            
            for field in ['company_name', 'company_code', 'international_site', 
                         'chinese_site', 'tracking_site', 'input_element_id', 'search_button_id']:
                if field in kwargs:
                    update_fields.append(f"{field} = ?")
                    update_values.append(kwargs[field])
            
            if update_fields:
                update_values.append(carrier_id)
                cursor.execute(f"""
                    UPDATE carriers SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, update_values)
            
            # 更新SCAC前缀
            if 'scac_prefixes' in kwargs:
                cursor.execute("DELETE FROM carrier_scac_prefixes WHERE carrier_id = ?", (carrier_id,))
                for scac in kwargs['scac_prefixes']:
                    cursor.execute("""
                        INSERT INTO carrier_scac_prefixes (carrier_id, scac_prefix)
                        VALUES (?, ?)
                    """, (carrier_id, scac))
            
            # 更新提单号规则
            if 'bl_patterns' in kwargs:
                cursor.execute("DELETE FROM carrier_bl_patterns WHERE carrier_id = ?", (carrier_id,))
                for pattern in kwargs['bl_patterns']:
                    cursor.execute("""
                        INSERT INTO carrier_bl_patterns (carrier_id, pattern)
                        VALUES (?, ?)
                    """, (carrier_id, pattern))
            
            conn.commit()
            
            # 清除缓存
            self.get_all_carriers.cache_clear()
            
            return True
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def delete_carrier(self, carrier_id: int) -> bool:
        """删除船公司（软删除）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                UPDATE carriers SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (carrier_id,))
            
            conn.commit()
            
            # 清除缓存
            self.get_all_carriers.cache_clear()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def get_statistics(self) -> Dict:
        """获取数据库统计信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 统计各种数据
        cursor.execute("SELECT COUNT(*) FROM carriers WHERE is_active = 1")
        active_carriers = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM carriers WHERE is_active = 0")
        inactive_carriers = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM carrier_scac_prefixes")
        total_scac = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM carrier_bl_patterns")
        total_patterns = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'active_carriers': active_carriers,
            'inactive_carriers': inactive_carriers,
            'total_scac_prefixes': total_scac,
            'total_bl_patterns': total_patterns
        }

# 全局数据库实例
_carrier_db = None

def get_carrier_database() -> CarrierDatabase:
    """获取全局船公司数据库实例"""
    global _carrier_db
    if _carrier_db is None:
        _carrier_db = CarrierDatabase()
    return _carrier_db

# 兼容性函数，保持与原 carrier_lookup.py 的接口一致
def get_company_info(input_str: str) -> Optional[Dict]:
    """根据输入字符串查找匹配的船公司信息（兼容性函数）"""
    db = get_carrier_database()
    return db.get_company_info(input_str)

if __name__ == "__main__":
    # 测试数据库功能
    db = CarrierDatabase()
    
    print("=== 船公司数据库测试 ===")
    
    # 测试统计信息
    stats = db.get_statistics()
    print(f"活跃船公司: {stats['active_carriers']}")
    print(f"SCAC前缀总数: {stats['total_scac_prefixes']}")
    print(f"提单规则总数: {stats['total_bl_patterns']}")
    
    # 测试查询功能
    test_inputs = ["MEDUVS935363", "MAEU1234567", "CMDU9876543"]
    
    print("\n=== 查询测试 ===")
    for test_input in test_inputs:
        result = db.get_company_info(test_input)
        if result:
            print(f"输入: {test_input}")
            print(f"公司: {result['company']}")
            print(f"追踪网站: {result['tracking_site']}")
            print("-" * 50)
        else:
            print(f"输入: {test_input} - 未找到匹配")
    
    print("\n数据库功能测试完成！")