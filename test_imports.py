#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入测试脚本
"""

print("测试导入...")

try:
    from scheduled_task_processor import ScheduledTaskProcessor
    print("✅ scheduled_task_processor 导入成功")
except Exception as e:
    print(f"❌ scheduled_task_processor 导入失败: {e}")

try:
    from scraping_executor import ScrapingExecutor
    print("✅ scraping_executor 导入成功")
except Exception as e:
    print(f"❌ scraping_executor 导入失败: {e}")

try:
    from ai_analysis_executor import AIAnalysisExecutor
    print("✅ ai_analysis_executor 导入成功")
except Exception as e:
    print(f"❌ ai_analysis_executor 导入失败: {e}")

print("导入测试完成！")