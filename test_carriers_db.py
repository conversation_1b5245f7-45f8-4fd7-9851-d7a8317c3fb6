#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试carriers数据库查询功能
"""

import sqlite3
import os

def test_carriers_database():
    """测试carriers数据库"""
    db_path = 'db/carriers.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    print(f"数据库文件路径: {os.path.abspath(db_path)}")
    print(f"数据库文件大小: {os.path.getsize(db_path)} bytes")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("\n数据库中的表:")
        for table in tables:
            print(f"  - {table['name']}")
        
        # 查看carriers表结构
        cursor.execute("PRAGMA table_info(carriers);")
        columns = cursor.fetchall()
        print("\ncarriers表结构:")
        for col in columns:
            print(f"  - {col['name']}: {col['type']}")
        
        # 查看carriers表数据数量
        cursor.execute("SELECT COUNT(*) as count FROM carriers;")
        count = cursor.fetchone()['count']
        print(f"\ncarriers表记录数: {count}")
        
        # 查看前5条记录
        cursor.execute("""
            SELECT id, company_name, company_code, tracking_site, 
                   input_element_id, search_button_id
            FROM carriers 
            WHERE is_active = 1 
            LIMIT 5
        """)
        records = cursor.fetchall()
        print("\n前5条活跃记录:")
        for record in records:
            print(f"  ID: {record['id']}")
            print(f"  公司: {record['company_name']}")
            print(f"  代码: {record['company_code']}")
            print(f"  追踪网站: {record['tracking_site']}")
            print(f"  输入框ID: {record['input_element_id']}")
            print(f"  搜索按钮ID: {record['search_button_id']}")
            print("  ---")
        
        # 测试查询SCAC前缀
        cursor.execute("""
            SELECT c.company_name, sp.scac_prefix
            FROM carriers c
            JOIN carrier_scac_prefixes sp ON c.id = sp.carrier_id
            WHERE c.company_name LIKE '%MSC%'
        """)
        msc_prefixes = cursor.fetchall()
        print("\nMSC的SCAC前缀:")
        for prefix in msc_prefixes:
            print(f"  {prefix['company_name']}: {prefix['scac_prefix']}")
        
        # 测试查询提单号规则
        cursor.execute("""
            SELECT c.company_name, bp.pattern
            FROM carriers c
            JOIN carrier_bl_patterns bp ON c.id = bp.carrier_id
            WHERE c.company_name LIKE '%MSC%'
        """)
        msc_patterns = cursor.fetchall()
        print("\nMSC的提单号规则:")
        for pattern in msc_patterns:
            print(f"  {pattern['company_name']}: {pattern['pattern']}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库查询错误: {e}")

def test_company_lookup():
    """测试公司查询功能"""
    print("\n=== 测试公司查询功能 ===")
    
    # 直接使用数据库查询
    try:
        from utils.carrier_database import get_company_info
        
        test_inputs = ["MEDUVS935363", "MAEU1234567", "CMDU9876543"]
        
        for test_input in test_inputs:
            result = get_company_info(test_input)
            print(f"\n输入: {test_input}")
            if result:
                print(f"  公司: {result['company']}")
                print(f"  追踪网站: {result['tracking_site']}")
                print(f"  输入框ID: {result.get('input_element_id', 'None')}")
                print(f"  搜索按钮ID: {result.get('search_button_id', 'None')}")
            else:
                print("  未找到匹配")
                
    except Exception as e:
        print(f"查询功能测试错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_carriers_database()
    test_company_lookup()