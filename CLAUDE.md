# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在处理本仓库代码时提供指导。

## 项目概述

Container Helper 是一个基于 AI 驱动的智能集装箱货运跟踪系统，集成了现代化的桌面界面、自动化网页抓取、智能数据分析和任务管理功能。系统能够自动从各大船公司网站获取货运信息，并通过 AI 技术进行智能分析和处理。

## 系统架构

### 核心组件
- **桌面应用程序** - 基于 PySide6 的现代化用户界面
- **AI 分析引擎** - 集成多种 AI 模型的视觉和文本分析
- **网页自动化** - 基于 Playwright 的智能网页操作
- **数据管理** - SQLite 数据库集群管理，支持连接池
- **任务调度** - 异步任务处理和队列管理
- **REST API 服务** - 基于 FastAPI 的外部集成接口
- **渐进式网页应用** - 现代化 Web 前端界面

### 主要技术栈
- **前端界面**: PySide6 (桌面端), HTML/CSS/JS (网页端)
- **后端核心**: Python 3.8+, FastAPI, SQLite
- **自动化操作**: Playwright (浏览器自动化)
- **AI 服务**: 多提供商支持 (默认使用豆包 AI)
- **任务处理**: 异步/多线程队列管理

## 开发环境配置

### 依赖项要求

#### 核心依赖 (requirements.txt)
```bash
# GUI 框架
PySide6>=6.6.0

# 网页自动化
playwright>=1.40.0

# AI 集成
openai>=1.0.0

# 网页解析
beautifulsoup4>=4.12.0
lxml>=4.9.0

# 数据处理
pandas>=2.0.0
openpyxl>=3.1.0

# HTTP/网络
requests>=2.31.0
urllib3>=2.0.0

# 图像处理
Pillow>=10.0.0

# 工具库
python-dateutil>=2.8.0
PyYAML>=6.0
colorama>=0.4.6
```

#### API 服务依赖 (api/api_requirements.txt)
```bash
# Web 框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据验证
pydantic==2.5.0

# HTTP 客户端
httpx==0.25.2
requests==2.31.0

# WebSocket 支持
websockets==12.0

# 异步文件操作
aiofiles==23.2.1

# 工具库
python-dateutil==2.8.2
orjson==3.9.10
python-dotenv==1.0.0
coloredlogs==15.0.1
psutil==5.9.6
```

### 环境初始化

1. **安装 Python 依赖**
   ```bash
   # 核心依赖
   pip install -r requirements.txt
   
   # API 服务依赖
   pip install -r api/api_requirements.txt
   
   # 安装 Playwright 浏览器
   playwright install chromium
   ```

2. **初始化数据库**
   ```bash
   # 按顺序初始化所有数据库
   python db/init_database.py           # 核心数据库
   python db/init_shipment_database.py  # 货运记录数据库
   python db/init_task_queue.py         # 任务队列数据库
   python db/init_carrier_database.py   # 承运人信息数据库
   python db/init_ai_config.py          # AI 配置数据库
   ```

3. **配置 AI 服务**
   - 使用桌面应用中的 AI 模型管理界面
   - 配置豆包 AI（推荐）或其他提供商
   - API 密钥和配置存储在 `db/ai_config.db` 中

## 常用命令

### 桌面应用程序
```bash
# 启动主桌面应用
python app.py

# 承运人管理界面
python carrier_management_ui.py

# AI 模型管理
python ai_model_management_ui.py

# 数据库管理工具
python database_management_ui.py

# 查看 AI 调用日志和统计
python ai_log_viewer_ui.py
```

### API 服务
```bash
# 启动 API 服务（推荐方式）
python api/start_api.py

# 备选方式：直接使用 uvicorn
uvicorn api.main:app --host 127.0.0.1 --port 8080 --reload

# 测试 API 功能
python api/test_api.py
```

### 开发与测试
```bash
# 运行完整工作流测试
python test_full_workflow.py

# 测试 AI 集成
python test_ai_module.py

# 测试数据库操作
python test_db_logging.py

# 测试承运人数据流
python test_carrier_data_flow.py

# 调试任务完成流程
python debug_task_completion.py

# 验证北京时间处理
python verify_beijing_time_fix.py
```

### 任务管理
```bash
# 创建测试任务
python create_tasks.py

# 调试任务处理
python debug_task_completion.py

# 手动任务处理测试
python test_manual_task_processing.py

# 自动任务处理测试
python test_auto_task_processing.py
```

### 数据库操作
```bash
# 检查数据库架构
python check_db_schema.py

# 检查触发器状态
python check_trigger_status.py

# 迁移承运人数据
python db/migrate_carrier_data.py

# 数据库日志工具
python db_logger.py
```

## 应用程序架构

### 数据流程
1. **用户输入** → 桌面界面或 API 端点
2. **承运人检测** → 自动识别船公司
3. **任务创建** → 货运跟踪任务入队
4. **网页自动化** → Playwright 抓取承运人网站
5. **AI 分析** → 多模型处理截图和文本内容
6. **数据存储** → 结构化数据保存至 SQLite
7. **界面更新** → 桌面应用实时状态更新

### 数据库架构
- **shipment_records.db** - 主要货运记录和物流时间节点
- **task_queue.db** - 异步任务管理和状态跟踪
- **carriers.db** - 船公司信息和配置数据
- **ai_config.db** - AI 提供商设置和 API 密钥
- **ai_call_logs.db** - AI 服务使用跟踪和性能监控

### 关键模块

#### 核心组件
- `app.py` - 主桌面应用程序入口
- `shipment_manager.py` - 货运记录管理
- `task_manager.py` - 任务队列操作
- `task_processor.py` - 后台任务执行
- `task_executor.py` - 单个任务执行逻辑

#### AI 集成
- `ai/client.py` - AI 客户端管理和配置
- `ai/vision_analyzer.py` - 图像/截图分析
- `ai/text_analyzer.py` - 文本内容处理
- `ai/prompt_config.py` - AI 提示词管理

#### 数据管理
- `utils/carrier_lookup.py` - 承运人识别逻辑
- `utils/carrier_database.py` - 承运人数据操作
- `utils/file_manager.py` - 文件操作和组织
- `db/connection_manager.py` - 数据库连接池管理

#### Web 界面
- `api/main.py` - FastAPI 应用程序
- `api/routers/` - REST API 端点
- `web/` - 渐进式网页应用前端

## 配置说明

### 数据库配置
所有数据库使用 SQLite 并支持连接池。连接管理器自动处理：
- 连接复用和池化
- 事务管理
- 北京时间处理
- 错误恢复

### AI 提供商配置
通过桌面应用配置 AI 提供商：
1. 从系统菜单打开「AI 模型管理」
2. 添加提供商（推荐豆包 AI）
3. 配置 API 密钥和模型设置
4. 保存前测试连接

### 环境变量
```bash
# API 服务
API_HOST=127.0.0.1
API_PORT=8080
LOG_LEVEL=info
API_RELOAD=true

# 承运人数据库
CARRIER_USE_DATABASE=true

# 任务处理（自动处理）
# 任务处理器与桌面应用自动启动
```

## Web 访问

### API 服务
- **主要 API**: http://127.0.0.1:8080/
- **API 文档**: http://127.0.0.1:8080/docs
- **备选文档**: http://127.0.0.1:8080/redoc
- **健康检查**: http://127.0.0.1:8080/health

### 渐进式网页应用
当 API 服务运行时，可访问 Web 界面：
- **网页应用**: http://127.0.0.1:8080/
- **添加 `?v=版本号` 参数强制刷新缓存**

## 主要功能特性

### 自动化工作流
1. **智能承运人检测** - 基于跟踪号自动识别
2. **网页抓取** - 智能导航承运人网站
3. **AI 分析** - 截图和内容分析进行数据提取
4. **实时更新** - 实时任务状态和进度监控
5. **数据导出** - 支持多种格式的货运数据导出

### 数据库管理
- **连接池** - 高效的数据库资源管理
- **事务安全** - ACID 兼容性和适当的错误处理
- **时区处理** - 所有操作一致使用北京时间 (UTC+8)
- **数据完整性** - 外键约束和验证

### 任务处理
- **异步处理** - 非阻塞后台任务执行
- **优先级队列** - 可配置的任务优先级
- **错误恢复** - 指数退避自动重试
- **进度跟踪** - 实时状态更新

## 故障排除

### 常见问题

1. **数据库连接错误**
   - 确保所有数据库已初始化
   - 检查文件权限
   - 通过桌面应用验证连接池状态

2. **AI 服务故障**
   - 在 AI 模型管理中验证 API 密钥
   - 运行任务前测试连接
   - 检查网络连接

3. **Playwright 浏览器问题**
   ```bash
   # 重新安装浏览器
   playwright install chromium
   
   # 清除浏览器缓存
   playwright uninstall
   playwright install
   ```

4. **任务处理卡顿**
   - 在桌面应用中检查任务处理器状态
   - 查看 `logs/` 目录中的日志
   - 通过系统菜单重启任务处理器

### 日志文件
- `logs/api.log` - API 服务日志
- `logs/api_error.log` - API 错误日志
- 桌面应用的控制台输出

### 性能监控
- 使用工具菜单中的「连接池状态」
- 通过「AI 调用日志」监控 AI 调用统计
- 在桌面界面检查任务完成率

## 安全注意事项

- API 密钥安全存储在 SQLite 数据库中
- 源代码中无硬编码凭据
- 默认仅本地 API 服务 (127.0.0.1)
- 文件访问限制在项目目录内
- 截图数据本地存储，文件结构有序组织

## 中文支持

### 界面语言
- 桌面应用全中文界面
- 错误消息和提示均为中文
- 数据库字段支持中文内容
- AI 分析结果支持中文解析

### 时区处理
- 统一使用北京时间 (东八区 UTC+8)
- 所有时间戳自动转换为本地时间
- 数据库触发器确保时间一致性