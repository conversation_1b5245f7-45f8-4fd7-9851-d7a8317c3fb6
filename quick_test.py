#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动测试脚本
"""

def test_basic_imports():
    """测试基本导入"""
    print("1. 测试基本导入...")
    
    try:
        import sys
        import os
        from datetime import datetime
        print("√ 基础模块导入成功")
        return True
    except Exception as e:
        print(f"× 基础模块导入失败: {e}")
        return False

def test_ai_modules():
    """测试AI模块"""
    print("2. 测试AI模块...")
    
    try:
        from ai.text_analyzer import TextAnalyzer
        analyzer = TextAnalyzer()
        print("√ TextAnalyzer创建成功")
        return True
    except Exception as e:
        print(f"× TextAnalyzer导入/创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_executors():
    """测试执行器"""
    print("3. 测试执行器...")
    
    try:
        from scraping_executor import ScrapingExecutor
        from ai_analysis_executor import AIAnalysisExecutor
        
        scraper = ScrapingExecutor()
        analyzer = AIAnalysisExecutor()
        print("√ 执行器创建成功")
        return True
    except Exception as e:
        print(f"× 执行器导入/创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scheduled_processor():
    """测试定时任务处理器"""
    print("4. 测试定时任务处理器...")
    
    try:
        from scheduled_task_processor import ScheduledTaskProcessor
        
        def dummy_callback(task_id, stage, result_data):
            pass
            
        processor = ScheduledTaskProcessor(
            scraping_interval=60,
            ai_interval=60, 
            status_update_interval=120,
            max_scraping_tasks=1,
            max_ai_tasks=1,
            completion_callback=dummy_callback
        )
        print("√ 定时任务处理器创建成功")
        return True
    except Exception as e:
        print(f"× 定时任务处理器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_modules():
    """测试应用程序模块"""
    print("5. 测试应用程序模块...")
    
    try:
        from shipment_manager import ShipmentManager
        from task_manager import TaskManager
        print("√ 应用程序核心模块导入成功")
        return True
    except Exception as e:
        print(f"× 应用程序模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("快速启动诊断")
    print("=" * 40)
    
    tests = [
        test_basic_imports,
        test_ai_modules,
        test_executors,
        test_scheduled_processor,
        test_app_modules,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print("测试结果: {}/{} 通过".format(passed, total))
    
    if passed == total:
        print("所有测试通过！可以尝试启动应用程序：")
        print("   py app.py")
    else:
        print("存在问题，请检查错误信息并修复")

if __name__ == "__main__":
    main()