#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件管理器修复后的排序功能
"""

import os
from utils.file_manager import get_file_manager

def test_file_manager_sorting():
    """测试文件管理器的排序功能"""
    file_mgr = get_file_manager()
    
    # 测试箱号
    tracking_number = "MEDUJ0618622"
    
    print(f"🔍 获取箱号 {tracking_number} 的所有文件记录...")
    
    # 获取文件记录
    files_data = file_mgr.get_files_for_tracking(tracking_number)
    
    if not files_data:
        print("❌ 没有找到任何文件记录")
        return
    
    print(f"📁 找到 {len(files_data)} 个文件夹")
    print("\n📋 文件夹列表（按时间排序）：")
    
    for i, record in enumerate(files_data):
        folder_name = record['folder_name']
        files_count = len(record['files'])
        
        # 检查是否包含截图文件
        has_screenshot = any(f['name'] == 'final_result.png' for f in record['files'])
        screenshot_status = "✅ 有截图" if has_screenshot else "❌ 缺少截图"
        
        # 检查是否包含AI分析结果
        has_analysis = any(f['name'].startswith('ai_analysis_result_') for f in record['files'])
        analysis_status = "✅ 有分析" if has_analysis else "❌ 缺少分析"
        
        print(f"  {i+1}. {folder_name}")
        print(f"     📄 文件数: {files_count}")
        print(f"     📸 {screenshot_status}")
        print(f"     🤖 {analysis_status}")
        
        if i == 0:
            print(f"     ⭐ 这是最新的文件夹")
        print()
    
    # 检查最新文件夹的完整性
    latest_record = files_data[0]
    latest_folder = latest_record['folder_name']
    has_screenshot = any(f['name'] == 'final_result.png' for f in latest_record['files'])
    has_analysis = any(f['name'].startswith('ai_analysis_result_') for f in latest_record['files'])
    
    print("\n🎯 最新文件夹状态检查：")
    print(f"📁 文件夹: {latest_folder}")
    print(f"📸 截图文件: {'✅ 存在' if has_screenshot else '❌ 缺失'}")
    print(f"🤖 分析文件: {'✅ 存在' if has_analysis else '❌ 缺失'}")
    
    if has_screenshot and has_analysis:
        print("\n🎉 最新文件夹数据完整！")
    else:
        print("\n⚠️ 最新文件夹数据不完整")
        
        # 查找最新的完整文件夹
        for record in files_data:
            folder_has_screenshot = any(f['name'] == 'final_result.png' for f in record['files'])
            folder_has_analysis = any(f['name'].startswith('ai_analysis_result_') for f in record['files'])
            
            if folder_has_screenshot and folder_has_analysis:
                print(f"💡 最新的完整文件夹是: {record['folder_name']}")
                break
        else:
            print("❌ 没有找到完整的文件夹")

if __name__ == "__main__":
    test_file_manager_sorting()