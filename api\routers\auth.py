#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证API路由
处理用户登录、登出、用户信息等认证相关的HTTP请求
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from api.models.user_schemas import (
    LoginRequest, 
    LoginResponse, 
    UserInfo,
    UserStatsResponse,
    UserPermissionsResponse,
    UserPermission,
    APIResponse
)
from api.services.auth_service import AuthService
from api.middleware.auth import get_current_user_required, get_current_user_optional
from api.utils.logger import api_logger

router = APIRouter()

@router.post("/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """
    用户登录
    
    - **invite_code**: 邀请码
    """
    try:
        api_logger.info(f"用户登录请求: {request.invite_code}")
        
        # 验证邀请码
        user_data = AuthService.verify_invite_code(request.invite_code)
        if not user_data:
            api_logger.warning(f"无效的邀请码: {request.invite_code}")
            return LoginResponse(
                success=False,
                message="无效的邀请码",
                access_token=None,
                user_info=None,
                expires_in=None
            )
        
        # 检查用户状态
        if not AuthService.is_active_user(user_data["user_id"]):
            api_logger.warning(f"用户账户已被禁用: {user_data['user_id']}")
            return LoginResponse(
                success=False,
                message="用户账户已被禁用",
                access_token=None,
                user_info=None,
                expires_in=None
            )
        
        # 创建访问令牌
        access_token = AuthService.create_access_token(user_data)
        
        # 获取用户信息
        user_info = AuthService.get_user_info(user_data["user_id"])
        
        # 更新最后登录时间
        AuthService.update_last_login(user_data["user_id"])
        
        api_logger.info(f"用户登录成功: {user_data['user_id']} - {user_data['name']}")
        
        return LoginResponse(
            success=True,
            message="登录成功",
            access_token=access_token,
            user_info=user_info,
            expires_in=60 * 24 * 7 * 60  # 7天（秒）
        )
        
    except Exception as e:
        api_logger.error(f"用户登录异常: {str(e)}")
        return LoginResponse(
            success=False,
            message="登录服务异常",
            access_token=None,
            user_info=None,
            expires_in=None
        )

@router.post("/auth/logout", response_model=APIResponse)
async def logout(current_user: UserInfo = Depends(get_current_user_required)):
    """
    用户登出
    
    需要认证令牌
    """
    try:
        api_logger.info(f"用户登出: {current_user.user_id} - {current_user.name}")
        
        # 在实际实现中，这里可能需要将令牌加入黑名单
        # 目前只是简单的响应成功
        
        return APIResponse(
            success=True,
            message="登出成功",
            data={"user_id": current_user.user_id}
        )
        
    except Exception as e:
        api_logger.error(f"用户登出异常: {str(e)}")
        raise HTTPException(status_code=500, detail="登出服务异常")

@router.get("/auth/profile", response_model=UserInfo)
async def get_profile(current_user: UserInfo = Depends(get_current_user_required)):
    """
    获取用户资料
    
    需要认证令牌
    """
    try:
        api_logger.info(f"获取用户资料: {current_user.user_id}")
        
        # 获取最新的用户信息
        user_info = AuthService.get_user_info(current_user.user_id)
        if not user_info:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return user_info
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取用户资料异常: {str(e)}")
        raise HTTPException(status_code=500, detail="获取用户资料异常")

@router.get("/auth/stats", response_model=UserStatsResponse)
async def get_user_stats(current_user: UserInfo = Depends(get_current_user_required)):
    """
    获取用户统计信息
    
    需要认证令牌
    """
    try:
        api_logger.info(f"获取用户统计: {current_user.user_id}")
        
        # TODO: 在实际实现中，这些统计数据应该从数据库查询
        # 目前使用模拟数据
        
        stats = UserStatsResponse(
            total_queries=current_user.query_count,
            successful_queries=int(current_user.query_count * current_user.success_rate / 100),
            failed_queries=int(current_user.query_count * (100 - current_user.success_rate) / 100),
            success_rate=current_user.success_rate,
            today_queries=5,  # 模拟今日查询数
            this_week_queries=25,  # 模拟本周查询数
            this_month_queries=current_user.query_count  # 模拟本月查询数
        )
        
        return stats
        
    except Exception as e:
        api_logger.error(f"获取用户统计异常: {str(e)}")
        raise HTTPException(status_code=500, detail="获取用户统计异常")

@router.get("/auth/permissions", response_model=UserPermissionsResponse)
async def get_user_permissions(current_user: UserInfo = Depends(get_current_user_required)):
    """
    获取用户权限列表
    
    需要认证令牌
    """
    try:
        api_logger.info(f"获取用户权限: {current_user.user_id}")
        
        # 定义权限描述
        permission_descriptions = {
            "*": "超级管理员权限",
            "query:create": "创建查询任务",
            "query:view": "查看查询结果", 
            "query:export": "导出查询数据",
            "batch:create": "批量创建任务",
            "priority:high": "高优先级任务",
            "admin:manage": "管理员功能"
        }
        
        permissions = []
        user_permissions = AuthService.get_user_permissions(current_user.user_id)
        
        for perm in user_permissions:
            description = permission_descriptions.get(perm, f"权限: {perm}")
            permissions.append(UserPermission(
                permission=perm,
                description=description,
                granted=True
            ))
        
        return UserPermissionsResponse(
            user_id=current_user.user_id,
            permissions=permissions
        )
        
    except Exception as e:
        api_logger.error(f"获取用户权限异常: {str(e)}")
        raise HTTPException(status_code=500, detail="获取用户权限异常")

@router.get("/auth/check")
async def check_auth_status(current_user: UserInfo = Depends(get_current_user_optional)):
    """
    检查认证状态（不强制要求认证）
    """
    if current_user:
        return {
            "authenticated": True,
            "user_id": current_user.user_id,
            "name": current_user.name,
            "role": current_user.role.value
        }
    else:
        return {
            "authenticated": False,
            "user_id": None,
            "name": None,
            "role": None
        }