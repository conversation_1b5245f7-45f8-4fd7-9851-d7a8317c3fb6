#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证服务
基于邀请码系统的用户管理
"""

import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from jose import JWTError, jwt
from passlib.context import CryptContext
from api.models.user_schemas import UserInfo, UserRole, UserStatus

# JWT配置
SECRET_KEY = "container-helper-jwt-secret-key-2025"  # 生产环境应该使用环境变量
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7天

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 邀请码数据库（模拟前端的邀请码系统）
# 在实际生产环境中，这应该存储在数据库中
INVITE_CODES_DB = {
    'LL2025001': {
        'user_id': '001',
        'name': '李乐', 
        'role': UserRole.SENIOR,
        'avatar': 'L',
        'status': UserStatus.ACTIVE,
        'permissions': ['query:create', 'query:view', 'query:export', 'batch:create'],
        'created_at': '2025-01-01T00:00:00Z',
        'query_count': 128,
        'success_rate': 95.5
    },
    'ADMIN001': {
        'user_id': 'ADMIN',
        'name': '系统管理员',
        'role': UserRole.ADMIN,
        'avatar': 'A',
        'status': UserStatus.ACTIVE,
        'permissions': ['*'],  # 超级管理员拥有所有权限
        'created_at': '2025-01-01T00:00:00Z',
        'query_count': 500,
        'success_rate': 99.2
    },
    'USER002': {
        'user_id': '002',
        'name': '张三',
        'role': UserRole.NORMAL,
        'avatar': 'Z',
        'status': UserStatus.ACTIVE,
        'permissions': ['query:create', 'query:view'],
        'created_at': '2025-01-15T00:00:00Z',
        'query_count': 25,
        'success_rate': 88.0
    },
    'VIP003': {
        'user_id': '003',
        'name': '王五',
        'role': UserRole.VIP,
        'avatar': 'W',
        'status': UserStatus.ACTIVE,
        'permissions': ['query:create', 'query:view', 'query:export', 'batch:create', 'priority:high'],
        'created_at': '2025-01-10T00:00:00Z',
        'query_count': 75,
        'success_rate': 92.0
    },
    # 测试邀请码
    'TEST001': {
        'user_id': 'TEST001',
        'name': '测试用户',
        'role': UserRole.NORMAL,
        'avatar': 'T',
        'status': UserStatus.ACTIVE,
        'permissions': ['query:create', 'query:view', 'batch:create'],  # 添加batch:create权限
        'created_at': '2025-01-20T00:00:00Z',
        'query_count': 5,
        'success_rate': 80.0
    }
}

class AuthService:
    """认证服务类"""
    
    @staticmethod
    def verify_invite_code(invite_code: str) -> Optional[Dict[str, Any]]:
        """
        验证邀请码
        
        Args:
            invite_code: 邀请码
            
        Returns:
            用户信息字典，如果邀请码无效则返回None
        """
        return INVITE_CODES_DB.get(invite_code.strip().upper())
    
    @staticmethod
    def create_access_token(user_data: Dict[str, Any]) -> str:
        """
        创建访问令牌
        
        Args:
            user_data: 用户数据
            
        Returns:
            JWT访问令牌
        """
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode = {
            "user_id": user_data["user_id"],
            "name": user_data["name"],
            "role": user_data["role"].value if isinstance(user_data["role"], UserRole) else user_data["role"],
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access_token"
        }
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_access_token(token: str) -> Optional[Dict[str, Any]]:
        """
        验证访问令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            解析后的令牌数据，如果无效则返回None
        """
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id: str = payload.get("user_id")
            if user_id is None:
                return None
            return payload
        except JWTError:
            return None
    
    @staticmethod
    def get_user_info(user_id: str) -> Optional[UserInfo]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息对象
        """
        # 从邀请码数据库中查找用户
        for invite_code, user_data in INVITE_CODES_DB.items():
            if user_data["user_id"] == user_id:
                return UserInfo(
                    user_id=user_data["user_id"],
                    name=user_data["name"],
                    role=user_data["role"],
                    avatar=user_data["avatar"],
                    status=user_data["status"],
                    permissions=user_data["permissions"],
                    created_at=datetime.fromisoformat(user_data["created_at"].replace('Z', '+00:00')),
                    last_login=datetime.utcnow(),  # 模拟最后登录时间
                    query_count=user_data["query_count"],
                    success_rate=user_data["success_rate"]
                )
        return None
    
    @staticmethod
    def update_last_login(user_id: str):
        """
        更新用户最后登录时间
        
        Args:
            user_id: 用户ID
        """
        # 在实际实现中，这里应该更新数据库
        # 目前只是模拟操作
        pass
    
    @staticmethod
    def check_permission(user_id: str, permission: str) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            permission: 权限名称
            
        Returns:
            是否有权限
        """
        user_info = AuthService.get_user_info(user_id)
        if not user_info:
            return False
        
        # 超级管理员拥有所有权限
        if '*' in user_info.permissions:
            return True
        
        # 检查具体权限
        return permission in user_info.permissions
    
    @staticmethod
    def get_user_permissions(user_id: str) -> List[str]:
        """
        获取用户权限列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            权限列表
        """
        user_info = AuthService.get_user_info(user_id)
        if not user_info:
            return []
        
        return user_info.permissions
    
    @staticmethod
    def is_active_user(user_id: str) -> bool:
        """
        检查用户是否激活
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否激活
        """
        user_info = AuthService.get_user_info(user_id)
        if not user_info:
            return False
        
        return user_info.status == UserStatus.ACTIVE

def get_current_user(token: str) -> Optional[UserInfo]:
    """
    从JWT令牌获取当前用户信息
    
    Args:
        token: JWT令牌
        
    Returns:
        用户信息，如果令牌无效则返回None
    """
    payload = AuthService.verify_access_token(token)
    if payload is None:
        return None
    
    user_id = payload.get("user_id")
    if user_id is None:
        return None
    
    return AuthService.get_user_info(user_id)

def require_permission(permission: str):
    """
    权限装饰器
    
    Args:
        permission: 需要的权限
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 这里应该从请求中获取当前用户信息
            # 在FastAPI依赖注入中实现
            pass
        return wrapper
    return decorator