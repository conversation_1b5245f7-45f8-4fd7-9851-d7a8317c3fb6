#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据流和存储问题
"""

import os
import sys
import sqlite3
from datetime import datetime

# 只导入需要的模块，避免playwright依赖
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from task_manager import TaskManager
from shipment_manager import ShipmentManager

def check_database_connectivity():
    """检查数据库连接性"""
    print("=== 数据库连接性检查 ===")
    
    # 检查货运记录数据库
    shipment_db_path = 'db/shipment_records.db'
    task_db_path = 'db/task_queue.db'
    
    print(f"货运记录数据库: {os.path.exists(shipment_db_path)} ({shipment_db_path})")
    print(f"任务队列数据库: {os.path.exists(task_db_path)} ({task_db_path})")
    
    if os.path.exists(shipment_db_path):
        try:
            conn = sqlite3.connect(shipment_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM shipment_records")
            count = cursor.fetchone()[0]
            print(f"货运记录表中有 {count} 条记录")
            
            cursor.execute("SELECT COUNT(*) FROM shipment_dates")
            dates_count = cursor.fetchone()[0]
            print(f"时间节点表中有 {dates_count} 条记录")
            conn.close()
        except Exception as e:
            print(f"货运记录数据库连接失败: {e}")
    
    if os.path.exists(task_db_path):
        try:
            conn = sqlite3.connect(task_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM task_queue")
            count = cursor.fetchone()[0]
            print(f"任务队列表中有 {count} 条记录")
            conn.close()
        except Exception as e:
            print(f"任务队列数据库连接失败: {e}")

def test_shipment_creation():
    """测试货运记录创建"""
    print("\n=== 测试货运记录创建 ===")
    
    try:
        manager = ShipmentManager()
        
        # 创建测试记录
        record_id = manager.create_shipment_record(
            bill_of_lading="TEST123456",
            carrier_company="MSC",
            created_by="debug_test"
        )
        
        print(f"成功创建货运记录，ID: {record_id}")
        
        # 验证记录是否创建成功
        record = manager.get_shipment_record(record_id)
        if record:
            print(f"记录验证成功: {record['bill_of_lading']}")
        else:
            print("记录验证失败：未找到刚创建的记录")
            
        return record_id
        
    except Exception as e:
        print(f"货运记录创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_task_creation():
    """测试任务创建"""
    print("\n=== 测试任务创建 ===")
    
    try:
        manager = TaskManager()
        
        # 创建测试任务
        task_id = manager.create_task(
            tracking_number="TEST123456",
            task_type="bill_of_lading",
            creator_id="debug_test",
            creator_name="调试测试",
            carrier="MSC",
            remarks="调试测试任务"
        )
        
        print(f"成功创建任务，ID: {task_id}")
        
        # 验证任务是否创建成功
        task = manager.get_task_by_id(task_id)
        if task:
            print(f"任务验证成功: {task['task_name']}")
        else:
            print("任务验证失败：未找到刚创建的任务")
            
        return task_id
        
    except Exception as e:
        print(f"任务创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_data_storage_flow():
    """测试完整的数据存储流程"""
    print("\n=== 测试完整数据存储流程 ===")
    
    try:
        # 1. 创建ShipmentManager实例
        shipment_manager = ShipmentManager()
        
        # 2. 创建货运记录
        record_id = shipment_manager.create_shipment_record(
            bill_of_lading="FLOW_TEST_001",
            carrier_company="MSC",
            created_by="flow_test"
        )
        print(f"步骤1: 创建货运记录成功，ID: {record_id}")
        
        # 3. 模拟任务处理结果数据
        mock_result_data = {
            'estimated_arrival_time': '2025-02-15',
            'estimated_arrival_port': 'Los Angeles CA',
            'dates_data': [
                {
                    'date': '2025-01-15',
                    'original_format': '15/01/2025',
                    'event_type': 'ETD',
                    'location': 'Shanghai CN',
                    'event': 'Estimated Time of Departure',
                    'status': 'estimated',
                    'vessel_info': 'MSC VESSEL 001',
                    'context': '从上海港预计离港时间'
                },
                {
                    'date': '2025-02-15',
                    'original_format': '15/02/2025',
                    'event_type': 'ETA',
                    'location': 'Los Angeles CA',
                    'event': 'Estimated Time of Arrival',
                    'status': 'estimated',
                    'vessel_info': 'MSC VESSEL 001',
                    'context': '预计到达洛杉矶港时间'
                }
            ]
        }
        
        # 4. 更新预计到港时间
        success = shipment_manager._update_estimated_arrival_time(record_id, mock_result_data['estimated_arrival_time'])
        print(f"步骤2: 更新预计到港时间{'成功' if success else '失败'}")
        
        # 5. 添加时间节点数据
        success = shipment_manager.add_shipment_dates(record_id, mock_result_data['dates_data'])
        print(f"步骤3: 添加时间节点数据{'成功' if success else '失败'}")
        
        # 6. 验证数据完整性
        record = shipment_manager.get_shipment_record(record_id)
        if record:
            print(f"步骤4: 数据验证成功")
            print(f"  - 提单号: {record['bill_of_lading']}")
            print(f"  - 预计到港时间: {record['estimated_arrival_time']}")
            print(f"  - 时间节点数量: {len(record['dates'])}")
            
            for i, date_info in enumerate(record['dates']):
                print(f"    节点{i+1}: {date_info['date']} - {date_info['description']} @ {date_info['location']}")
        else:
            print("步骤4: 数据验证失败")
            
        return record_id
        
    except Exception as e:
        print(f"数据存储流程测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_task_completion_callback():
    """测试任务完成回调机制"""
    print("\n=== 测试任务完成回调机制 ===")
    
    try:
        # 创建一个带回调的ShipmentManager
        shipment_manager = ShipmentManager()
        
        # 创建货运记录
        record_id = shipment_manager.create_shipment_record(
            bill_of_lading="CALLBACK_TEST_001",
            carrier_company="MSC",
            created_by="callback_test"
        )
        print(f"创建货运记录，ID: {record_id}")
        
        # 获取对应的任务ID（从备注中提取）
        record = shipment_manager.get_shipment_record(record_id)
        if record and record.get('remarks'):
            import re
            match = re.search(r'任务ID: ([a-f0-9-]+)', record['remarks'])
            if match:
                task_id = match.group(1)
                print(f"找到关联任务ID: {task_id}")
                
                # 模拟任务完成数据
                mock_result_data = {
                    'estimated_arrival_time': '2025-03-01',
                    'dates_data': [
                        {
                            'date': '2025-02-01',
                            'event': 'Test Loading',
                            'location': 'Test Port',
                            'event_type': 'loading'
                        }
                    ]
                }
                
                # 调用任务完成处理
                success = shipment_manager.handle_task_completion(task_id, mock_result_data)
                print(f"任务完成处理{'成功' if success else '失败'}")
                
                # 验证结果
                updated_record = shipment_manager.get_shipment_record(record_id)
                if updated_record:
                    print(f"验证更新结果:")
                    print(f"  - 预计到港时间: {updated_record['estimated_arrival_time']}")
                    print(f"  - 时间节点数量: {len(updated_record['dates'])}")
            else:
                print("未找到任务ID")
        else:
            print("记录或备注为空")
            
    except Exception as e:
        print(f"任务完成回调测试失败: {e}")
        import traceback
        print(traceback.format_exc())

def main():
    """主函数"""
    print("开始数据流调试...")
    
    # 检查数据库连接
    check_database_connectivity()
    
    # 测试货运记录创建
    record_id = test_shipment_creation()
    
    # 测试任务创建
    task_id = test_task_creation()
    
    # 测试完整数据存储流程
    flow_record_id = test_data_storage_flow()
    
    # 测试任务完成回调
    test_task_completion_callback()
    
    print("\n调试完成！")

if __name__ == "__main__":
    main()