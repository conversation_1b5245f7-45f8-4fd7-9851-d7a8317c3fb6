#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试货运记录状态更新修复
验证AI分析完成后是否能正确更新最新货运记录的状态
"""

import sqlite3
import os
from datetime import datetime

def test_status_update_fix():
    """测试状态更新修复"""
    
    if not os.path.exists('db/shipment_records.db'):
        print("❌ 货运记录数据库不存在")
        return
        
    if not os.path.exists('db/task_queue.db'):
        print("❌ 任务队列数据库不存在")
        return
    
    print("🔧 测试状态更新修复逻辑...")
    print("=" * 60)
    
    # 连接两个数据库
    shipment_conn = sqlite3.connect('db/shipment_records.db')
    shipment_conn.row_factory = sqlite3.Row
    task_conn = sqlite3.connect('db/task_queue.db')
    task_conn.row_factory = sqlite3.Row
    
    shipment_cursor = shipment_conn.cursor()
    task_cursor = task_conn.cursor()
    
    # 查找同一跟踪号的多个货运记录
    shipment_cursor.execute("""
        SELECT bill_of_lading, container_number, COUNT(*) as record_count
        FROM shipment_records 
        GROUP BY bill_of_lading, container_number
        HAVING COUNT(*) > 1
        ORDER BY record_count DESC
        LIMIT 3
    """)
    
    duplicates = shipment_cursor.fetchall()
    
    if not duplicates:
        print("ℹ️  没有找到重复的跟踪号，创建测试用例...")
        # 这里可以创建测试用例，但先检查现有数据
    
    print(f"📋 发现 {len(duplicates)} 个有重复记录的跟踪号:")
    
    for duplicate in duplicates:
        tracking_number = duplicate['bill_of_lading'] or duplicate['container_number']
        count = duplicate['record_count']
        
        print(f"\n🔍 跟踪号: {tracking_number} ({count} 条记录)")
        
        # 查看该跟踪号的所有货运记录
        shipment_cursor.execute("""
            SELECT id, status, created_at, updated_at, remarks
            FROM shipment_records 
            WHERE bill_of_lading = ? OR container_number = ?
            ORDER BY created_at DESC
        """, (tracking_number, tracking_number))
        
        records = shipment_cursor.fetchall()
        
        print("   货运记录:")
        for i, record in enumerate(records, 1):
            status_indicator = "🆕" if i == 1 else "📜"
            print(f"     {status_indicator} ID: {record['id']}, 状态: {record['status']}, 创建: {record['created_at']}")
        
        # 查看该跟踪号的AI分析任务
        task_cursor.execute("""
            SELECT id, status, remarks, completed_at, created_at
            FROM task_queue 
            WHERE tracking_number = ? AND task_stage = 'ai_analysis'
            ORDER BY created_at DESC
            LIMIT 3
        """, (tracking_number,))
        
        ai_tasks = task_cursor.fetchall()
        
        print("   AI分析任务:")
        for task in ai_tasks:
            # 从备注中提取关联的货运记录ID
            linked_record_id = "未知"
            if task['remarks'] and '货运记录ID:' in task['remarks']:
                try:
                    linked_record_id = task['remarks'].split('货运记录ID:')[1].split(',')[0].strip()
                except:
                    linked_record_id = "解析失败"
            
            latest_record_id = records[0]['id'] if records else "无"
            
            if task['status'] == 'completed':
                if linked_record_id == str(latest_record_id):
                    link_status = "✅ 正确关联"
                else:
                    link_status = f"❌ 关联错误 (应为:{latest_record_id})"
            else:
                link_status = f"⏳ 任务未完成"
            
            print(f"     🤖 任务: {task['id'][:8]}, 状态: {task['status']}, 关联记录: {linked_record_id}, {link_status}")
        
        # 模拟修复后的查找逻辑
        print("   🔧 修复后的查找逻辑测试:")
        shipment_cursor.execute('''
            SELECT id FROM shipment_records 
            WHERE (bill_of_lading = ? OR container_number = ?)
            AND status != '已完成'
            ORDER BY created_at DESC 
            LIMIT 1
        ''', (tracking_number, tracking_number))
        
        latest_unfinished = shipment_cursor.fetchone()
        if latest_unfinished:
            print(f"     ✅ 应该更新的货运记录ID: {latest_unfinished['id']}")
        else:
            print(f"     ⚠️  未找到未完成的货运记录")
    
    shipment_conn.close()
    task_conn.close()
    print("\n" + "=" * 60)
    print("🎯 修复说明:")
    print("   - 修改了 sync_status_with_task 方法")
    print("   - 修改了 handle_task_completion 方法")
    print("   - AI分析任务完成时，会查找最新的未完成货运记录")
    print("   - 避免状态更新到错误的历史记录")

if __name__ == "__main__":
    test_status_update_fix()