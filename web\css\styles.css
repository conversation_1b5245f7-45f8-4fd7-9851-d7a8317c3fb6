/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量定义 (New Dark Theme) */
:root {
    --bg-primary: #121212; /* Overall background */
    --bg-secondary: #1E1E1E; /* Sidebar and Panel background */
    --bg-content: #121212; /* Main content area background */
    --color-primary: #3b82f6; /* Accent blue */
    --color-text: #E0E0E0; /* Primary text */
    --color-text-secondary: #8A8A8A; /* Secondary text */
    --color-border: #333333; /* Borders */
    --sidebar-width: 240px;
    --header-height: 60px;
    --border-radius: 6px;
    --shadow: none; /* Removed shadows for a flatter look */
    --transition: all 0.2s ease-in-out;
}

/* 基础字体设置 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--bg-primary);
    color: var(--color-text);
    line-height: 1.6;
    font-size: 14px;
}

/* 主应用容器 */
#app {
    display: flex;
    min-height: 100vh;
}

/* 侧边导航栏 */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--color-border);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--transition);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--color-border);
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text);
}

.logo i {
    width: 24px;
    height: 24px;
    color: var(--color-primary);
}

/* 导航菜单 */
.nav-menu {
    list-style: none;
    padding: 20px 16px;
}

.nav-item {
    margin-bottom: 8px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 12px;
    position: relative;
    font-weight: 500;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.08);
    color: var(--color-text);
}

.nav-link:hover .nav-icon {
    background-color: rgba(59, 130, 246, 0.2);
    color: var(--color-primary);
}

.nav-item.active .nav-link {
    background-color: rgba(59, 130, 246, 0.15);
    color: var(--color-primary);
}

.nav-item.active .nav-link .nav-icon {
    background-color: var(--color-primary);
    color: white;
}

.nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.05);
    transition: var(--transition);
}

.nav-link i {
    width: 18px;
    height: 18px;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    background-color: var(--bg-content);
    min-height: 100vh;
    color: var(--color-text); /* Changed default text color */
}

#page-content {
    padding: 30px;
    /* max-width: 1200px; Removed for full-width layout */
    /* margin: 0 auto; Removed for full-width layout */
}

/* 页面标题 */
.page-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title i {
    width: 28px;
    height: 28px;
    color: var(--color-primary);
}

/* 卡片样式 */
.card {
    background: var(--bg-secondary); /* Darker card background */
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid var(--color-border);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--color-border);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-title i {
    width: 18px;
    height: 18px;
    color: var(--color-primary);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    line-height: 1;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #1d6fe6;
    transform: translateY(-1px);
    box-shadow: none;
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--color-text);
    border: 1px solid var(--color-border);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: #555;
}

.btn-large {
    padding: 12px 28px;
    font-size: 16px;
    font-weight: 600;
}

.btn i {
    width: 16px;
    height: 16px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--color-text-secondary);
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--color-border);
    background-color: var(--bg-primary);
    color: var(--color-text);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 网格布局 */
.grid {
    display: grid;
    gap: 24px;
}

.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(18, 18, 18, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    #page-content {
        padding: 20px;
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}

/* 页面头部样式 */
.page-header {
    margin-bottom: 24px;
}

.page-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 搜索区域样式 */
.search-section {
    background: var(--bg-secondary);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.search-input-group label {
    display: block;
    margin-bottom: 12px;
    color: var(--color-text-secondary);
    font-size: 14px;
}

.search-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-controls .form-control {
    flex: 1;
    max-width: 400px;
}

/* 任务表格样式 */
.task-table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    border: 1px solid var(--color-border);
}

.task-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
}

.task-table th {
    background: var(--bg-primary);
    color: var(--color-text-secondary);
    font-weight: 500;
    font-size: 13px;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--color-border);
    white-space: nowrap;
}

.task-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text);
    font-size: 14px;
}

.task-row {
    cursor: pointer;
    transition: var(--transition);
}

.task-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

.task-row:last-child td {
    border-bottom: none;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-completed {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-processing {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.status-pending {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
}

.status-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* 表格单元格特殊样式 */
.task-id {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--color-primary);
}

.vessel-info {
    line-height: 1.4;
}

.vessel-name {
    font-weight: 500;
    color: var(--color-text);
}

.voyage {
    font-size: 12px;
    color: var(--color-text-secondary);
    margin-top: 2px;
}

.port {
    color: var(--color-text);
}

.eta {
    font-weight: 500;
    color: var(--color-text);
}

.update-time {
    color: var(--color-text-secondary);
    font-size: 13px;
}

/* 无数据状态 */
.no-data {
    text-align: center;
    padding: 40px 20px;
}

.no-data-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: var(--color-text-secondary);
}

.no-data-content i {
    width: 48px;
    height: 48px;
    opacity: 0.5;
}

.no-data-content p {
    margin: 0;
    font-size: 14px;
}

/* 任务操作按钮 */
.task-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.hidden { display: none; }
.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }