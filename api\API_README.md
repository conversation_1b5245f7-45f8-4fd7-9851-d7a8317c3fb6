# 船期查询API服务

## 概述

船期查询API服务是一个基于FastAPI的RESTful API服务，提供船期查询任务管理功能。该服务复用现有的船期查询系统，通过HTTP API和WebSocket接口为前端应用和第三方系统提供服务。

## 功能特性

### 核心功能
- 🚢 **船期查询任务管理**: 创建、查询、取消、重试船期查询任务
- 📊 **实时状态更新**: 通过WebSocket推送任务状态和进度更新
- 🏢 **船公司管理**: 获取支持的船公司列表和信息
- 📈 **统计分析**: 提供查询统计和性能指标
- 📝 **任务日志**: 详细的任务执行日志记录

### 技术特点
- ⚡ **高性能**: 基于FastAPI和异步编程
- 🔒 **请求限流**: 防止API滥用的频率限制
- 🛡️ **异常处理**: 完善的错误处理和响应
- 📋 **数据验证**: 严格的输入验证和类型检查
- 📊 **结构化日志**: 详细的日志记录和监控
- 🔄 **实时通信**: WebSocket支持实时状态推送

## 快速开始

### 1. 安装依赖

```bash
# 安装API服务依赖
pip install -r api_requirements.txt
```

### 2. 启动服务

```bash
# 方式1: 使用启动脚本
python api/start_api.py

# 方式2: 直接使用uvicorn
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 查看API文档

启动服务后，访问以下地址查看API文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

## API接口

### 船期查询相关

#### 创建船期查询任务
```http
POST /api/v1/shipment/query
Content-Type: application/json

{
  "container_number": "MEDUJ0618622",
  "carrier_code": "MSK",
  "priority": "normal",
  "callback_url": "https://example.com/callback",
  "metadata": {
    "source": "web_app",
    "user_id": "12345"
  }
}
```

#### 获取支持的船公司
```http
GET /api/v1/shipment/carriers?supported_only=true
```

#### 获取查询统计
```http
GET /api/v1/shipment/stats
```

### 任务管理相关

#### 查询任务状态
```http
GET /api/v1/task/{task_id}
```

#### 获取任务列表
```http
GET /api/v1/tasks?status=running&page=1&page_size=20
```

#### 取消任务
```http
DELETE /api/v1/task/{task_id}
Content-Type: application/json

{
  "reason": "用户取消"
}
```

#### 重试任务
```http
POST /api/v1/task/{task_id}/retry
Content-Type: application/json

{
  "reason": "网络错误重试",
  "reset_attempts": false
}
```

#### 获取任务日志
```http
GET /api/v1/task/{task_id}/logs?limit=100
```

## WebSocket实时通知

### 任务状态订阅
```javascript
// 连接到特定任务的WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/task/{task_id}');

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
    
    switch(message.type) {
        case 'task_status_update':
            // 处理任务状态更新
            break;
        case 'task_progress':
            // 处理任务进度更新
            break;
        case 'task_completed':
            // 处理任务完成
            break;
        case 'task_failed':
            // 处理任务失败
            break;
    }
};
```

### 系统消息订阅
```javascript
// 连接到系统级WebSocket
const systemWs = new WebSocket('ws://localhost:8000/ws/system');

systemWs.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('系统消息:', message);
};
```

## 配置说明

### 环境变量

```bash
# API服务配置
API_HOST=0.0.0.0          # 服务监听地址
API_PORT=8000             # 服务端口
API_RELOAD=true           # 是否启用自动重载
LOG_LEVEL=info            # 日志级别

# 数据库配置
DB_PATH=./db              # 数据库文件路径
```

### 限流配置

- **全局限流**: 每分钟1000次请求
- **客户端限流**: 每个IP每分钟100次请求
- **查询限流**: 每个IP每分钟10次查询请求

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    "field": "详细错误信息"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 常见错误代码

- `TASK_NOT_FOUND`: 任务不存在
- `INVALID_REQUEST`: 请求参数无效
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `SERVICE_UNAVAILABLE`: 服务不可用
- `VALIDATION_ERROR`: 数据验证失败

## 性能优化

### 请求优化
- 使用连接池复用HTTP连接
- 实现请求缓存减少重复查询
- 异步处理提高并发性能

### 数据库优化
- 索引优化提高查询速度
- 连接池管理数据库连接
- 定期清理过期数据

### 内存优化
- 限制WebSocket连接数量
- 定期清理过期的限流记录
- 合理设置日志轮转策略

## 监控和日志

### 日志文件
- `logs/api.log`: 主要日志文件
- `logs/api_error.log`: 错误日志文件

### 监控指标
- 请求响应时间
- 错误率统计
- 任务执行状态
- WebSocket连接数
- 内存和CPU使用率

## 开发和测试

### 运行测试
```bash
# 运行API测试脚本
python api/test_api.py
```

### 代码格式化
```bash
# 使用black格式化代码
black api/

# 使用flake8检查代码质量
flake8 api/
```

## 部署说明

### 生产环境部署

1. **使用Gunicorn部署**:
```bash
gunicorn api.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

2. **使用Docker部署**:
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY api_requirements.txt .
RUN pip install -r api_requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "api/start_api.py"]
```

3. **使用Nginx反向代理**:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 注意事项

1. **数据库依赖**: 确保所有必需的数据库文件存在
2. **端口冲突**: 确保8000端口未被占用
3. **权限问题**: 确保有足够权限创建日志文件
4. **内存使用**: 监控WebSocket连接数量避免内存泄漏
5. **安全考虑**: 生产环境中应配置适当的CORS策略

## 兼容性

- **Python版本**: 3.8+
- **操作系统**: Windows, Linux, macOS
- **数据库**: SQLite 3.x
- **浏览器**: 支持WebSocket的现代浏览器

---

如有问题或建议，请查看项目文档或联系开发团队。