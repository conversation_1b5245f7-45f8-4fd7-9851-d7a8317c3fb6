#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态与运行信息API
"""

from fastapi import APIRouter
from fastapi.responses import JSONResponse
from pathlib import Path
import json
import os
from datetime import datetime, timedelta

router = APIRouter()


@router.get("/system/processor-status")
async def get_processor_status():
    """获取后台处理器（定时任务处理器）的最新心跳/运行状态"""
    hb_path = Path("db") / "processor_heartbeat.json"
    if not hb_path.exists():
        return JSONResponse(
            {
                "running": False,
                "status": "unknown",
                "message": "未找到心跳文件，处理器可能未运行",
            }
        )

    try:
        with hb_path.open("r", encoding="utf-8") as f:
            data = json.load(f)
    except Exception as e:
        return JSONResponse(
            {
                "running": False,
                "status": "error",
                "message": f"读取心跳失败: {e}",
            }
        )

    # 判断心跳是否过期（默认90秒内视为在线）
    try:
        ts = datetime.fromisoformat(data.get("timestamp"))
        delta = datetime.now() - ts
        stale = delta > timedelta(seconds=90)
    except Exception:
        stale = True

    return {
        "running": bool(data.get("running") and not stale),
        "status": "online" if not stale and data.get("running") else "offline",
        "last_heartbeat": data.get("timestamp"),
        "active": data.get("active", {}),
        "intervals": data.get("intervals", {}),
        "stats": data.get("stats", {}),
        "raw": data,
    }
