#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务队列数据库迁移脚本 - 添加两阶段任务支持
添加 task_stage、parent_task_id、raw_data_path 字段
"""

import sqlite3
import os
from datetime import datetime

def migrate_task_stages():
    """
    迁移任务队列数据库，添加两阶段任务支持的字段
    """
    db_path = "db/task_queue.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查是否已经存在新字段
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        
        # 需要添加的字段
        new_fields = []
        
        if 'task_stage' not in existing_columns:
            new_fields.append(("task_stage", "TEXT NOT NULL DEFAULT 'scraping'", "任务阶段：scraping(网页抓取) / ai_analysis(AI分析)"))
        
        if 'parent_task_id' not in existing_columns:
            new_fields.append(("parent_task_id", "TEXT", "父任务ID（AI分析任务关联的抓取任务ID）"))
        
        if 'raw_data_path' not in existing_columns:
            new_fields.append(("raw_data_path", "TEXT", "原始数据存储路径（抓取的HTML和截图文件夹路径）"))
        
        if not new_fields:
            print("✅ 数据库已经是最新版本，无需迁移")
            return True
        
        print(f"🔄 开始迁移任务队列数据库，添加 {len(new_fields)} 个新字段...")
        
        # 添加新字段
        for field_name, field_type, description in new_fields:
            try:
                alter_sql = f"ALTER TABLE task_queue ADD COLUMN {field_name} {field_type}"
                cursor.execute(alter_sql)
                print(f"  ✅ 添加字段: {field_name} - {description}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    print(f"  ⚠️ 字段已存在: {field_name}")
                else:
                    raise e
        
        # 添加新的索引
        new_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_task_stage ON task_queue(task_stage)",
            "CREATE INDEX IF NOT EXISTS idx_parent_task_id ON task_queue(parent_task_id)",
            "CREATE INDEX IF NOT EXISTS idx_raw_data_path ON task_queue(raw_data_path)"
        ]
        
        for index_sql in new_indexes:
            cursor.execute(index_sql)
            print(f"  ✅ 创建索引: {index_sql.split('idx_')[1].split(' ')[0]}")
        
        # 提交事务
        conn.commit()
        print(f"✅ 任务队列数据库迁移成功！")
        
        # 显示更新后的表结构
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        print("\n📋 更新后的任务队列表结构:")
        for col in columns:
            marker = "🆕" if col[1] in [f[0] for f in new_fields] else "  "
            print(f"{marker} {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_migration():
    """
    验证迁移是否成功
    """
    db_path = "db/task_queue.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查新字段是否存在
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_fields = ['task_stage', 'parent_task_id', 'raw_data_path']
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"❌ 验证失败，缺少字段: {missing_fields}")
            return False
        
        print("✅ 验证成功，所有必需字段都存在")
        
        # 检查索引
        cursor.execute("PRAGMA index_list(task_queue)")
        indexes = cursor.fetchall()
        index_names = [idx[1] for idx in indexes]
        
        required_indexes = ['idx_task_stage', 'idx_parent_task_id', 'idx_raw_data_path']
        missing_indexes = [idx for idx in required_indexes if idx not in index_names]
        
        if missing_indexes:
            print(f"⚠️ 缺少索引: {missing_indexes}")
        else:
            print("✅ 所有必需索引都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 开始任务队列数据库迁移...")
    if migrate_task_stages():
        print("\n🔍 验证迁移结果...")
        verify_migration()
        print("\n✨ 任务队列数据库迁移完成！")
    else:
        print("\n❌ 迁移失败，请检查错误信息")