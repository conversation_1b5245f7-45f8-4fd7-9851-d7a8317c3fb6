#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI调用日志数据库初始化脚本
创建ai_call_logs表用于记录AI调用的详细信息
"""

import sqlite3
import os
from datetime import datetime

def create_database():
    """创建数据库和表"""
    db_path = 'ai_call_logs.db'
    
    # 如果数据库文件已存在，备份
    if os.path.exists(db_path):
        backup_path = f'ai_call_logs_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        os.rename(db_path, backup_path)
        print(f"已备份现有数据库到: {backup_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建AI调用日志表
    create_table_sql = """
    CREATE TABLE ai_call_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        model VARCHAR(100) NOT NULL,
        content_type VARCHAR(20) NOT NULL DEFAULT 'text',
        caller_id VARCHAR(50) NOT NULL,
        request_time DATETIME NOT NULL,
        response_time DATETIME NOT NULL,
        duration_seconds DECIMAL(10,3) NOT NULL,
        prompt_tokens INTEGER NOT NULL,
        completion_tokens INTEGER NOT NULL,
        total_tokens INTEGER NOT NULL,
        business_id VARCHAR(100),
        business_type VARCHAR(50),
        request_content_length INTEGER,
        response_content_length INTEGER,
        status VARCHAR(20) NOT NULL DEFAULT 'success',
        error_message TEXT,
        cost_amount DECIMAL(10,4),
        currency VARCHAR(10) DEFAULT 'USD',
        remarks TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """
    
    cursor.execute(create_table_sql)
    
    # 创建索引
    indexes = [
        "CREATE INDEX idx_caller_request_time ON ai_call_logs(caller_id, request_time)",
        "CREATE INDEX idx_business_id ON ai_call_logs(business_id)",
        "CREATE INDEX idx_model_request_time ON ai_call_logs(model, request_time)",
        "CREATE INDEX idx_request_time ON ai_call_logs(request_time)",
        "CREATE INDEX idx_status ON ai_call_logs(status)",
        "CREATE INDEX idx_content_type ON ai_call_logs(content_type)"
    ]
    
    for index_sql in indexes:
        cursor.execute(index_sql)
    
    # 创建触发器自动更新updated_at字段
    trigger_sql = """
    CREATE TRIGGER update_ai_call_logs_updated_at 
    AFTER UPDATE ON ai_call_logs
    FOR EACH ROW
    BEGIN
        UPDATE ai_call_logs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
    """
    
    cursor.execute(trigger_sql)
    
    conn.commit()
    conn.close()
    
    print(f"数据库创建成功: {db_path}")
    print("表结构:")
    print("- ai_call_logs: AI调用日志表")
    print("- 已创建6个索引用于优化查询性能")
    print("- 已创建触发器自动更新updated_at字段")

def test_database():
    """测试数据库连接和表结构"""
    conn = sqlite3.connect('ai_call_logs.db')
    cursor = conn.cursor()
    
    # 查看表结构
    cursor.execute("PRAGMA table_info(ai_call_logs)")
    columns = cursor.fetchall()
    
    print("\n表字段信息:")
    for col in columns:
        print(f"- {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT: ' + str(col[4]) if col[4] else ''}")
    
    # 查看索引
    cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='ai_call_logs'")
    indexes = cursor.fetchall()
    
    print("\n索引信息:")
    for idx in indexes:
        print(f"- {idx[0]}")
    
    conn.close()

if __name__ == "__main__":
    print("正在创建AI调用日志数据库...")
    create_database()
    test_database()
    print("\n数据库初始化完成！")